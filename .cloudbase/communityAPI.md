# 社区功能 API 文档

## 目录
1. [概述](#概述)
2. [数据模型](#数据模型)
3. [API 接口](#api-接口)
4. [数据流程](#数据流程)
5. [错误处理](#错误处理)

## 概述

社区功能为用户提供了一个互动平台，允许用户分享训练计划、发布帖子、评论互动、关注其他用户等。主要功能包括：

- 每日训练分享
- 帖子发布与管理
- 评论系统
- 点赞功能
- 用户关注系统
- 通知系统
- 图片管理

## 数据模型

### 1. 每日训练 (DailyWorkout)
```python
class DailyWorkout(Base):
    __tablename__ = "daily_workouts"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    title = Column(String(100), nullable=False)
    content = Column(String(1000), nullable=False)
    status = Column(Enum(DailyWorkoutStatus), default=DailyWorkoutStatus.ACTIVE)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
```

### 2. 帖子 (Post)
```python
class Post(Base):
    __tablename__ = "posts"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    title = Column(String(100), nullable=False)
    content = Column(String(5000), nullable=False)
    related_workout_id = Column(Integer, ForeignKey("daily_workouts.id"), nullable=True)
    like_count = Column(Integer, default=0)
    comment_count = Column(Integer, default=0)
    share_count = Column(Integer, default=0)
    status = Column(Enum(PostStatus), default=PostStatus.ACTIVE)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
```

### 3. 评论 (Comment)
```python
class Comment(Base):
    __tablename__ = "comments"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    post_id = Column(Integer, ForeignKey("posts.id"), nullable=False)
    parent_id = Column(Integer, ForeignKey("comments.id"), nullable=True)
    content = Column(String(1000), nullable=False)
    like_count = Column(Integer, default=0)
    status = Column(Enum(CommentStatus), default=CommentStatus.ACTIVE)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
```

### 4. 通知 (Notification)
```python
class Notification(Base):
    __tablename__ = "notifications"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    type = Column(Enum(NotificationType), nullable=False)
    content = Column(String(500), nullable=False)
    is_read = Column(Boolean, default=False)
    created_at = Column(DateTime, default=datetime.utcnow)
```

### 5. 用户关系 (UserRelation)
```python
class UserRelation(Base):
    __tablename__ = "user_relations"
    
    id = Column(Integer, primary_key=True, index=True)
    follower_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    following_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    created_at = Column(DateTime, default=datetime.utcnow)
```

### 6. 图片 (Image)
```python
class Image(Base):
    __tablename__ = "images"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    post_id = Column(Integer, ForeignKey("posts.id"), nullable=True)
    url = Column(String(500), nullable=False)
    title = Column(String(100), nullable=True)
    description = Column(String(500), nullable=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
```

## API 接口

### 1. 每日训练接口

#### 创建每日训练
```http
POST /api/v1/community/daily-workouts/
Content-Type: application/json

{
    "title": "今日训练计划",
    "content": "训练内容...",
    "exercises": [
        {
            "exercise_id": 1,
            "sets": 3,
            "reps": "12-15",
            "rest_seconds": 60
        }
    ]
}
```

#### 更新每日训练
```http
PUT /api/v1/community/daily-workouts/{workout_id}
Content-Type: application/json

{
    "title": "更新后的训练计划",
    "content": "更新后的内容..."
}
```

#### 获取每日训练列表
```http
GET /api/v1/community/daily-workouts/?skip=0&limit=20
```

#### 搜索每日训练
```http
GET /api/v1/community/daily-workouts/search/?keyword=训练&skip=0&limit=20
```

### 2. 帖子接口

#### 创建帖子
```http
POST /api/v1/community/posts/
Content-Type: application/json

{
    "title": "我的训练心得",
    "content": "帖子内容...",
    "related_workout_id": 1,
    "image_urls": ["url1", "url2"]
}
```

#### 更新帖子
```http
PUT /api/v1/community/posts/{post_id}
Content-Type: application/json

{
    "title": "更新后的标题",
    "content": "更新后的内容..."
}
```

#### 获取帖子列表
```http
GET /api/v1/community/posts/?skip=0&limit=20
```

#### 点赞帖子
```http
POST /api/v1/community/posts/{post_id}/like/
```

#### 举报帖子
```http
POST /api/v1/community/posts/{post_id}/report/
Content-Type: application/json

{
    "reason": "违规内容"
}
```

### 3. 评论接口

#### 创建评论
```http
POST /api/v1/community/posts/{post_id}/comments/
Content-Type: application/json

{
    "content": "评论内容",
    "parent_id": null
}
```

#### 更新评论
```http
PUT /api/v1/community/comments/{comment_id}
Content-Type: application/json

{
    "content": "更新后的评论内容"
}
```

#### 点赞评论
```http
POST /api/v1/community/comments/{comment_id}/like/
```

#### 举报评论
```http
POST /api/v1/community/comments/{comment_id}/report/
Content-Type: application/json

{
    "reason": "违规内容"
}
```

### 4. 通知接口

#### 获取通知列表
```http
GET /api/v1/community/notifications/?skip=0&limit=20
```

#### 标记通知为已读
```http
PATCH /api/v1/community/notifications/{notification_id}/read/
```

#### 标记所有通知为已读
```http
PATCH /api/v1/community/notifications/read-all/
```

### 5. 用户关系接口

#### 关注用户
```http
POST /api/v1/community/users/{user_id}/follow/
```

#### 取消关注
```http
DELETE /api/v1/community/users/{user_id}/follow/
```

#### 获取关注列表
```http
GET /api/v1/community/users/{user_id}/following/?skip=0&limit=20
```

#### 获取粉丝列表
```http
GET /api/v1/community/users/{user_id}/followers/?skip=0&limit=20
```

### 6. 图片接口

#### 上传图片
```http
POST /api/v1/community/images/
Content-Type: multipart/form-data

{
    "file": "图片文件",
    "title": "图片标题",
    "description": "图片描述",
    "post_id": 1
}
```

#### 更新图片信息
```http
PUT /api/v1/community/images/{image_id}
Content-Type: application/json

{
    "title": "新标题",
    "description": "新描述"
}
```

## 数据流程

### 1. 帖子发布流程
1. 用户创建帖子
2. 系统验证用户权限和内容
3. 保存帖子到数据库
4. 更新用户帖子计数
5. 发送通知给关注者

### 2. 评论流程
1. 用户发表评论
2. 系统验证评论内容
3. 保存评论到数据库
4. 更新帖子评论计数
5. 发送通知给帖子作者

### 3. 点赞流程
1. 用户点赞
2. 系统检查是否已点赞
3. 更新点赞计数
4. 发送通知给被点赞者

### 4. 关注流程
1. 用户关注其他用户
2. 系统检查是否已关注
3. 创建关注关系
4. 发送通知给被关注者

## 错误处理

### 通用错误码
- 400: 请求参数错误
- 401: 未授权
- 403: 禁止访问
- 404: 资源不存在
- 500: 服务器内部错误

### 业务错误码
- 1001: 内容违规
- 1002: 重复操作
- 1003: 资源不存在
- 1004: 权限不足
- 1005: 操作失败

### 错误响应格式
```json
{
    "code": 1001,
    "message": "内容包含违规信息",
    "detail": "具体错误详情"
}
``` 