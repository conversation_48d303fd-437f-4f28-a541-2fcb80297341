# ScienceFit 健身助手小程序

一款基于微信小程序平台的健身助手应用，提供专业的健身动作指导、肌肉示意图和训练计划，帮助用户科学健身。

## 项目特点

- 🏋️‍♀️ **专业动作库**：包含300+种健身动作的详细介绍和演示视频
- 💪 **肌肉示意图**：直观展示每个动作锻炼的主要和次要肌群
- 📱 **响应式UI**：适配各种屏幕尺寸，提供良好的用户体验
- 🔄 **下拉刷新**：支持页面内容的实时更新
- 📊 **用户中心**：记录用户的训练历史和个人资料
- 🌐 **API适配器**：优化的后端接口调用，提高性能和稳定性

## 项目结构

```
sciencefit/
├── description/backend/                  # API适配器和整体后端部分，见对应目录下说明
├── pages/                # 小程序页面
│   ├── detail/           # 动作详情页
│   ├── exe-list/         # 动作列表页
│   ├── home/             # 首页
│   └── user-center/      # 用户中心页
├── utils/                # 工具类函数
│   ├── avatar.js         # 头像处理工具
│   ├── common.js         # 通用工具函数
│   ├── config.js         # 配置文件
│   └── util.js           # 实用工具函数
├── images/               # 静态图片资源
├── app.js                # 小程序入口文件
├── app.json              # 小程序全局配置
├── app.wxss              # 小程序全局样式
└── project.config.json   # 项目配置文件
```

## 主要功能

### 动作详情页

- 展示动作视频/图片
- 提供动作详细介绍和要点说明
- 肌肉示意图直观展示锻炼部位
- 支持下拉刷新更新内容

### 动作列表页

- 分类展示各种健身动作
- 支持按器材、部位、难度等筛选
- 自动记录点击量，推荐热门动作

### 用户中心

- 用户信息展示和编辑
- 训练记录和收藏动作管理
- 个性化推荐

## 技术栈

- FASTAPI 作为后端
- ES6+ JavaScript
- 云开发数据库
- RESTful API
- 组件化开发
