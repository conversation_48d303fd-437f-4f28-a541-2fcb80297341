## Process-log
### #1
后端建立
我目前拿到了这个项目，安装要求配置完了FastAPI，并执行了uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload 完成了后端配置
另外我的数据库Database有一个在生产环境线上的，我在开发的时候不希望docker启动一个本地数据库然后做alembic的数据库迁移，而是准备直接调用线上数据库的端口，映射到本地，从而完成数据库配置。
远程服务器信息如下：
服务器: **************
用户名: dingyan
密码: sciencefit123
因此我执行了 ssh -L 5432:127.0.0.1:5432 -o ServerAliveInterval=60 -o ServerAliveCountMax=120 dingyan@**************
并且计划在本地通过psql -h 127.0.0.1 -p 5432 -U postgres -d fitness_db 来访问到数据库

<!-- 这里我做出了一个改变，之前代码里的env.py, config.py都是5433端口替换成5432，我把他们又改成了5432替换成5433 -->
### #2
小程序可以在微信开发者工具的界面进行调试，在调试过程中，我发现我之前很多函数都是用wx的cloudfunction功能，现在我可以用#1中的后端API来替代他们

我在开发的过程中，会发现一些cloudfunction没有写对应的API，所以需要编写函数，完成和数据库的交互，有时候还要在数据库中建立对应的表（有时候表已经存在了，你可以让我读取对应的schema）

我的API的定义一般建立在： description/backend/app/api/endpoints下
我的表结构的定义一般在：description/backend/app/models下
但是我的表结构如果需要查看，或者修改还是要去psql中修改

### #3
