/**
 * API模块索引
 */

// 导入各个API模块
const authApi = require('./auth');
const userApi = require('./user');
const exerciseApi = require('./exercise');
const workoutApi = require('./workout');
const foodApi = require('./food');
const foodbaseApi = require('./foodbase');
const mealApi = require('./meal');
const liveApi = require('./live');
const trainingPlanApi = require('./training-plan');
const teamTrainingApi = require('./team-training');
const chatAIApi = require('./chatAI');
const communityApi = require('./community');
const nutrientApi = require('./nutrient');
const exerciseAdapterApi = require('./exercise-adapter');
const workoutTemplatesApi = require('./workout-templates');

// 导出所有API
module.exports = {
  auth: authApi,
  user: userApi,
  exercise: exerciseApi,
  workout: workoutApi,
  food: foodApi,
  foodbase: foodbaseApi,
  meal: mealApi,
  live: liveApi,
  trainingPlan: trainingPlanApi,
  teamTraining: teamTrainingApi,
  chatAI: chatAIApi,
  community: communityApi,
  nutrient: nutrientApi,
  exerciseAdapter: exerciseAdapterApi,
  workoutTemplates: workoutTemplatesApi
}; 