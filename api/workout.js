const request = require('./request');

/**
 * Workout API module
 * Provides functions to interact with workout endpoints
 */

const BASE_URL = '/api/v1/workout';

/**
 * @typedef {Object} WorkoutStatus
 * @property {'not_started'|'in_progress'|'completed'|'cancelled'} status - Workout status
 */

/**
 * @typedef {Object} WorkoutStatistics
 * @property {number} workout_id - Workout ID
 * @property {string} status - Workout status
 * @property {number} estimated_duration - Estimated duration in minutes
 * @property {number} actual_duration - Actual duration in minutes
 * @property {number} net_duration - Net training duration excluding pauses
 * @property {number} total_pause_time - Total pause time in minutes
 * @property {number} pause_count - Number of pauses
 * @property {number} time_efficiency - Training efficiency (ratio)
 */

/**
 * @typedef {Object} WorkoutSession
 * @property {number} workout_id - Workout ID
 * @property {number} session_id - Session ID
 * @property {string} session_name - Session name
 * @property {string} status - Session status
 * @property {string} start_time - Start time
 * @property {string} end_time - End time
 */

/**
 * Update workout status
 * @param {string|number} workoutId - Workout ID
 * @param {WorkoutStatus} params - Status update parameters
 * @returns {Promise<Object>} Updated workout info
 */
function updateWorkoutStatus(workoutId, params) {
  return request.put(`${BASE_URL}/${workoutId}/status`, params);
}

/**
 * Get workout statistics
 * @param {string|number} workoutId - Workout ID
 * @returns {Promise<WorkoutStatistics>} Workout statistics
 */
function getWorkoutStatistics(workoutId) {
  return request.get(`${BASE_URL}/${workoutId}/statistics`);
}

/**
 * Link workout to team training session
 * @param {string|number} workoutId - Workout ID
 * @param {string|number} sessionId - Session ID
 * @returns {Promise<WorkoutSession>} Linked workout session
 */
function linkWorkoutToSession(workoutId, sessionId) {
  return request.post(`${BASE_URL}/${workoutId}/link-session/${sessionId}`);
}

/**
 * Start workout
 * @param {string|number} workoutId - Workout ID
 * @returns {Promise<Object>} Started workout info
 */
function startWorkout(workoutId) {
  return request.post(`${BASE_URL}/${workoutId}/start`);
}

/**
 * Complete workout
 * @param {string|number} workoutId - Workout ID
 * @param {Object} [params] - Completion parameters
 * @param {string} [params.feedback] - User feedback
 * @param {number} [params.difficulty_rating] - Difficulty rating (1-5)
 * @param {number} [params.mood_rating] - Mood rating (1-5)
 * @param {string} [params.notes] - Additional notes
 * @returns {Promise<Object>} Completed workout info
 */
function completeWorkout(workoutId, params = {}) {
  return request.post(`${BASE_URL}/${workoutId}/complete`, params);
}

/**
 * Record workout set
 * @param {string|number} workoutId - Workout ID
 * @param {string|number} exerciseId - Exercise ID
 * @param {Object} params - Set record parameters
 * @param {number} params.weight - Weight used
 * @param {number} params.reps - Repetitions completed
 * @param {number} [params.rpe] - Rate of Perceived Exertion (1-10)
 * @param {string} [params.notes] - Additional notes
 * @returns {Promise<Object>} Recorded set info
 */
function recordWorkoutSet(workoutId, exerciseId, params) {
  return request.post(`${BASE_URL}/${workoutId}/exercises/${exerciseId}/records`, params);
}

/**
 * Pause workout
 * @param {string|number} workoutId - Workout ID
 * @returns {Promise<Object>} Paused workout info
 */
function pauseWorkout(workoutId) {
  return request.post(`${BASE_URL}/${workoutId}/pause`);
}

/**
 * Resume workout
 * @param {string|number} workoutId - Workout ID
 * @returns {Promise<Object>} Resumed workout info
 */
function resumeWorkout(workoutId) {
  return request.post(`${BASE_URL}/${workoutId}/resume`);
}

/**
 * Get workout history
 * @param {Object} [params] - Query parameters
 * @param {number} [params.user_id] - Filter by user ID
 * @param {string} [params.start_date] - Filter by start date
 * @param {string} [params.end_date] - Filter by end date
 * @param {number} [params.skip=0] - Number of records to skip
 * @param {number} [params.limit=20] - Number of records to return
 * @returns {Promise<Array>} Workout history
 */
function getWorkoutHistory(params = {}) {
  const defaultParams = {
    skip: 0,
    limit: 20,
    ...params
  };
  return request.get(`${BASE_URL}/history`, { data: defaultParams });
}

/**
 * Create a new workout
 * @param {Object} params - Workout parameters
 * @param {number} params.plan_id - Training plan ID
 * @param {string} params.scheduled_date - Scheduled date (YYYY-MM-DD)
 * @param {string} params.status - Workout status (default: 'not_started')
 * @returns {Promise<Object>} Created workout info
 */
function createWorkout(params) {
  return request.post(BASE_URL, params);
}

/**
 * Get workout detail
 * @param {string|number} workoutId - Workout ID
 * @returns {Promise<Object>} Workout detail including exercises
 */
function getWorkoutDetail(workoutId) {
  return request.get(`${BASE_URL}/${workoutId}`);
}

/**
 * Update the workout with complete data in one request
 * @param {string|number} workoutId - Workout ID
 * @param {Object} data - Complete workout data including exercises and sets
 * @returns {Promise<Object>} Updated workout info
 */
function updateWorkoutFull(workoutId, data) {
  return request.put(`${BASE_URL}/${workoutId}/full-update`, data);
}

module.exports = {
  updateWorkoutStatus,
  getWorkoutStatistics,
  linkWorkoutToSession,
  startWorkout,
  completeWorkout,
  recordWorkoutSet,
  pauseWorkout,
  resumeWorkout,
  getWorkoutHistory,
  createWorkout,
  getWorkoutDetail,
  updateWorkoutFull
};