/**
 * 动作库API模块
 */

const { get, post } = require('./request');

// 基础URL
const BASE_PATH = '/api/v1/exercise';

// 常量数据
const MUSCLE_CATEGORIES = [
  { id: '2', name: '内收肌', en_name: 'hip_adductors' },
  { id: '4', name: '肱二头肌', en_name: 'biceps' },
  { id: '5', name: '肱肌', en_name: 'brachialis' },
  { id: '6', name: '肱桡肌', en_name: 'brachioradialis' },
  { id: '8', name: '三角肌前束', en_name: 'deltoid_anterior' },
  { id: '9', name: '三角肌后束', en_name: 'deltoid_lateral' },
  { id: '10', name: '三角肌', en_name: 'deltoids' },
  { id: '12', name: '腓肠肌', en_name: 'gastrocnemius' },
  { id: '13', name: '臀大肌', en_name: 'gluteus_maximus' },
  { id: '17', name: '腘绳肌', en_name: 'hamstrings' },
  { id: '18', name: '髂腰肌', en_name: 'il<PERSON><PERSON><PERSON><PERSON>' },
  { id: '19', name: '冈下肌', en_name: 'infraspinatus' },
  { id: '20', name: '背阔肌', en_name: 'latissimus_dorsi' },
  { id: '22', name: '腹斜肌', en_name: 'obliques' },
  { id: '24', name: '胸大肌上束', en_name: 'chest' },
  { id: '25', name: '胸大肌', en_name: 'chest' },
  { id: '27', name: '股四头肌', en_name: 'quadriceps' },
  { id: '28', name: '腹直肌', en_name: 'rectus_abdominis' },
  { id: '29', name: '缝匠肌', en_name: 'sartorius' },
  { id: '31', name: '前锯肌', en_name: 'serratus_anterior' },
  { id: '32', name: '比目鱼肌', en_name: 'soleus' },
  { id: '36', name: '股筋膜张肌', en_name: 'tensor_fasciae_femoris' },
  { id: '37', name: '大圆肌', en_name: 'teres_major' },
  { id: '38', name: '小圆肌', en_name: 'teres_minor' },
  { id: '39', name: '胫骨肌', en_name: 'tibias' },
  { id: '43', name: '斜方肌', en_name: 'trapezius' },
  { id: '44', name: '肱三头肌', en_name: 'triceps' },
  { id: '45', name: '腕伸肌', en_name: 'wrist_extensors' },
  { id: '46', name: '腕屈肌', en_name: 'wrist_flexors' },
  { id: '51', name: '胸大肌下束', en_name: 'chest' }
];

const BODY_PART_CATEGORIES = [
  { id: 2, name: '胸部' },
  { id: 1, name: '腿部' },
  { id: 3, name: '髋部' },
  { id: 4, name: '背部' },
  { id: 5, name: '大臂' },
  { id: 6, name: '肩部' },
  { id: 7, name: '小臂' },
  { id: 8, name: '小腿' },
  { id: 9, name: '颈部' },
  { id: 10, name: '有氧' },
  { id: 11, name: '全身' },
  { id: 12, name: '腰腹部' },
  { id: 13, name: '爆发力' },
  { id: 14, name: '力量举' },
  { id: 15, name: '瑜伽' },
  { id: 16, name: '拉伸' },
  { id: 17, name: '二头' },
  { id: 18, name: '三头' },
  { id: 19, name: '股四头肌' },
  { id: 20, name: '腘绳肌' }
];

const EQUIPMENT_CATEGORIES = [
  { id: 1, name: '杠铃' },
  { id: 2, name: '自重' },
  { id: 3, name: '绳索' },
  { id: 4, name: '哑铃' },
  { id: 5, name: 'EZ杠铃' },
  { id: 6, name: '固定器械' },
  { id: 7, name: '悍马器械' },
  { id: 8, name: '史密斯' },
  { id: 9, name: '负重' },
  { id: 10, name: '协助' },
  { id: 11, name: '弹力带' },
  { id: 12, name: '战绳' },
  { id: 13, name: '波速球' },
  { id: 14, name: '悍马' },
  { id: 15, name: '壶铃' },
  { id: 16, name: '药球' },
  { id: 17, name: '奥杆' },
  { id: 18, name: '雪橇' },
  { id: 19, name: '阻力带' },
  { id: 20, name: '泡沫轴' },
  { id: 21, name: '筋膜球' },
  { id: 22, name: '绳类' },
  { id: 23, name: '瑜伽球' },
  { id: 24, name: '训练棍' },
  { id: 25, name: 'TRX' },
  { id: 26, name: '六角杠铃' },
  { id: 27, name: '卷腹轮' }
];

/**
 * 获取动作列表
 * @param {Object} params - 查询参数
 * @param {Number} [params.body_part_id] - 身体部位ID
 * @param {Number} [params.equipment_id] - 器材ID
 * @param {Number} [params.skip=0] - 分页起始位置
 * @param {Number} [params.limit=100] - 返回记录数量
 * @returns {Promise<Array>} 返回动作列表
 */
const getExercises = (params = {}) => {
  const queryParams = {
    skip: params.skip || 0,
    limit: params.limit || 100
  };

  // 添加可选参数
  if (params.body_part_id) {
    queryParams.body_part_id = params.body_part_id;
  }
  if (params.equipment_id) {
    queryParams.equipment_id = params.equipment_id;
  }

  return get(`${BASE_PATH}/exercises/`, queryParams);
};

/**
 * 搜索动作
 * @param {Object} params - 搜索参数
 * @param {String} [params.keyword] - 搜索关键词
 * @param {Number} [params.body_part_id] - 身体部位ID
 * @param {Number} [params.equipment_id] - 器材ID
 * @param {Number} [params.muscle_id] - 目标肌肉ID
 * @param {String} [params.level] - 难度级别
 * @param {Number} [params.skip=0] - 分页起始位置
 * @param {Number} [params.limit=100] - 返回记录数量
 * @returns {Promise<Array>} 返回搜索结果
 */
const searchExercises = (params = {}) => {
  const queryParams = {
    skip: params.skip || 0,
    limit: params.limit || 100
  };

  // 添加所有可选参数
  if (params.keyword) {
    queryParams.keyword = params.keyword;
  }
  if (params.body_part_id) {
    queryParams.body_part_id = params.body_part_id;
  }
  if (params.equipment_id) {
    queryParams.equipment_id = params.equipment_id;
  }
  if (params.muscle_id) {
    queryParams.muscle_id = params.muscle_id;
  }
  if (params.level) {
    queryParams.level = params.level;
  }

  return get(`${BASE_PATH}/exercises/search`, queryParams);
};

/**
 * 创建动作
 * @param {Object} exerciseData - 动作数据
 * @returns {Promise<Object>} 返回创建的动作
 */
const createExercise = async (exerciseData) => {
  try {
    // 第一步：创建基本动作信息
    const baseData = {
      name: exerciseData.name,
      body_part_id: exerciseData.body_part_id,
      equipment_id: exerciseData.equipment_id,
      level: exerciseData.level,
      image_name: exerciseData.image_name,
      gif_url: exerciseData.gif_url
    };

    const exerciseResult = await post(`${BASE_PATH}/exercises/`, baseData);

    if (!exerciseResult || !exerciseResult.id) {
      throw new Error('创建动作基本信息失败');
    }

    const exerciseId = exerciseResult.id;

    // 第二步：创建动作详情
    const detailData = {
      target_muscles_id: exerciseData.target_muscles_id,
      synergist_muscles_id: exerciseData.synergist_muscles_id,
      ex_instructions: exerciseData.ex_instructions,
      exercise_tips: exerciseData.exercise_tips,
      video_file: exerciseData.video_file
    };

    await post(`${BASE_PATH}/exercises/${exerciseId}/detail`, detailData);

    return {
      success: true,
      exerciseId: exerciseId
    };
  } catch (error) {
    console.error('创建动作失败:', error);
    return {
      success: false,
      error: error.message || '创建动作失败'
    };
  }
};

/**
 * 获取动作详情
 * @param {Number} exerciseId - 动作ID
 * @returns {Promise<Object>} 返回动作详情
 */
const getExerciseDetail = (exerciseId) => {
  return get(`${BASE_PATH}/exercises/${exerciseId}/detail`);
};

/**
 * 获取动作基本信息
 * @param {Number} exerciseId - 动作ID
 * @returns {Promise<Object>} 返回动作基本信息
 */
const getExercise = (exerciseId) => {
  return get(`${BASE_PATH}/exercises/${exerciseId}`);
};

/**
 * 获取肌肉分类数据
 * @returns {Array} 肌肉分类数据
 */
const getMuscleCategories = () => {
  return MUSCLE_CATEGORIES;
};

/**
 * 获取身体部位分类数据
 * @returns {Array} 身体部位分类数据
 */
const getBodyPartCategories = () => {
  return BODY_PART_CATEGORIES;
};

/**
 * 获取器材分类数据
 * @returns {Array} 器材分类数据
 */
const getEquipmentCategories = () => {
  return EQUIPMENT_CATEGORIES;
};

/**
 * 增加动作点击次数
 * @param {Number} exerciseId - 动作ID
 * @returns {Promise<Object>} 返回更新结果
 */
const incrementHitTime = (exerciseId) => {
  return post(`${BASE_PATH}/exercises/${exerciseId}/hit`, {});
};

module.exports = {
  getExercises,
  searchExercises,
  createExercise,
  getExerciseDetail,
  getExercise,
  incrementHitTime,
  getMuscleCategories,
  getBodyPartCategories,
  getEquipmentCategories
};