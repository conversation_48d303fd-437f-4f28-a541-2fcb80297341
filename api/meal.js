/**
 * 餐食记录相关API
 */

const { request, post, BASE_URL, get } = require('./request');

/**
 * 上传图片到服务器
 * @param {string} url - API路径
 * @param {string} filePath - 本地文件路径
 * @param {Object} formData - 额外的表单数据
 * @returns {Promise} 返回上传结果
 */
const uploadImage = (url, filePath, formData = {}) => {
  const token = wx.getStorageSync('token');
  
  console.log(`上传图片 ${filePath} 到 ${url}`);
  console.log('表单数据:', formData);
  
  return new Promise((resolve, reject) => {
    wx.uploadFile({
      url: BASE_URL + url,
      filePath: filePath,
      name: 'image', // 服务器接收的文件参数名
      formData: formData,
      header: {
        'Authorization': token ? `Bearer ${token}` : ''
        // uploadFile会自动设置Content-Type为multipart/form-data
      },
      success: (res) => {
        console.log(`上传响应状态码: ${res.statusCode}`);
        if (res.statusCode >= 200 && res.statusCode < 300) {
          console.log('图片上传成功');
          let data;
          try {
            data = JSON.parse(res.data);
          } catch (e) {
            data = res.data;
          }
          resolve(data);
        } else {
          console.error('图片上传失败:', res.statusCode, res.data);
          let errorData;
          try {
            errorData = JSON.parse(res.data);
          } catch (e) {
            errorData = res.data;
          }
          reject({
            statusCode: res.statusCode,
            message: '上传失败: ' + JSON.stringify(errorData),
            data: errorData
          });
        }
      },
      fail: (err) => {
        console.error('图片上传请求错误:', err);
        reject({
          message: '网络请求失败',
          originalError: err
        });
      }
    });
  });
};

/**
 * 确认食物识别结果
 * @param {number} recognitionId - 识别结果ID
 * @param {Array} foodItems - 食物项数组
 * @param {string} mealName - 餐食名称
 * @param {string} healthRecommendation - 健康建议
 * @param {Object} nutritionTotals - 营养总计
 * @param {string} mealDate - 餐食日期
 * @param {string} mealType - 餐食类型
 * @param {Object} options - 其他选项
 * @returns {Promise} 返回确认结果
 */
const confirmFoodRecognition = async (
  recognitionId,
  foodItems,
  mealName,
  healthRecommendation,
  nutritionTotals = null,
  mealDate = null,
  mealType = null,
  options = {}
) => {
  console.log(`Meal API: 确认食物识别结果, ID=${recognitionId}`);
  
  // 参数验证
  if (!recognitionId) {
    console.error('确认食物识别失败: 缺少recognition_id');
    throw new Error('缺少recognition_id参数');
  }
  
  if (!Array.isArray(foodItems) || foodItems.length === 0) {
    console.error('确认食物识别失败: 缺少food_items或为空数组');
    throw new Error('食物列表不能为空');
  }
  
  // 输出基本参数信息
  console.log('确认参数:', {
    recognitionId,
    mealName,
    mealType,
    mealDate,
    foodItemsCount: foodItems.length
  });
  
  // 准备基本确认数据
  const confirmationData = {
    food_items: foodItems,
    meal_name: mealName,
    health_recommendation: healthRecommendation
  };
  
  // 添加营养总计
  if (nutritionTotals) {
    confirmationData.nutrition_totals = nutritionTotals;
  }
  
  // 添加餐食日期和类型
  if (mealDate) {
    confirmationData.meal_date = mealDate;
  }
  
  if (mealType) {
    confirmationData.meal_type = mealType;
  }
  
  // 使用POST请求发送确认数据
  try {
    const url = `/api/v1/food-recognition/${recognitionId}/confirm`;
    console.log(`准备发送确认请求到: ${url}`);
    
    // 移除不需要的UI字段，避免数据过大
    const processedData = JSON.parse(JSON.stringify(confirmationData));
    processedData.food_items = processedData.food_items.map(item => {
      // 移除UI专用字段
      delete item.isLocalImage;
      // 如果有本地图片路径但没有进行上传处理，删除它
      if (item.imageUrl && !item.imageUrl.startsWith('http')) {
        delete item.imageUrl;
      }
      return item;
    });
    
    // 直接使用POST发送确认数据
    console.log('发送确认数据 (简化版)', processedData);
    const result = await post(url, processedData);
    console.log('确认请求成功，返回结果类型:', typeof result);
    return result;
  } catch (error) {
    console.error('确认食物识别失败:', error);
    // 输出错误状态码
    if (error.statusCode) {
      console.error(`HTTP状态码: ${error.statusCode}`);
    }
    throw error;
  }
};

/**
 * 保存餐食记录
 * @param {string} mealType - 餐食类型
 * @param {Object} foodData - 食物数据
 * @param {string} selectedDate - 选中日期
 * @returns {Promise} 返回保存结果
 */
const saveMealRecord = async (mealType, foodData, selectedDate) => {
  console.log(`Meal API: 保存餐食记录, 类型=${mealType}, 日期=${selectedDate}`);
  
  try {
    // 准备保存数据
    const saveData = {
      meal_type: mealType,
      meal_date: selectedDate,
      food_data: foodData
    };
    
    // 发送保存请求
    return await post('/api/v1/user/meals', saveData);
  } catch (error) {
    console.error('保存餐食记录失败:', error);
    throw error;
  }
};

/**
 * 获取每日营养摄入汇总
 * @param {string} queryDate - 查询日期（格式：YYYY-MM-DD，默认为当天）
 * @returns {Promise<Object>} 返回每日营养摄入汇总数据
 */
const getDailyNutrition = async (queryDate) => {
  console.log(`Meal API: 获取每日营养摄入汇总, 日期=${queryDate}`);
  
  try {
    // 构建带有查询参数的URL
    let url = '/api/v1/meal/daily';
    
    // 格式化日期确保格式为YYYY-MM-DD
    if (queryDate) {
      // 解析日期
      const parts = queryDate.split('-');
      if (parts.length === 3) {
        const year = parts[0];
        // 确保月和日是两位数
        const month = parts[1].padStart(2, '0');
        const day = parts[2].padStart(2, '0');
        // 重新组合成YYYY-MM-DD格式
        const formattedDate = `${year}-${month}-${day}`;
        url += `?query_date=${formattedDate}`;
      } else {
        url += `?query_date=${queryDate}`;
      }
    }
    
    // 发送GET请求
    const result = await get(url);
    
    // 处理返回结果中的图片URL，添加BASE_URL前缀
    if (result && result.meals && Array.isArray(result.meals)) {
      result.meals.forEach(meal => {
        // 为餐食图片添加BASE_URL前缀
        if (meal.image_url && !meal.image_url.startsWith('http')) {
          meal.image_url = BASE_URL + meal.image_url;
        }
        
        if (meal.thumb_image_url && !meal.thumb_image_url.startsWith('http')) {
          meal.thumb_image_url = BASE_URL + meal.thumb_image_url;
        }
        
        // 如果有food_items，也处理其中的图片URL
        if (meal.food_items && Array.isArray(meal.food_items)) {
          meal.food_items.forEach(item => {
            if (item.image_url && !item.image_url.startsWith('http')) {
              item.image_url = BASE_URL + item.image_url;
            }
          });
        }
      });
    }
    
    return result;
  } catch (error) {
    console.error('获取每日营养摄入失败:', error);
    throw error;
  }
};

/**
 * 获取餐食详情
 * @param {number|string} mealId - 餐食记录ID
 * @returns {Promise<Object>} 返回餐食详细信息
 */
const getMealDetail = async (mealId) => {
  console.log(`Meal API: 获取餐食详情, ID=${mealId}`);
  
  try {
    return await get(`/api/v1/meal/${mealId}`);
  } catch (error) {
    console.error('获取餐食详情失败:', error);
    throw error;
  }
};

/**
 * 创建餐食记录
 * @param {Object} mealData - 餐食创建数据
 * @param {string} mealData.date - 日期（格式：YYYY-MM-DD）
 * @param {string} mealData.meal_type - 餐食类型（breakfast/lunch/dinner/snack）
 * @param {string} [mealData.image_url] - 图片URL
 * @param {string} [mealData.file_id] - 文件ID
 * @param {string} [mealData.thumb_image_url] - 缩略图URL
 * @param {boolean} [mealData.is_ai_recognized] - 是否AI识别
 * @returns {Promise<Object>} 返回创建的餐食记录
 */
const createMealRecord = async (mealData) => {
  console.log(`Meal API: 创建餐食记录, 类型=${mealData.meal_type}, 日期=${mealData.date}`);
  
  try {
    return await post('/api/v1/meal/', mealData);
  } catch (error) {
    console.error('创建餐食记录失败:', error);
    throw error;
  }
};

/**
 * 更新餐食记录
 * @param {number|string} mealId - 餐食记录ID
 * @param {Object} updateData - 餐食更新数据
 * @returns {Promise<Object>} 返回更新后的餐食记录
 */
const updateMealRecord = async (mealId, updateData) => {
  console.log(`Meal API: 更新餐食记录, ID=${mealId}`);
  
  try {
    return await request(`/api/v1/meal/${mealId}`, 'PUT', updateData);
  } catch (error) {
    console.error('更新餐食记录失败:', error);
    throw error;
  }
};

/**
 * 删除餐食记录
 * @param {number|string} mealId - 餐食记录ID
 * @returns {Promise<Object>} 返回被删除的餐食记录
 */
const deleteMealRecord = async (mealId) => {
  console.log(`Meal API: 删除餐食记录, ID=${mealId}`);
  
  try {
    return await request(`/api/v1/meal/${mealId}`, 'DELETE');
  } catch (error) {
    console.error('删除餐食记录失败:', error);
    throw error;
  }
};

/**
 * 添加食物项
 * @param {number|string} mealId - 餐食记录ID
 * @param {Object} foodItemData - 食物项创建数据
 * @returns {Promise<Object>} 返回创建的食物项
 */
const addFoodItem = async (mealId, foodItemData) => {
  console.log(`Meal API: 添加食物项, 餐食ID=${mealId}, 食物=${foodItemData.name}`);
  
  try {
    return await post(`/api/v1/meal/${mealId}/food-items`, foodItemData);
  } catch (error) {
    console.error('添加食物项失败:', error);
    throw error;
  }
};

/**
 * 获取食物项详情
 * @param {number|string} foodItemId - 食物项ID
 * @returns {Promise<Object>} 返回食物项详细信息
 */
const getFoodItemDetail = async (foodItemId) => {
  console.log(`Meal API: 获取食物项详情, ID=${foodItemId}`);
  
  try {
    return await get(`/api/v1/meal/food-items/${foodItemId}`);
  } catch (error) {
    console.error('获取食物项详情失败:', error);
    throw error;
  }
};

/**
 * 更新食物项
 * @param {number|string} foodItemId - 食物项ID
 * @param {Object} updateData - 食物项更新数据
 * @returns {Promise<Object>} 返回更新后的食物项
 */
const updateFoodItem = async (foodItemId, updateData) => {
  console.log(`Meal API: 更新食物项, ID=${foodItemId}`);
  
  try {
    return await request(`/api/v1/meal/food-items/${foodItemId}`, 'PUT', updateData);
  } catch (error) {
    console.error('更新食物项失败:', error);
    throw error;
  }
};

/**
 * 删除食物项
 * @param {number|string} foodItemId - 食物项ID
 * @returns {Promise<Object>} 返回被删除的食物项
 */
const deleteFoodItem = async (foodItemId) => {
  console.log(`Meal API: 删除食物项, ID=${foodItemId}`);
  
  try {
    return await request(`/api/v1/meal/food-items/${foodItemId}`, 'DELETE');
  } catch (error) {
    console.error('删除食物项失败:', error);
    throw error;
  }
};

module.exports = {
  confirmFoodRecognition,
  saveMealRecord,
  getDailyNutrition,
  getMealDetail,
  createMealRecord,
  updateMealRecord,
  deleteMealRecord,
  addFoodItem,
  getFoodItemDetail,
  updateFoodItem,
  deleteFoodItem
}; 