/**
 * 社区功能API
 */

const { get, post, put, del } = require('./request');

/**
 * 获取帖子列表
 * @param {Object} params - 查询参数
 * @param {Number} params.skip - 跳过的数量
 * @param {Number} params.limit - 返回的最大数量
 * @returns {Promise} 返回帖子列表
 */
const getPosts = (params = { skip: 0, limit: 20 }) => {
  return get('/api/v1/community/posts/', params);
};

/**
 * 获取帖子详情
 * @param {number} postId 帖子ID
 */
const getPostDetail = async (postId) => {
  try {
    const response = await get(`/api/v1/community/posts/${postId}`);
    return response;
  } catch (error) {
    console.error('获取帖子详情失败:', error);
    throw error;
  }
};

/**
 * 创建帖子
 * @param {Object} postData - 帖子数据
 * @param {String} postData.title - 帖子标题
 * @param {String} postData.content - 帖子内容
 * @param {Number} postData.related_workout_id - 关联的训练ID
 * @param {Array} postData.image_urls - 图片URL数组
 * @returns {Promise} 返回创建结果
 */
const createPost = (postData) => {
  return post('/api/v1/community/posts/', postData);
};

/**
 * 更新帖子
 * @param {Number} postId - 帖子ID
 * @param {Object} postData - 更新的帖子数据
 * @returns {Promise} 返回更新结果
 */
const updatePost = (postId, postData) => {
  return put(`/api/v1/community/posts/${postId}`, postData);
};

/**
 * 点赞帖子
 * @param {Number} postId - 帖子ID
 * @returns {Promise} 返回点赞结果
 */
const likePost = (postId) => {
  return post(`/api/v1/community/posts/${postId}/like/`);
};

/**
 * 取消点赞帖子
 * @param {Number} postId - 帖子ID
 * @returns {Promise} 返回取消点赞结果
 */
const unlikePost = (postId) => {
  return del(`/api/v1/community/posts/${postId}/like/`);
};

/**
 * 举报帖子
 * @param {Number} postId - 帖子ID
 * @param {Object} reportData - 举报数据
 * @param {String} reportData.reason - 举报原因
 * @returns {Promise} 返回举报结果
 */
const reportPost = (postId, reportData) => {
  return post(`/api/v1/community/posts/${postId}/report/`, reportData);
};

/**
 * 获取帖子的评论列表
 * @param {Number} postId - 帖子ID
 * @param {Object} params - 查询参数
 * @param {Number} params.skip - 跳过的数量
 * @param {Number} params.limit - 返回的最大数量
 * @returns {Promise} 返回评论列表
 */
const getPostComments = (postId, params = { skip: 0, limit: 20 }) => {
  return get(`/api/v1/community/posts/${postId}/comments/`, params);
};

/**
 * 创建评论
 * @param {number} postId 帖子ID
 * @param {object} commentData 评论数据
 */
const createComment = async (postId, commentData) => {
  try {
    const response = await post(`/api/v1/community/posts/${postId}/comments/`, commentData);
    return response;
  } catch (error) {
    console.error('创建评论失败:', error);
    throw error;
  }
};

/**
 * 获取评论列表
 * @param {number} postId 帖子ID
 * @param {object} params 查询参数
 */
const getComments = async (postId, params = {}) => {
  try {
    const response = await get(`/api/v1/community/posts/${postId}/comments/`, params);
    return response;
  } catch (error) {
    console.error('获取评论列表失败:', error);
    throw error;
  }
};

/**
 * 回复评论
 * @param {number} commentId 评论ID
 * @param {object} replyData 回复数据
 */
const replyComment = async (commentId, replyData) => {
  try {
    const response = await post(`/api/v1/community/comments/${commentId}/replies/`, replyData);
    return response;
  } catch (error) {
    console.error('回复评论失败:', error);
    throw error;
  }
};

/**
 * 点赞评论
 * @param {number} commentId 评论ID
 * @param {boolean} isLiked 是否点赞
 */
const likeComment = async (commentId, isLiked) => {
  try {
    if (isLiked) {
      return await post(`/api/v1/community/comments/${commentId}/like/`, {});
    } else {
      return await del(`/api/v1/community/comments/${commentId}/like/`);
    }
  } catch (error) {
    console.error('点赞评论失败:', error);
    throw error;
  }
};

/**
 * 取消点赞评论
 * @param {Number} commentId - 评论ID
 * @returns {Promise} 返回取消点赞结果
 */
const unlikeComment = (commentId) => {
  return del(`/api/v1/community/comments/${commentId}/like/`);
};

/**
 * 举报评论
 * @param {Number} commentId - 评论ID
 * @param {Object} reportData - 举报数据
 * @param {String} reportData.reason - 举报原因
 * @returns {Promise} 返回举报结果
 */
const reportComment = (commentId, reportData) => {
  return post(`/api/v1/community/comments/${commentId}/report/`, reportData);
};

/**
 * 获取通知列表
 * @param {Object} params - 查询参数
 * @param {Number} params.skip - 跳过的数量
 * @param {Number} params.limit - 返回的最大数量
 * @returns {Promise} 返回通知列表
 */
const getNotifications = (params = { skip: 0, limit: 20 }) => {
  return get('/api/v1/community/notifications/', params);
};

/**
 * 标记通知为已读
 * @param {Number} notificationId - 通知ID
 * @returns {Promise} 返回标记结果
 */
const markNotificationAsRead = (notificationId) => {
  return put(`/api/v1/community/notifications/${notificationId}/read/`);
};

/**
 * 标记所有通知为已读
 * @returns {Promise} 返回标记结果
 */
const markAllNotificationsAsRead = () => {
  return put('/api/v1/community/notifications/read-all/');
};

/**
 * 获取用户信息
 * @param {Number} userId - 用户ID
 * @returns {Promise} 返回用户信息
 */
const getUserProfile = (userId) => {
  return get(`/api/v1/community/users/${userId}`);
};

/**
 * 获取用户训练列表
 * @param {Number} userId - 用户ID
 * @param {Object} params - 查询参数
 * @param {Number} params.skip - 跳过的数量
 * @param {Number} params.limit - 返回的最大数量
 * @returns {Promise} 返回用户训练列表
 */
const getUserWorkouts = (userId, params = { skip: 0, limit: 20 }) => {
  return get(`/api/v1/community/users/${userId}/workouts`, params);
};

/**
 * 获取用户统计数据
 * @param {Number} userId - 用户ID
 * @returns {Promise} 返回用户统计数据
 */
const getUserStats = (userId) => {
  return get(`/api/v1/community/users/${userId}/stats`);
};

/**
 * 关注/取消关注用户
 * @param {number} userId 用户ID
 * @param {boolean} isFollowing 是否关注
 */
const followUser = async (userId, isFollowing) => {
  try {
    const url = `/api/v1/community/users/${userId}/follow/`;

    if (isFollowing) {
      return await post(url, {});
    } else {
      return await del(url);
    }
  } catch (error) {
    console.error('关注用户失败:', error);
    throw error;
  }
};

/**
 * 检查用户关注状态
 * @param {number} userId 用户ID
 */
const checkFollowStatus = async (userId) => {
  try {
    const response = await get(`/api/v1/community/users/${userId}/follow-status/`);
    return response;
  } catch (error) {
    console.error('检查关注状态失败:', error);
    throw error;
  }
};

/**
 * 获取关注列表
 * @param {Number} userId - 用户ID
 * @param {Object} params - 查询参数
 * @param {Number} params.skip - 跳过的数量
 * @param {Number} params.limit - 返回的最大数量
 * @returns {Promise} 返回关注列表
 */
const getFollowing = (userId, params = { skip: 0, limit: 20 }) => {
  return get(`/api/v1/community/users/${userId}/following/`, params);
};

/**
 * 获取粉丝列表
 * @param {Number} userId - 用户ID
 * @param {Object} params - 查询参数
 * @param {Number} params.skip - 跳过的数量
 * @param {Number} params.limit - 返回的最大数量
 * @returns {Promise} 返回粉丝列表
 */
const getFollowers = (userId, params = { skip: 0, limit: 20 }) => {
  return get(`/api/v1/community/users/${userId}/followers/`, params);
};

/**
 * 获取训练详情
 * @param {Number} workoutId - 训练ID
 * @returns {Promise} 返回训练详情
 */
const getWorkoutDetail = (workoutId) => {
  return get(`/api/v1/community/daily-workouts/${workoutId}`);
};

/**
 * 创建训练记录
 * @param {Object} workoutData - 训练数据
 * @returns {Promise} 返回创建结果
 */
const createWorkout = (workoutData) => {
  return post('/api/v1/community/daily-workouts/', workoutData);
};

/**
 * 分享训练记录到社区
 * @param {Number} workoutId - 训练ID (0表示创建新记录，其他值表示更新现有记录)
 * @param {Object} shareData - 分享数据
 * @param {String} shareData.title - 分享标题
 * @param {String} shareData.content - 分享内容
 * @param {Object} shareData.workout_data - 训练数据
 * @param {Array} shareData.workout_data.exercises - 动作列表
 * @param {Number} shareData.workout_data.duration_seconds - 训练总时长(秒)
 * @param {Number} shareData.workout_data.total_sets - 总组数
 * @param {Number} shareData.workout_data.total_volume - 总容量
 * @param {Array} shareData.images - 图片URL数组
 * @param {String} shareData.visibility - 可见性 (everyone, friends, private)
 * @param {Array} shareData.tags - 标签列表
 * @returns {Promise} 返回分享结果(PostResponse结构)
 */
const shareWorkout = (workoutId, shareData) => {
  return post(`/api/v1/community/workout/${workoutId}/share`, shareData);
};

module.exports = {
  // 帖子相关
  getPosts,
  getPostDetail,
  createPost,
  updatePost,
  likePost,
  unlikePost,
  reportPost,

  // 评论相关
  getPostComments,
  createComment,
  getComments,
  replyComment,
  likeComment,
  unlikeComment,
  reportComment,

  // 通知相关
  getNotifications,
  markNotificationAsRead,
  markAllNotificationsAsRead,

  // 用户相关
  getUserProfile,
  getUserWorkouts,
  getUserStats,
  followUser,
  checkFollowStatus,
  getFollowing,
  getFollowers,

  // 训练相关
  getWorkoutDetail,
  createWorkout,
  shareWorkout
};