/**
 * 动作库API适配器
 * 
 * 此模块用于兼容原有云函数调用方式，
 * 将云函数调用转换为对新后端API的调用
 */

const exerciseApi = require('./exercise');
const { BASE_URL, post } = require('./request');

/**
 * 将API返回的动作列表转换为原云函数返回格式
 * @param {Array} exercises - API返回的动作列表
 * @param {Number} limit - 请求的数据条数
 * @returns {Object} 原云函数格式的响应
 */
const formatExerciseListResponse = (exercises, limit = 20) => {
  // 当返回的数据量等于请求的limit时，认为可能还有更多数据
  const hasMore = exercises.length >= limit;
  
  return {
    success: true,
    dataList: exercises.map(exercise => ({
      ...exercise,
      full_gif_url: exercise.gif_url ? `${BASE_URL}/exercises/gifs/${exercise.gif_url}` : '',
      full_image_url: exercise.image_name ? `${BASE_URL}/exercises/images/${exercise.image_name}` : ''
    })),
    total: exercises.length,
    hasMore: hasMore
  };
};

/**
 * 将API返回的动作详情转换为原云函数返回格式
 * @param {Object} detail - API返回的动作详情
 * @returns {Object} 原云函数格式的响应
 */
const formatExerciseDetailResponse = (detail) => {
  console.log('收到的动作详情数据:', detail);
  
  // 获取肌肉信息 - 修正为小写字段名
  const targetMuscles = detail.target_muscles_id
    ? detail.target_muscles_id.map(id => {
        const muscle = exerciseApi.getMuscleCategories().find(m => m.id === id.toString());
        return muscle ? muscle.en_name : '';
      }).filter(Boolean)
    : [];
  
  const synergistMuscles = detail.synergist_muscles_id
    ? detail.synergist_muscles_id.map(id => {
        const muscle = exerciseApi.getMuscleCategories().find(m => m.id === id.toString());
        return muscle ? muscle.en_name : '';
      }).filter(Boolean)
    : [];
  
  console.log('映射后的肌肉数据:', { targetMuscles, synergistMuscles });
  
  // 构建肌肉图片URL - 修正基础路径
  const frontBaseUrl = `${BASE_URL}/muscles/body_front`;
  const backBaseUrl = `${BASE_URL}/muscles/body_back`;
  
  const frontImages_main = targetMuscles.map(muscle => 
    `${frontBaseUrl}/main/front_${muscle}.png`
  );
  
  const frontImages_minor = synergistMuscles.map(muscle => 
    `${frontBaseUrl}/minor/front_${muscle}.png`
  );
  
  const backImages_main = targetMuscles.map(muscle => 
    `${backBaseUrl}/main/back_${muscle}.png`
  );
  
  const backImages_minor = synergistMuscles.map(muscle => 
    `${backBaseUrl}/minor/back_${muscle}.png`
  );
  
  // 合并前视图和后视图的图片链接
  // 修改: 过滤掉frontImages_minor中与frontImages_main中同名的肌肉图片
  const getMuscleName = (url) => {
    const match = url.match(/front_([^.]+)\.png/);
    return match ? match[1] : '';
  };
  
  // 获取所有主要肌肉名称
  const mainMuscleNames = frontImages_main.map(getMuscleName);
  
  // 过滤掉frontImages_minor中与frontImages_main中同名的肌肉图片
  const filteredFrontImagesMinor = frontImages_minor.filter(url => {
    const muscleName = getMuscleName(url);
    return !mainMuscleNames.includes(muscleName);
  });
  
  const frontImages = [...frontImages_main, ...filteredFrontImagesMinor].filter(url => url);
  const backImages = [...backImages_main, ...backImages_minor].filter(url => url);
  
  console.log('生成的肌肉图片URL:', { frontImages, backImages });
  
  // 获取肌肉名称 - 修正为小写字段名
  const targetMusclesNames = detail.target_muscles_id
    ? detail.target_muscles_id.map(id => {
        const muscle = exerciseApi.getMuscleCategories().find(m => m.id === id.toString());
        return muscle ? muscle.name : '';
      }).filter(Boolean).join('、')
    : '';
  
  const synergistMusclesNames = detail.synergist_muscles_id
    ? detail.synergist_muscles_id.map(id => {
        const muscle = exerciseApi.getMuscleCategories().find(m => m.id === id.toString());
        return muscle ? muscle.name : '';
      }).filter(Boolean).join('、')
    : '';
  
  // 处理视频URL - 统一为video路径
  const videoUrl = detail.video_file 
    ? `${BASE_URL}/api/v1/exercise/video/${detail.video_file}`
    : '';
  console.log(videoUrl)
  return {
    success: true,
    data: {
      exerciseInfo: {
        ...detail,
        Target_muscles_names: targetMusclesNames,
        Synergist_muscles_names: synergistMusclesNames,
        videoUrl,
        video_url: videoUrl // 添加一个小写字段，与detail.js保持一致
      },
      targetMuscles,
      synergistMuscles,
      frontImages,
      backImages
    }
  };
};

/**
 * 适配原有fetchExerciseList云函数
 * @param {Object} params - 原云函数参数
 * @returns {Promise<Object>} 返回符合原云函数格式的响应
 */
const fetchExerciseList = async (params = {}) => {
  try {
    const limit = 20; // 每页数据量
    
    const apiParams = {
      body_part_id: params.bodyPartId ? parseInt(params.bodyPartId) : undefined,
      equipment_id: params.equipmentId ? parseInt(params.equipmentId) : undefined,
      skip: ((params.page || 1) - 1) * limit,
      limit: limit
    };
    
    const exercises = await exerciseApi.getExercises(apiParams);
    return formatExerciseListResponse(exercises, limit);
  } catch (error) {
    console.error('获取动作列表失败:', error);
    return {
      success: false,
      errorMessage: error.message || '获取动作列表失败'
    };
  }
};

/**
 * 适配原有fetchExerciseDetail云函数
 * @param {Object} params - 原云函数参数
 * @returns {Promise<Object>} 返回符合原云函数格式的响应
 */
const fetchExerciseDetail = async (params = {}) => {
  try {
    if (!params.id) {
      return {
        success: false,
        error: '未提供动作ID'
      };
    }
    
    const detail = await exerciseApi.getExerciseDetail(params.id);
    return formatExerciseDetailResponse(detail);
  } catch (error) {
    console.error('获取动作详情失败:', error);
    return {
      success: false,
      error: error.message || '获取动作详情失败'
    };
  }
};

/**
 * 上传动作媒体文件
 * @param {Object} fileContent - 文件内容
 * @param {String} fileType - 文件类型 (image, gif, video)
 * @returns {Promise<Object>} 返回上传结果
 */
const uploadExerciseMedia = async (fileContent, fileType) => {
  try {
    if (!fileContent) {
      return {
        success: false,
        error: '文件内容为空'
      };
    }
    
    // 创建 formData 并添加文件
    const formData = new FormData();
    formData.append('file', fileContent);
    formData.append('type', fileType);
    
    // 调用API上传媒体文件
    const result = await post(`${BASE_URL}/api/v1/exercise/upload/${fileType}`, formData, {
      header: {
        'Content-Type': 'multipart/form-data'
      }
    });
    
    if (!result || !result.file_name) {
      throw new Error('上传失败，服务器未返回有效的文件名');
    }
    
    return {
      success: true,
      fileName: result.file_name
    };
  } catch (error) {
    console.error(`上传${fileType}失败:`, error);
    return {
      success: false,
      error: error.message || `上传${fileType}失败`
    };
  }
};

/**
 * 适配搜索动作API，处理返回数据格式
 * @param {Object} params - 搜索参数
 * @param {String} [params.keyword] - 搜索关键词
 * @param {Number} [params.body_part_id] - 身体部位ID
 * @param {Number} [params.equipment_id] - 器材ID
 * @param {Number} [params.muscle_id] - 目标肌肉ID
 * @param {String} [params.level] - 难度级别
 * @param {Number} [params.skip=0] - 分页起始位置
 * @param {Number} [params.limit=100] - 返回记录数量
 * @returns {Promise<Object>} 返回格式化后的搜索结果
 */
const searchExercisesList = async (params = {}) => {
  try {
    const limit = params.limit || 100; // 提高默认限制
    
    const apiParams = {
      keyword: params.keyword,
      body_part_id: params.body_part_id,
      equipment_id: params.equipment_id,
      muscle_id: params.muscle_id,
      level: params.level,
      skip: params.skip || 0,
      limit: limit
    };
    
    const exercises = await exerciseApi.searchExercises(apiParams);
    return formatExerciseListResponse(exercises, limit);
  } catch (error) {
    console.error('搜索动作失败:', error);
    return {
      success: false,
      errorMessage: error.message || '搜索动作失败'
    };
  }
};

// 导出包装好的兼容函数
module.exports = {
  // 原有云函数适配
  fetchExerciseList,
  fetchExerciseDetail,
  
  // 直接API访问
  getExercises: exerciseApi.getExercises,
  searchExercises: exerciseApi.searchExercises,
  searchExercisesList,
  createExercise: exerciseApi.createExercise,
  getExerciseDetail: exerciseApi.getExerciseDetail,
  
  // 媒体文件上传
  uploadExerciseMedia,
  
  // 添加点击量增加函数
  incrementHitTime: async (params = {}) => {
    try {
      if (!params.id) {
        return {
          success: false,
          error: '未提供动作ID'
        };
      }
      
      await exerciseApi.incrementHitTime(params.id);
      return {
        success: true
      };
    } catch (error) {
      console.error('增加点击量失败:', error);
      return {
        success: false,
        error: error.message || '增加点击量失败'
      };
    }
  },
  
  // 常量数据
  getMuscleCategories: exerciseApi.getMuscleCategories,
  getBodyPartCategories: exerciseApi.getBodyPartCategories,
  getEquipmentCategories: exerciseApi.getEquipmentCategories
}; 