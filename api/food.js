/**
 * 食物识别和分析相关API
 */

const { request, post, BASE_URL } = require('./request');

/**
 * 使用wx.uploadFile直接上传图片文件
 * @param {string} url - API路径
 * @param {string} filePath - 本地文件路径
 * @param {Object} formData - 额外的表单数据
 * @returns {Promise} 返回上传结果
 */
const uploadImage = (url, filePath, formData = {}) => {
  const token = wx.getStorageSync('token');
  
  console.log(`直接上传图片 ${filePath} 到 ${url}`);
  console.log('表单数据:', formData);
  
  return new Promise((resolve, reject) => {
    wx.uploadFile({
      url: BASE_URL + url,
      filePath: filePath,
      name: 'image', // 服务器接收的文件参数名
      formData: formData,
      header: {
        'Authorization': token ? `Bearer ${token}` : ''
        // uploadFile会自动设置Content-Type为multipart/form-data
      },
      success: (res) => {
        console.log(`上传响应状态码: ${res.statusCode}`);
        if (res.statusCode >= 200 && res.statusCode < 300) {
          console.log('图片上传成功');
          let data;
          try {
            data = JSON.parse(res.data);
          } catch (e) {
            data = res.data;
          }
          resolve(data);
        } else {
          console.error('图片上传失败:', res.statusCode, res.data);
          let errorData;
          try {
            errorData = JSON.parse(res.data);
          } catch (e) {
            errorData = res.data;
          }
          reject({
            statusCode: res.statusCode,
            message: '上传失败: ' + JSON.stringify(errorData),
            data: errorData
          });
        }
      },
      fail: (err) => {
        console.error('图片上传请求错误:', err);
        reject({
          message: '网络请求失败',
          originalError: err
        });
      }
    });
  });
};

/**
 * 专用于处理二进制图像数据的POST请求
 * @param {string} url - API路径
 * @param {Object} data - 请求数据对象，包含图像数据和其他参数
 * @returns {Promise} 返回请求结果
 */
const postWithImageFormData = (url, data) => {
  const token = wx.getStorageSync('token');
  console.log(`发送图像数据到 ${url} (使用JSON格式)`);
  
  return new Promise((resolve, reject) => {
    wx.request({
      url: BASE_URL + url,
      method: 'POST',
      data: data, // 直接使用数据对象
      header: {
        'Authorization': token ? `Bearer ${token}` : '',
        'Content-Type': 'application/json' // 使用JSON格式
      },
      success: (res) => {
        console.log(`API响应状态码: ${res.statusCode}`);
        if (res.statusCode >= 200 && res.statusCode < 300) {
          console.log('API调用成功，获取到数据');
          resolve(res.data);
        } else {
          console.error('API调用失败:', res.statusCode, res.data);
          reject({
            statusCode: res.statusCode,
            message: res.data && res.data.detail ? res.data.detail : '请求失败',
            data: res.data
          });
        }
      },
      fail: (err) => {
        console.error('API请求错误:', err);
        reject({
          message: '网络请求失败',
          originalError: err
        });
      }
    });
  });
};

/**
 * 将base64字符串转换为文件对象
 * @param {string} base64String - base64格式的图片数据
 * @returns {ArrayBuffer} ArrayBuffer形式的文件数据
 */
const base64ToArrayBuffer = (base64String) => {
  // 移除base64前缀
  const base64 = base64String.replace(/^data:image\/(png|jpeg|jpg);base64,/, '');
  return wx.base64ToArrayBuffer(base64);
};

/**
 * 从本地路径获取文件内容
 * @param {string} filePath - 本地文件路径
 * @returns {Promise<ArrayBuffer>} 文件内容
 */
const getFileContent = (filePath) => {
  return new Promise((resolve, reject) => {
    wx.getFileSystemManager().readFile({
      filePath: filePath,
      success: res => resolve(res.data),
      fail: err => reject(err)
    });
  });
};

/**
 * 将文件内容转换为base64字符串
 * @param {ArrayBuffer} fileData - 文件二进制数据
 * @returns {string} base64编码的字符串
 */
const arrayBufferToBase64 = (fileData) => {
  return wx.arrayBufferToBase64(fileData);
};

/**
 * 分析食物图片
 * @param {string|ArrayBuffer} imageData - 图片数据（base64字符串或ArrayBuffer）
 * @param {string} mealType - 餐食类型：breakfast/lunch/dinner/snack
 * @param {string} mealDate - 餐食日期，格式为YYYY-MM-DD，默认为当天
 * @param {string} imagePath - 图片本地路径（优先使用）
 * @returns {Promise} 返回分析结果
 */
const analyzeFoodImage = async (imageData, mealType, mealDate = null, imagePath = null) => {
  console.log(`Food API: 分析食物图片, 餐食类型=${mealType}, 餐食日期=${mealDate || '今天'}`);
  
  // 如果有图片路径，使用直接上传方式
  if (imagePath) {
    console.log('使用直接上传图片方式:', imagePath);
    
    // 准备表单数据 - 注意：meal_type必须通过表单传递
    const formData = {
      meal_type: mealType, // 必须是：breakfast, lunch, dinner, snack之一
    };
    
    if (mealDate) {
      formData.meal_date = mealDate; // 可选，格式必须为YYYY-MM-DD
    }
    
    // 可以添加优化选项
    formData.optimize = 'true'; // 可选，布尔值
    
    // 使用uploadFile直接上传图片，所有参数作为表单数据
    try {
      return await uploadImage('/api/v1/food_recognition/analyze', imagePath, formData);
    } catch (error) {
      console.error('上传图片失败:', error);
      throw error;
    }
  }
  
  // 如果没有图片路径，使用JSON请求
  // 准备请求数据
  const requestData = {
    meal_type: mealType, // 必须包含在请求体中
    image: '' // 将添加图片数据
  };
  
  if (mealDate) {
    requestData.meal_date = mealDate;
  }
  
  // 添加优化选项
  requestData.optimize = true;
  
  // 添加图片数据
  try {
    let base64Image;
    
    if (typeof imageData === 'string') {
      // 如果是base64字符串，确保没有前缀
      console.log('使用提供的base64字符串');
      base64Image = imageData.replace(/^data:image\/(png|jpeg|jpg);base64,/, '');
    } else {
      // 已经是ArrayBuffer，转换为base64
      console.log('将ArrayBuffer转换为base64字符串');
      base64Image = arrayBufferToBase64(imageData);
    }
    
    // 添加图片数据到请求体
    requestData.image = base64Image;
    
    console.log('使用JSON方式调用API: /api/v1/food_recognition/analyze');
    return postWithImageFormData('/api/v1/food_recognition/analyze', requestData);
    
  } catch (error) {
    console.error('准备图片数据失败:', error);
    throw error;
  }
};

/**
 * 确认食物识别结果
 * @param {number} recognitionId - 识别结果ID
 * @param {string|ArrayBuffer} originalImageData - 原始图片数据
 * @param {Array} foodItems - 食物项数组
 * @param {string} mealName - 餐食名称
 * @param {string} healthRecommendation - 健康建议
 * @param {Object} nutritionTotals - 营养总计
 * @param {string} mealDate - 餐食日期
 * @param {string} mealType - 餐食类型
 * @param {string} imagePath - 图片本地路径（优先使用）
 * @returns {Promise} 返回确认结果
 */
const confirmFoodRecognition = async (
  recognitionId, 
  originalImageData, 
  foodItems, 
  mealName, 
  healthRecommendation,
  nutritionTotals = null,
  mealDate = null,
  mealType = null,
  imagePath = null
) => {
  console.log(`Food API: 确认食物识别结果, ID=${recognitionId}`);
  
  // 如果有图片路径，使用直接上传方式
  if (imagePath) {
    console.log('使用直接上传图片方式确认:', imagePath);
    
    // 准备表单数据 - 所有数据都作为表单字段传递
    const formData = {
      food_items: JSON.stringify(foodItems),
      meal_name: mealName,
      health_recommendation: healthRecommendation
    };
    
    if (nutritionTotals) {
      formData.nutrition_totals = JSON.stringify(nutritionTotals);
    }
    
    if (mealDate) {
      formData.meal_date = mealDate;
    }
    
    if (mealType) {
      formData.meal_type = mealType;
    }
    
    // 使用uploadFile直接上传图片
    try {
      return await uploadImage(`/api/v1/food_recognition/${recognitionId}/confirm`, imagePath, formData);
    } catch (error) {
      console.error('上传图片失败:', error);
      throw error;
    }
  }
  
  // 如果没有图片路径，使用JSON方式
  // 准备请求数据
  const requestData = {
    food_items: foodItems,
    meal_name: mealName,
    health_recommendation: healthRecommendation
  };
  
  // 添加可选字段
  if (nutritionTotals) {
    requestData.nutrition_totals = nutritionTotals;
  }
  
  if (mealDate) {
    requestData.meal_date = mealDate;
  }
  
  if (mealType) {
    requestData.meal_type = mealType;
  }
  
  // 添加图片数据
  try {
    let base64Image;
    
    if (typeof originalImageData === 'string') {
      // 如果是base64字符串，确保没有前缀
      console.log('使用提供的base64字符串');
      base64Image = originalImageData.replace(/^data:image\/(png|jpeg|jpg);base64,/, '');
    } else if (originalImageData) {
      // 已经是ArrayBuffer，转换为base64
      console.log('将ArrayBuffer转换为base64字符串');
      base64Image = arrayBufferToBase64(originalImageData);
    }
    
    // 添加图片数据到请求
    if (base64Image) {
      requestData.image = base64Image;
    }
    
    console.log(`使用JSON方式调用确认API: /api/v1/food_recognition/${recognitionId}/confirm`);
    return postWithImageFormData(`/api/v1/food_recognition/${recognitionId}/confirm`, requestData);
    
  } catch (error) {
    console.error('准备图片数据失败:', error);
    throw error;
  }
};

/**
 * 上传食物图片到用户目录
 * @param {string} filePath - 本地文件路径
 * @param {Object} formData - 额外的表单数据，如food_name等
 * @returns {Promise} 返回上传结果，包含image_url
 */
const uploadFoodImage = (filePath, formData = {}) => {
  console.log(`Food API: 上传食物图片 ${filePath} 到用户目录`);
  
  // 使用已有的uploadImage函数，指定特定的API路径
  return uploadImage('/api/v1/user/upload-food-image', filePath, formData);
};

/**
 * 获取食物识别记录详情
 * @param {number|string} recognitionId - 识别记录ID
 * @returns {Promise} 返回识别记录详情
 */
const getFoodRecognitionById = (recognitionId) => {
  if (!recognitionId) {
    return Promise.reject(new Error('Missing recognition ID'));
  }
  
  console.log(`Food API: 获取食物识别记录详情, ID=${typeof recognitionId}`);
  
  return request(`/api/v1/food-recognition/${recognitionId}`);
};

module.exports = {
  analyzeFoodImage,
  confirmFoodRecognition,
  uploadImage,
  base64ToArrayBuffer,
  getFileContent,
  arrayBufferToBase64,
  getFoodRecognitionById,
  uploadFoodImage
}; 