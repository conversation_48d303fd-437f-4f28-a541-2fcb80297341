/**
 * 食物库相关API模块
 * @file foodbase.js
 * @description 连接服务器关于食物库的接口
 */

const { get, post, request, BASE_URL } = require('./request');

/**
 * 获取食品列表
 * @param {Object} options - 查询选项
 * @param {number} [options.skip=0] - 分页偏移
 * @param {number} [options.limit=100] - 每页数量
 * @param {string} [options.name] - 食品名称搜索
 * @param {string} [options.category] - 食品类别筛选
 * @param {string} [options.food_type] - 食品类型筛选
 * @returns {Promise<Array>} 返回食品搜索结果列表
 */
const getFoodList = async (options = {}) => {
  console.log('食物库API: 获取食品列表', options);
  
  try {
    // 构建查询参数
    const queryParams = [];
    
    if (options.skip !== undefined) {
      queryParams.push(`skip=${options.skip}`);
    }
    
    if (options.limit !== undefined) {
      queryParams.push(`limit=${options.limit}`);
    }
    
    if (options.name) {
      queryParams.push(`name=${encodeURIComponent(options.name)}`);
    }
    
    if (options.category) {
      queryParams.push(`category=${encodeURIComponent(options.category)}`);
    }
    
    if (options.food_type) {
      queryParams.push(`food_type=${encodeURIComponent(options.food_type)}`);
    }
    
    // 构建URL
    let url = '/api/v1/food/';
    if (queryParams.length > 0) {
      url += `?${queryParams.join('&')}`;
    }
    
    // 发送请求
    const result = await get(url);
    
    // 处理返回结果，添加BASE_URL到缩略图
    if (Array.isArray(result)) {
      return result.map(item => {
        if (item.thumb_image_url && !item.thumb_image_url.startsWith('http')) {
          item.thumb_image_url = BASE_URL + item.thumb_image_url;
        }
        return item;
      });
    }
    
    return result;
  } catch (error) {
    console.error('获取食品列表失败:', error);
    throw error;
  }
};

/**
 * 获取食品类别列表
 * @returns {Promise<Array<string>>} 返回类别字符串列表
 */
const getFoodCategories = async () => {
  console.log('食物库API: 获取食品类别列表');
  
  try {
    return await get('/api/v1/food/categories');
  } catch (error) {
    console.error('获取食品类别列表失败:', error);
    throw error;
  }
};

/**
 * 获取食品类型列表
 * @returns {Promise<Array<string>>} 返回类型字符串列表
 */
const getFoodTypes = async () => {
  console.log('食物库API: 获取食品类型列表');
  
  try {
    return await get('/api/v1/food/types');
  } catch (error) {
    console.error('获取食品类型列表失败:', error);
    throw error;
  }
};

/**
 * 通过ID获取食品详情
 * @param {number} foodId - 食品ID
 * @returns {Promise<Object>} 返回完整食品信息
 */
const getFoodById = async (foodId) => {
  console.log(`食物库API: 获取食品详情, ID=${foodId}`);
  
  try {
    const result = await get(`/api/v1/food/${foodId}`);
    
    // 处理图片URL，添加BASE_URL
    if (result) {
      if (result.large_image_url && !result.large_image_url.startsWith('http')) {
        result.large_image_url = BASE_URL + result.large_image_url;
      }
      
      if (result.thumb_image_url && !result.thumb_image_url.startsWith('http')) {
        result.thumb_image_url = BASE_URL + result.thumb_image_url;
      }
    }
    
    return result;
  } catch (error) {
    console.error(`获取食品详情失败, ID=${foodId}:`, error);
    throw error;
  }
};

/**
 * 通过编码获取食品详情
 * @param {string} code - 食品编码
 * @returns {Promise<Object>} 返回完整食品信息
 */
const getFoodByCode = async (code) => {
  console.log(`食物库API: 通过编码获取食品详情, 编码=${code}`);
  
  try {
    const result = await get(`/api/v1/food/code/${code}`);
    
    // 处理图片URL，添加BASE_URL
    if (result) {
      if (result.image_url && !result.image_url.startsWith('http')) {
        result.image_url = BASE_URL + result.image_url;
      }
      
      if (result.thumb_image_url && !result.thumb_image_url.startsWith('http')) {
        result.thumb_image_url = BASE_URL + result.thumb_image_url;
      }
    }
    
    return result;
  } catch (error) {
    console.error(`通过编码获取食品详情失败, 编码=${code}:`, error);
    throw error;
  }
};

/**
 * 创建新食品（管理员）
 * @param {Object} foodData - 食品创建数据
 * @returns {Promise<Object>} 返回创建的食品信息
 */
const createFood = async (foodData) => {
  console.log('食物库API: 创建新食品');
  
  try {
    return await post('/api/v1/food/', foodData);
  } catch (error) {
    console.error('创建新食品失败:', error);
    throw error;
  }
};

/**
 * 更新食品信息（管理员）
 * @param {number} foodId - 食品ID
 * @param {Object} updateData - 食品更新数据
 * @returns {Promise<Object>} 返回更新后的食品信息
 */
const updateFood = async (foodId, updateData) => {
  console.log(`食物库API: 更新食品信息, ID=${foodId}`);
  
  try {
    return await request(`/api/v1/food/${foodId}`, 'PUT', updateData);
  } catch (error) {
    console.error(`更新食品信息失败, ID=${foodId}:`, error);
    throw error;
  }
};

/**
 * 删除食品（管理员）
 * @param {number} foodId - 食品ID
 * @returns {Promise<Object>} 返回删除的食品信息
 */
const deleteFood = async (foodId) => {
  console.log(`食物库API: 删除食品, ID=${foodId}`);
  
  try {
    return await request(`/api/v1/food/${foodId}`, 'DELETE');
  } catch (error) {
    console.error(`删除食品失败, ID=${foodId}:`, error);
    throw error;
  }
};

/**
 * 基于名称搜索食品，使用高级相似度匹配算法
 * @param {string} name - 食品名称搜索关键词
 * @param {number} [limit=10] - 返回结果数量上限
 * @returns {Promise<Array>} 返回食品搜索结果列表
 */
const searchFoodsByName = async (name, limit = 10) => {
  console.log(`食物库API: 搜索食品，关键词="${name}", 限制=${limit}`);
  
  try {
    // 构建查询参数
    const queryParams = [
      `name=${encodeURIComponent(name)}`,
      `limit=${limit}`
    ];
    
    // 构建URL
    const url = `/api/v1/food/search?${queryParams.join('&')}`;
    
    // 发送请求
    const result = await get(url);
    
    // 处理返回结果，添加BASE_URL到缩略图
    if (Array.isArray(result)) {
      return result.map(item => {
        if (item.thumb_image_url && !item.thumb_image_url.startsWith('http')) {
          item.thumb_image_url = BASE_URL + item.thumb_image_url;
        }
        return item;
      });
    }
    
    return result;
  } catch (error) {
    console.error('搜索食品失败:', error);
    throw error;
  }
};

/**
 * 获取食物的详细营养素信息
 * @param {number} foodId - 食物ID
 * @param {string[]} nutrientNames - 要查询的营养素名称数组
 * @returns {Promise<Object>} 返回营养素详情
 */
const getFoodNutrients = async (foodId, nutrientNames) => {
  console.log(`食物库API: 获取食物营养素详情, ID=${foodId}, 营养素=${nutrientNames.join(', ')}`);
  
  try {
    if (!foodId) {
      throw new Error('缺少食物ID参数');
    }
    
    if (!Array.isArray(nutrientNames) || nutrientNames.length === 0) {
      throw new Error('营养素名称必须是非空数组');
    }
    
    // 构建请求数据
    const requestData = {
      food_id: foodId,
      nutrient_names: nutrientNames
    };
    
    // 发送POST请求到/api/v1/food/nutrients
    const result = await post('/api/v1/food/nutrients', requestData);
    
    return result;
  } catch (error) {
    console.error(`获取食物营养素详情失败, ID=${foodId}:`, error);
    throw error;
  }
};

module.exports = {
  getFoodList,
  getFoodCategories,
  getFoodTypes,
  getFoodById,
  getFoodByCode,
  createFood,
  updateFood,
  deleteFood,
  searchFoodsByName,
  getFoodNutrients
}; 