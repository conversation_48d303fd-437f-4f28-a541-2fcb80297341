/**
 * 用户中心相关API
 */

const { get, post, BASE_URL } = require('./request');

/**
 * 获取用户个人资料
 * @returns {Promise} 返回用户资料
 */
const getUserProfile = () => {
  return new Promise((resolve, reject) => {
    get('/api/v1/user/profile')
      .then(res => {
        // 处理响应数据，确保avatarUrl包含完整URL
        if (res && res.user) {
          // 处理avatarUrl
          if (res.user.avatarUrl && 
              typeof res.user.avatarUrl === 'string' && 
              !res.user.avatarUrl.startsWith('http') &&
              !res.user.avatarUrl.startsWith('data:')) {
            res.user.avatarUrl = `${BASE_URL}${res.user.avatarUrl}`;
            console.log('【获取用户资料】已处理avatarUrl:', res.user.avatarUrl);
          }
          
          // 处理avatar_url
          if (res.user.avatar_url && 
              typeof res.user.avatar_url === 'string' && 
              !res.user.avatar_url.startsWith('http') &&
              !res.user.avatar_url.startsWith('data:')) {
            res.user.avatar_url = `${BASE_URL}${res.user.avatar_url}`;
            console.log('【获取用户资料】已处理avatar_url:', res.user.avatar_url);
          }
        }
        
        resolve(res);
      })
      .catch(err => {
        reject(err);
      });
  });
};

/**
 * 根据openid检查用户是否存在
 * @param {String} openid - 用户的openid
 * @returns {Promise} 返回用户信息或错误
 */
const checkUserByOpenid = (openid) => {
  return get(`/api/v1/user/check?openid=${openid}`);
};

/**
 * 更新用户个人资料
 * @param {Object} data - 个人资料数据
 * @returns {Promise} 返回更新结果
 */
const updateUserProfile = (data) => {
  return new Promise((resolve, reject) => {
    // 检查是否有头像base64数据
    if (data.avatar_url && typeof data.avatar_url === 'string' && data.avatar_url.startsWith('data:')) {
      console.log('个人资料中包含base64头像数据，使用Form格式发送');
      
      // 从data中提取avatar_url单独处理
      const avatarBase64 = data.avatar_url;
      delete data.avatar_url;
      
      // 先上传头像，再更新其他资料
      uploadAvatar(avatarBase64)
        .then(avatarRes => {
          console.log('头像上传成功:', avatarRes);
          
          // 将返回的avatar_url添加到用户资料中
          if (avatarRes && avatarRes.avatar_url) {
            data.avatar_url = avatarRes.avatar_url;
          }
          
          // 继续更新其他资料
          return post('/api/v1/user/profile', data);
        })
        .then(res => {
          processUserResponse(res, resolve);
        })
        .catch(err => {
          console.error('更新资料失败:', err);
          reject(err);
        });
    } else {
      // 没有头像数据，直接更新资料
      post('/api/v1/user/profile', data)
        .then(res => {
          processUserResponse(res, resolve);
        })
        .catch(err => {
          reject(err);
        });
    }
  });
};

// 处理用户响应数据中的URL
function processUserResponse(res, resolve) {
  // 处理响应数据，确保avatarUrl包含完整URL
  if (res && res.user) {
    // 处理avatarUrl
    if (res.user.avatarUrl && 
        typeof res.user.avatarUrl === 'string' && 
        !res.user.avatarUrl.startsWith('http') &&
        !res.user.avatarUrl.startsWith('data:')) {
      res.user.avatarUrl = `${BASE_URL}${res.user.avatarUrl}`;
      console.log('【更新用户资料】已处理avatarUrl:', res.user.avatarUrl);
    }
    
    // 处理avatar_url
    if (res.user.avatar_url && 
        typeof res.user.avatar_url === 'string' && 
        !res.user.avatar_url.startsWith('http') &&
        !res.user.avatar_url.startsWith('data:')) {
      res.user.avatar_url = `${BASE_URL}${res.user.avatar_url}`;
      console.log('【更新用户资料】已处理avatar_url:', res.user.avatar_url);
    }
  }
  
  resolve(res);
}

/**
 * 上传用户头像
 * @param {String} filePath - 头像文件路径、URL或base64数据
 * @returns {Promise} 返回上传结果
 */
const uploadAvatar = (filePath) => {
  return new Promise((resolve, reject) => {
    // 如果是base64数据（以data:开头），直接提交数据
    if (typeof filePath === 'string' && filePath.startsWith('data:')) {
      console.log('提交头像base64数据');
      
      // 使用FormData格式提交而不是JSON
      console.log('使用FormData格式发送base64数据到服务器');
      
      // 获取token
      const token = wx.getStorageSync('token');
      if (!token) {
        return reject(new Error('未登录'));
      }
      
      console.log('发送到服务器的头像数据:', {
        API: '/api/v1/user/avatar',
        请求方法: 'POST',
        数据格式: 'Form',
        字段名: 'avatar_url',
        数据类型: 'base64',
        数据长度: filePath.length,
        数据前缀: filePath.substring(0, 30) + '...'
      });
      
      wx.request({
        url: `${BASE_URL}/api/v1/user/avatar`,
        method: 'POST',
        header: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/x-www-form-urlencoded'
        },
        data: {
          avatar_url: filePath
        },
        success: (res) => {
          console.log('头像base64提交响应:', res.statusCode);
          
          if (res.statusCode >= 200 && res.statusCode < 300) {
            console.log('头像base64提交成功:', res.data);
            
            // 处理响应数据中的头像URL
            if (res.data && res.data.avatar_url && 
                typeof res.data.avatar_url === 'string' && 
                !res.data.avatar_url.startsWith('http') &&
                !res.data.avatar_url.startsWith('data:')) {
              res.data.avatar_url = `${BASE_URL}${res.data.avatar_url}`;
              console.log('【上传头像base64】已处理返回的avatar_url:', res.data.avatar_url);
            }
            
            // 同样处理avatarUrl字段（如果存在）
            if (res.data && res.data.avatarUrl && 
                typeof res.data.avatarUrl === 'string' && 
                !res.data.avatarUrl.startsWith('http') &&
                !res.data.avatarUrl.startsWith('data:')) {
              res.data.avatarUrl = `${BASE_URL}${res.data.avatarUrl}`;
              console.log('【上传头像base64】已处理返回的avatarUrl:', res.data.avatarUrl);
            }
            
            resolve(res.data);
          } else {
            console.error('头像base64提交失败:', res.statusCode, res.data);
            reject({
              statusCode: res.statusCode,
              message: res.data && res.data.detail ? res.data.detail : '上传头像失败',
              data: res.data
            });
          }
        },
        fail: (err) => {
          console.error('头像base64提交请求失败:', err);
          reject(err);
        }
      });
      
      return;
    }
    
    // 如果是http/https链接，直接提交URL而不是上传文件
    if (typeof filePath === 'string' && (filePath.startsWith('http://') || filePath.startsWith('https://'))) {
      console.log('提交头像URL:', filePath);
      
      // 直接调用API，提交URL而不是上传文件
      post('/api/v1/user/avatar', { avatar_url: filePath })
        .then(res => {
          console.log('头像URL提交成功:', res);
          
          // 处理响应数据中的头像URL
          if (res && res.avatar_url && 
              typeof res.avatar_url === 'string' && 
              !res.avatar_url.startsWith('http') &&
              !res.avatar_url.startsWith('data:')) {
            res.avatar_url = `${BASE_URL}${res.avatar_url}`;
            console.log('【上传头像URL】已处理返回的avatar_url:', res.avatar_url);
          }
          
          // 同样处理avatarUrl字段（如果存在）
          if (res && res.avatarUrl && 
              typeof res.avatarUrl === 'string' && 
              !res.avatarUrl.startsWith('http') &&
              !res.avatarUrl.startsWith('data:')) {
            res.avatarUrl = `${BASE_URL}${res.avatarUrl}`;
            console.log('【上传头像URL】已处理返回的avatarUrl:', res.avatarUrl);
          }
          
          resolve(res);
        })
        .catch(err => {
          console.error('头像URL提交失败:', err);
          reject(err);
        });
      
      return;
    }
    
    // 检查文件路径是否有效
    if (!filePath || typeof filePath !== 'string') {
      console.error('无效的文件路径:', filePath);
      return reject(new Error('无效的文件路径'));
    }
    
    console.log('准备上传头像文件:', filePath);
    
    // 检查token是否过期或将要过期
    const tokenExpiresAt = wx.getStorageSync('token_expires_at');
    let isExpired = false;
    
    if (tokenExpiresAt) {
      try {
        const expiresAt = new Date(tokenExpiresAt);
        const now = new Date();
        // 检查是否过期或即将过期（30分钟内）
        const thirtyMinutesLater = new Date(now.getTime() + 30 * 60 * 1000);
        isExpired = expiresAt <= thirtyMinutesLater;
        
        if (isExpired) {
          console.log('【上传头像】Token即将过期，尝试刷新');
        }
      } catch (error) {
        console.error('【上传头像】解析过期时间失败:', error);
      }
    } else {
      // 没有过期时间，假设可能过期
      isExpired = true;
    }
    
    const doUpload = (currentToken) => {
      // 上传文件
      wx.uploadFile({
        url: `${BASE_URL}/api/v1/user/avatar`,
        filePath: filePath,
        name: 'avatar',
        header: {
          'Authorization': `Bearer ${currentToken}`
        },
        formData: {
          // 可以添加额外的表单数据
          upload_type: 'avatar'
        },
        success: (res) => {
          console.log('上传响应:', res.statusCode, res.data);
          try {
            const data = JSON.parse(res.data);
            if (res.statusCode === 200) {
              // 处理响应数据中的头像URL
              if (data && data.avatar_url && 
                  typeof data.avatar_url === 'string' && 
                  !data.avatar_url.startsWith('http') &&
                  !data.avatar_url.startsWith('data:')) {
                data.avatar_url = `${BASE_URL}${data.avatar_url}`;
                console.log('【上传头像】已处理返回的avatar_url:', data.avatar_url);
              }
              
              // 同样处理avatarUrl字段（如果存在）
              if (data && data.avatarUrl && 
                  typeof data.avatarUrl === 'string' && 
                  !data.avatarUrl.startsWith('http') &&
                  !data.avatarUrl.startsWith('data:')) {
                data.avatarUrl = `${BASE_URL}${data.avatarUrl}`;
                console.log('【上传头像】已处理返回的avatarUrl:', data.avatarUrl);
              }
              
              resolve(data);
            } else if (res.statusCode === 401) {
              // 如果token无效，尝试刷新后重试
              console.log('【上传头像】Token已过期，尝试刷新');
              
              const authApi = require('./auth');
              authApi.refreshToken(token)
                .then(newTokenData => {
                  console.log('【上传头像】Token刷新成功，重试上传');
                  
                  // 保存新token
                  wx.setStorageSync('token', newTokenData.token);
                  if (newTokenData.expires_at) {
                    wx.setStorageSync('token_expires_at', newTokenData.expires_at);
                  }
                  
                  // 重新上传
                  doUpload(newTokenData.token);
                })
                .catch(refreshErr => {
                  console.error('【上传头像】Token刷新失败，需要重新登录:', refreshErr);
                  reject({
                    message: '登录已过期，请重新登录',
                    originalError: refreshErr
                  });
                });
            } else {
              console.error('上传失败，状态码:', res.statusCode, '错误信息:', data);
              reject(data);
            }
          } catch (error) {
            console.error('解析响应数据失败:', error);
            reject(error);
          }
        },
        fail: (error) => {
          console.error('上传请求失败:', error);
          reject(error);
        }
      });
    };
    
    // 如果token过期或将要过期，先尝试刷新
    if (isExpired) {
      const authApi = require('./auth');
      authApi.refreshToken(token)
        .then(newTokenData => {
          console.log('【上传头像】Token刷新成功');
          
          // 保存新token
          wx.setStorageSync('token', newTokenData.token);
          if (newTokenData.expires_at) {
            wx.setStorageSync('token_expires_at', newTokenData.expires_at);
          }
          
          // 使用新token上传
          doUpload(newTokenData.token);
        })
        .catch(err => {
          console.error('【上传头像】Token刷新失败，尝试使用原token:', err);
          
          // 刷新失败仍然尝试使用原token
          doUpload(token);
        });
    } else {
      // 直接使用原token
      doUpload(token);
    }
  });
};

/**
 * 获取用户设置
 * @returns {Promise} 返回用户设置
 */
const getUserSettings = () => {
  return get('/api/v1/user/settings');
};

/**
 * 更新用户设置
 * @param {Object} data - 设置数据
 * @returns {Promise} 返回更新结果
 */
const updateUserSettings = (data) => {
  return post('/api/v1/user/settings', data);
};

/**
 * 生成分享二维码
 * @param {Object} data - 包含page、scene和width的对象
 * @returns {Promise} 返回二维码URL
 */
const generateQrCode = (data) => {
  return new Promise((resolve, reject) => {
    post('/api/v1/qrcode/generate', data)
      .then(res => {
        // 处理响应数据，确保qrCodeUrl包含完整URL
        if (res && res.url) {
          // 检查URL是否是相对路径
          if (typeof res.url === 'string' && 
              !res.url.startsWith('http') &&
              !res.url.startsWith('data:')) {
            res.url = `${BASE_URL}${res.url}`;
            console.log('【生成二维码】已处理QR码URL:', res.url);
          }
          
          // 保存到应用全局QR码存储
          const app = getApp();
          if (app && app.storeQrCodeUrl) {
            app.storeQrCodeUrl(res.url);
            console.log('【生成二维码】已使用storeQrCodeUrl保存QR码URL');
          } else if (app && app.globalData) {
            // 兼容旧版本，如果没有storeQrCodeUrl方法
            app.globalData.qrCodeUrl = res.url;
            console.log('【生成二维码】已保存QR码URL到全局变量');
          }
        }
        
        resolve(res);
      })
      .catch(err => {
        reject(err);
      });
  });
};

/**
 * 记录分享数据
 * @param {Object} data - 分享数据
 * @returns {Promise} 返回记录结果
 */
const trackShare = (data) => {
  return post('/api/v1/share/track', data);
};

module.exports = {
  getUserProfile,
  updateUserProfile,
  uploadAvatar,
  getUserSettings,
  updateUserSettings,
  generateQrCode,
  trackShare,
  checkUserByOpenid
}; 