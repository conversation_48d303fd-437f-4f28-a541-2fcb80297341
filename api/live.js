/**
 * API请求模块
 * 封装与后端的通信功能
 */

// API基础URL，生产环境中应替换为实际域名
const API_BASE_URL = 'https://api.example.com';

/**
 * 上传视频到后端处理
 * @param {String} filePath - 视频文件路径
 * @param {Object} options - 上传选项
 * @param {String} options.quality - 视频质量 (high/medium/low)
 * @param {String} options.outputFormat - 输出格式 (livephoto/mp4/gif)
 * @param {String} options.title - 视频标题
 * @param {String} options.description - 视频描述
 * @returns {Promise} 上传结果Promise
 */
function uploadVideo(filePath, options = {}) {
  return new Promise((resolve, reject) => {
    wx.showLoading({
      title: '上传中...',
      mask: true
    });
    
    wx.uploadFile({
      url: `${API_BASE_URL}/api/process_video`,
      filePath: filePath,
      name: 'video',
      formData: {
        quality: options.quality || 'high',
        outputFormat: options.outputFormat || 'livephoto',
        title: options.title || '视频分享',
        description: options.description || '通过小程序分享'
      },
      success: (res) => {
        wx.hideLoading();
        
        if (res.statusCode === 200) {
          try {
            // 解析响应数据
            const result = JSON.parse(res.data);
            resolve(result);
          } catch (error) {
            reject(new Error('解析响应失败'));
          }
        } else {
          reject(new Error(`服务器错误 (${res.statusCode})`));
        }
      },
      fail: (error) => {
        wx.hideLoading();
        reject(error);
      }
    });
  });
}

/**
 * 下载处理后的文件
 * @param {String} fileUrl - 文件URL
 * @returns {Promise} 下载结果Promise
 */
function downloadFile(fileUrl) {
  return new Promise((resolve, reject) => {
    wx.showLoading({
      title: '下载中...',
      mask: true
    });
    
    wx.downloadFile({
      url: fileUrl,
      success: (res) => {
        wx.hideLoading();
        
        if (res.statusCode === 200) {
          resolve(res.tempFilePath);
        } else {
          reject(new Error(`下载失败 (${res.statusCode})`));
        }
      },
      fail: (error) => {
        wx.hideLoading();
        reject(error);
      }
    });
  });
}

/**
 * 预览文件
 * @param {String} filePath - 文件路径
 * @param {String} fileType - 文件类型 (image/video)
 */
function previewFile(filePath, fileType = 'video') {
  if (fileType === 'image') {
    wx.previewImage({
      urls: [filePath]
    });
  } else {
    wx.openDocument({
      filePath: filePath,
      showMenu: true,
      success: () => {
        console.log('打开文件成功');
      },
      fail: (error) => {
        console.error('打开文件失败:', error);
        wx.showToast({
          title: '打开文件失败',
          icon: 'none'
        });
      }
    });
  }
}

module.exports = {
  uploadVideo,
  downloadFile,
  previewFile
};
