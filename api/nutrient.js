/**
 * 营养素相关API接口
 * @file nutrient.js
 * @description 提供营养素相关的API调用方法
 */
const { post } = require('./request');

/**
 * 获取每日营养素推荐摄入量
 * @param {number} age - 用户年龄(岁)
 * @param {string} sex - 性别，只能是'male'或'female'
 * @param {string} [pregnancy_stage] - 孕期阶段: 'early'(早期)、'mid'(中期)、'late'(晚期)、'lactation'(哺乳期)或不提供
 * @param {Array<string>} [dietary_names] - 营养素字段名称列表，如'vitamin_a'、'phosphor'等
 * @returns {Promise<Object>} 营养素推荐摄入量数据
 */
function getDailyNutrientTargets(age, sex, pregnancy_stage, dietary_names) {
  console.log('[API] getDailyNutrientTargets 参数:', { age, sex, pregnancy_stage, dietary_names });
  
  // 构建请求数据
  const requestData = {
    age,
    sex,
    ...(pregnancy_stage ? { pregnancy_stage } : {}),
    ...(dietary_names && dietary_names.length > 0 ? { dietary_names } : {})
  };
  
  // 使用post方法发送请求，与meal.js保持一致
  return post('/api/v1/nutrient/daily-target', requestData);
}

// 确保模块正确导出
const nutrientApi = {
  getDailyNutrientTargets
};

// 使用两种方式导出，确保兼容性
module.exports = nutrientApi; 