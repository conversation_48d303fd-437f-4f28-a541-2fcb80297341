/**
 * 封装微信请求方法
 */

// 开发环境配置
const DEV_CONFIG = {
  // 禁用模拟数据，使用真实服务器
  USE_MOCK_DATA: false,
  // 模拟延迟(毫秒)
  MOCK_DELAY: 800
};

// API基础URL - 自动判断环境
// 获取当前环境
let BASE_URL = '';
let IS_DEV_ENV = false;

// 设置环境
try {
  // 判断是否为开发环境
  if (wx.getAccountInfoSync) {
    const accountInfo = wx.getAccountInfoSync();
    const env = accountInfo.miniProgram.envVersion;
    console.log('【环境】当前小程序环境:', env);

    // 设置开发环境标志
    IS_DEV_ENV = env === 'develop';

    // 判断是否为开发者工具
    const isDevTools = wx.getSystemInfoSync().platform === 'devtools';

    // 根据环境设置BASE_URL
    if (env === 'develop') {
      // 开发环境：模拟器使用localhost，真机使用IP
      if (isDevTools) {
        BASE_URL = 'http://**************:8000';
      } else {
        BASE_URL = 'http://**************:8000'; // 更新为您电脑的实际IP地址
      }
    } else if (env === 'trial') {
      // 体验版环境
      BASE_URL = 'https://api.yourdomain.com';
    } else {
      // 正式环境
      BASE_URL = 'https://api.yourdomain.com';
    }
  } else {
    // 默认本地开发环境
    BASE_URL = 'http://**************:8000';
  }
} catch (e) {
  console.error('【环境】获取环境信息失败:', e);
  // 出错时使用默认地址
  BASE_URL = 'https://sciencefit.site';
}
BASE_URL = 'https://sciencefit.site';
console.log('【环境】API基础地址:', BASE_URL);
console.log('【环境】是否为开发环境:', IS_DEV_ENV);
console.log('【环境】是否使用模拟数据:', IS_DEV_ENV && DEV_CONFIG.USE_MOCK_DATA);

// 当auth.js加载后，我们才能导入authApi，避免循环依赖
let authApi;
const getAuthApi = () => {
  if (!authApi) {
    authApi = require('./auth');
  }
  return authApi;
};

/**
 * 测试后端接口连接
 * @returns {Promise} 返回连接测试结果
 */
const testConnection = () => {
  console.log('【接口测试】正在测试API连接:', BASE_URL);

  return new Promise((resolve, reject) => {
    wx.request({
      url: `${BASE_URL}/api/v1/ping`,
      method: 'GET',
      timeout: 5000,
      success: (res) => {
        console.log('【接口测试】响应状态码:', res.statusCode);
        if (res.statusCode >= 200 && res.statusCode < 300) {
          console.log('【接口测试】API连接正常');
          resolve(true);
        } else {
          console.error('【接口测试】API连接异常, 状态码:', res.statusCode);
          reject(false);
        }
      },
      fail: (err) => {
        console.error('【接口测试】请求失败:', JSON.stringify(err));
        reject(err);
      }
    });
  });
};

/**
 * 检查令牌是否需要刷新
 * @returns {Boolean} 如果令牌需要刷新，返回true
 */
const shouldRefreshToken = () => {
  const tokenExpiresAt = wx.getStorageSync('token_expires_at');

  // 如果没有过期时间，默认需要刷新
  if (!tokenExpiresAt) {
    return true;
  }

  try {
    // 将过期时间转换为日期对象
    const expiresAt = new Date(tokenExpiresAt);
    const now = new Date();

    // 如果过期时间小于当前时间，或者在30分钟内过期，则需要刷新
    const thirtyMinutesLater = new Date(now.getTime() + 30 * 60 * 1000);

    // console.log('【token检查】当前时间:', now.toISOString());
    // console.log('【token检查】过期时间:', expiresAt.toISOString());
    // console.log('【token检查】30分钟后:', thirtyMinutesLater.toISOString());

    return expiresAt <= thirtyMinutesLater;
  } catch (error) {
    console.error('【token检查】解析过期时间失败:', error);
    return true;
  }
};

/**
 * 发送HTTP请求
 * @param {String} url - API路径
 * @param {String} method - 请求方法
 * @param {Object} data - 请求数据
 * @returns {Promise} 返回请求结果
 */
const request = (url, method, data) => {
  return new Promise((resolve, reject) => {
    const token = wx.getStorageSync('token');

    // 检查是否需要刷新token
    if (token && shouldRefreshToken()) {
      console.log('【请求】Token即将过期，尝试刷新');

      // 刷新token
      getAuthApi().refreshToken(token)
        .then(newTokenData => {
          // 保存新token
          wx.setStorageSync('token', newTokenData.token);

          // 使用新token发送请求
          makeRequest(newTokenData.token);
        })
        .catch(err => {
          console.error('【请求】刷新token失败:', err);

          // token刷新失败，仍然尝试使用原token发送请求
          makeRequest(token);
        });
    } else {
      // 直接使用现有token发送请求
      makeRequest(token);
    }

    function makeRequest(authToken) {
      // 打印请求详情
      console.log('【请求详情】', {
        url: BASE_URL + url,
        method: method,
        data: data,
        headers: {
          'Authorization': authToken ? `Bearer ${authToken}` : '',
          'Content-Type': 'application/json'
        }
      });

      wx.request({
        url: BASE_URL + url,
        method: method,
        data: data,
        header: {
          'Authorization': authToken ? `Bearer ${authToken}` : '',
          'Content-Type': 'application/json'
        },
        success: (res) => {
          // 处理401错误(未授权)，尝试刷新token
          if (res.statusCode === 401) {
            console.log('【请求】收到401未授权响应，尝试刷新token');

            if (!authToken) {
              console.error('【请求】无token可刷新，需要重新登录');
              redirectToLogin();
              reject({ message: '未登录或登录已过期' });
              return;
            }

            // 刷新token并重试
            getAuthApi().refreshToken(authToken)
              .then(newTokenData => {
                console.log('【请求】token刷新成功，重试请求');

                // 保存新token
                wx.setStorageSync('token', newTokenData.token);
                if (newTokenData.expires_at) {
                  wx.setStorageSync('token_expires_at', newTokenData.expires_at);
                }

                // 使用新token重试请求
                return makeRequest(newTokenData.token);
              })
              .catch(err => {
                console.error('【请求】token刷新失败，需要重新登录:', err);
                redirectToLogin();
                reject({ message: '登录已过期，请重新登录' });
              });
          } else if (res.statusCode >= 200 && res.statusCode < 300) {
            resolve(res.data);
          } else {
            console.error('【请求】请求失败:', res.statusCode, res.data);
            reject({
              statusCode: res.statusCode,
              message: res.data && res.data.detail ? res.data.detail : '请求失败',
              data: res.data
            });
          }
        },
        fail: (err) => {
          console.error('【请求】请求错误:', err);

          let errorMsg = '网络请求失败';
          if (err.errMsg) {
            if (err.errMsg.includes('timeout')) {
              errorMsg = '请求超时';
            } else if (err.errMsg.includes('fail')) {
              errorMsg = '网络连接失败';
            }
          }

          reject({
            message: errorMsg,
            originalError: err
          });
        }
      });
    }

    function redirectToLogin() {
      // 使用app.js中的redirectToLogin方法，避免重复实现
      const app = getApp();
      if (app && app.redirectToLogin) {
        console.log('【请求】使用app.redirectToLogin方法跳转到登录页面');
        app.redirectToLogin();
      } else {
        console.log('【请求】app.redirectToLogin不可用，使用备用方法');
        // 清除登录状态
        wx.removeStorageSync('token');
        wx.removeStorageSync('token_expires_at');
        wx.removeStorageSync('userInfo');

        // 更新全局状态
        if (app && app.globalData) {
          app.globalData.isLoggedIn = false;
          app.globalData.token = null;
          app.globalData.userInfo = null;
        }

        // 跳转到登录页
        wx.navigateTo({
          url: '/pages/login/login',
          fail: () => {
            // 如果navigateTo失败，可能是在tabBar页面，使用reLaunch
            wx.reLaunch({
              url: '/pages/login/login'
            });
          }
        });
      }
    }
  });
};

// 添加GET方法便捷调用
const get = (url, data = {}, header = {}) => {
  return request(url, 'GET', data);
};

// 添加POST方法便捷调用
const post = (url, data = {}, header = {}) => {
  return request(url, 'POST', data);
};

// 添加DELETE方法便捷调用
const del = (url, data = {}, header = {}) => {
  return request(url, 'DELETE', data);
};

// 添加PUT方法便捷调用
const put = (url, data = {}, header = {}) => {
  console.log('【PUT请求】准备发送PUT请求:', {
    url: url,
    data: data,
    header: header
  });
  return request(url, 'PUT', data);
};

module.exports = {
  request,
  get,
  post,
  del,
  put,
  BASE_URL,
  IS_DEV_ENV,
  DEV_CONFIG,
  testConnection
};
