const request = require('./request');

/**
 * Team Training API module
 * Provides functions to interact with team training endpoints
 */

const BASE_URL = '/api/v1';

/**
 * @typedef {Object} TrainingTemplate
 * @property {number} id - Template ID
 * @property {number} team_id - Team ID
 * @property {string} name - Template name
 * @property {string} description - Template description
 * @property {number} duration_weeks - Duration in weeks
 * @property {number} sessions_per_week - Number of sessions per week
 * @property {number} difficulty_level - Difficulty level (1-5)
 * @property {string} target_audience - Target audience description
 * @property {Array<number>} equipment_required - Required equipment IDs
 * @property {boolean} is_public - Whether the template is public
 * @property {string} created_at - Creation timestamp
 * @property {string} updated_at - Last update timestamp
 */

/**
 * @typedef {Object} TemplateExercise
 * @property {number} day - Day number
 * @property {number} exercise_id - Exercise ID
 * @property {number} sets - Number of sets
 * @property {string} reps - Rep scheme (e.g. "8-10", "12")
 * @property {number} [rest_seconds] - Rest time in seconds
 * @property {string} [notes] - Exercise notes
 */

/**
 * @typedef {Object} ClientPlan
 * @property {number} id - Plan ID
 * @property {number} client_relation_id - Client relation ID
 * @property {number} training_plan_id - Training plan template ID
 * @property {string} status - Plan status
 * @property {string} start_date - Start date
 * @property {string} end_date - End date
 * @property {Object} scheduled_time - Scheduled times by day of week (1-7)
 * @property {string} notes - Additional notes
 * @property {string} created_at - Creation timestamp
 * @property {string} updated_at - Last update timestamp
 */

/**
 * @typedef {Object} TrainingSession
 * @property {number} id - Session ID
 * @property {number} plan_id - Training plan ID
 * @property {number} client_id - Client ID
 * @property {number} coach_id - Coach ID
 * @property {string} status - Session status
 * @property {string} scheduled_date - Scheduled date and time
 * @property {string} [start_time] - Actual start time
 * @property {string} [end_time] - Actual end time
 * @property {string} [notes] - Session notes
 */

/**
 * Create a training plan template for a team
 * @param {number} teamId - Team ID
 * @param {Object} params - Template parameters
 * @param {string} params.name - Template name
 * @param {string} [params.description] - Template description
 * @param {number} params.duration_weeks - Duration in weeks
 * @param {number} params.sessions_per_week - Sessions per week
 * @param {number} [params.difficulty_level] - Difficulty level (1-5)
 * @param {string} [params.target_audience] - Target audience
 * @param {Array<number>} [params.equipment_required] - Required equipment IDs
 * @param {boolean} [params.is_public] - Whether the template is public
 * @param {Array<TemplateExercise>} params.exercises - Exercises for the template
 * @returns {Promise<TrainingTemplate>} Created template
 */
function createTeamTemplate(teamId, params) {
  return request.post(`${BASE_URL}/teams/${teamId}/templates`, params);
}

/**
 * Get team training templates
 * @param {number} teamId - Team ID
 * @returns {Promise<Array<TrainingTemplate>>} List of team templates
 */
function getTeamTemplates(teamId) {
  return request.get(`${BASE_URL}/teams/${teamId}/templates`);
}

/**
 * Create a training plan for a client
 * @param {number} clientRelationId - Client relation ID
 * @param {Object} params - Plan parameters
 * @param {number} params.training_plan_id - Training template ID
 * @param {string} params.start_date - Start date (ISO format)
 * @param {string} params.end_date - End date (ISO format)
 * @param {Object} params.scheduled_time - Scheduled times by day of week (1-7)
 * @param {string} [params.notes] - Additional notes
 * @returns {Promise<ClientPlan>} Created client plan
 */
function createClientPlan(clientRelationId, params) {
  return request.post(`${BASE_URL}/clients/${clientRelationId}/training-plans`, params);
}

/**
 * Get client training plans
 * @param {number} clientRelationId - Client relation ID
 * @param {Object} [params] - Query parameters
 * @param {string} [params.status] - Filter by status
 * @returns {Promise<Array<ClientPlan>>} List of client plans
 */
function getClientPlans(clientRelationId, params = {}) {
  return request.get(`${BASE_URL}/clients/${clientRelationId}/training-plans`, { data: params });
}

/**
 * Update client training plan
 * @param {number} planId - Plan ID
 * @param {Object} params - Update parameters
 * @param {string} [params.status] - New status
 * @param {string} [params.end_date] - New end date
 * @param {Object} [params.scheduled_time] - New scheduled times
 * @param {string} [params.notes] - New notes
 * @returns {Promise<ClientPlan>} Updated client plan
 */
function updateClientPlan(planId, params) {
  return request.put(`${BASE_URL}/clients/training-plans/${planId}`, params);
}

/**
 * Get training sessions for a plan
 * @param {number} planId - Plan ID
 * @param {Object} [params] - Query parameters
 * @param {string} [params.status] - Filter by status
 * @returns {Promise<Array<TrainingSession>>} List of training sessions
 */
function getPlanSessions(planId, params = {}) {
  return request.get(`${BASE_URL}/training-plans/${planId}/sessions`, { data: params });
}

/**
 * Start training session
 * @param {number} sessionId - Session ID
 * @returns {Promise<TrainingSession>} Started session
 */
function startSession(sessionId) {
  return request.post(`${BASE_URL}/sessions/${sessionId}/start`);
}

/**
 * Record training set for a session exercise
 * @param {number} sessionId - Session ID
 * @param {number} exerciseId - Exercise ID
 * @param {Object} params - Record parameters
 * @param {number} params.weight - Weight used
 * @param {number} params.reps - Repetitions completed
 * @param {number} [params.rpe] - Rate of Perceived Exertion (1-10)
 * @param {string} [params.notes] - Additional notes
 * @returns {Promise<Object>} Recorded set info
 */
function recordSessionSet(sessionId, exerciseId, params) {
  return request.post(`${BASE_URL}/sessions/${sessionId}/exercises/${exerciseId}/records`, params);
}

/**
 * Complete training session
 * @param {number} sessionId - Session ID
 * @param {Object} params - Completion parameters
 * @param {string} [params.feedback] - Client feedback
 * @param {number} [params.mood_rating] - Mood rating (1-5)
 * @param {number} [params.difficulty_rating] - Difficulty rating (1-5)
 * @param {string} [params.notes] - Additional notes
 * @returns {Promise<TrainingSession>} Completed session
 */
function completeSession(sessionId, params) {
  return request.put(`${BASE_URL}/sessions/${sessionId}/complete`, params);
}

module.exports = {
  createTeamTemplate,
  getTeamTemplates,
  createClientPlan,
  getClientPlans,
  updateClientPlan,
  getPlanSessions,
  startSession,
  recordSessionSet,
  completeSession
}; 