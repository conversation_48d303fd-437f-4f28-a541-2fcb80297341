const { request, post, del, BASE_URL } = require('./request'); // Assuming 'del' exists or is added for DELETE
const messageProcessor = require('../utils/messageProcessor');
const API_VERSION = 'v2';  // 可切换为'v1'以回退到旧版
// const BASE_URL = `/api/${API_VERSION}/chat`;
// Removed activeSessions as connection state is now managed by the page
// Cache for recent conversations to prevent redundant API calls
const recentConversationsCache = {
  data: null,
  timestamp: 0,
  cacheLifetime: 5 * 60 * 1000, // 5 minutes in milliseconds
  isExpired() {
    return !this.data || (Date.now() - this.timestamp > this.cacheLifetime);
  },
  update(data) {
    this.data = data;
    this.timestamp = Date.now();
  },
  clear() {
    this.data = null;
    this.timestamp = 0;
  }
};

/**
 * 处理训练计划中的URL
 * @param {Object} plan - 训练计划对象
 * @returns {Object} 处理后的训练计划对象
 */
const processTrainingPlanUrls = (plan) => {
  if (!plan) return plan;

  const processedPlan = { ...plan };
  if (plan.exercises) {
    processedPlan.exercises = plan.exercises.map(exercise => ({
      ...exercise,
      gif_url: exercise.gif_url ? BASE_URL + exercise.gif_url.replace(/^\/data/, '') : '',
      image_name: exercise.image_name ? BASE_URL + exercise.image_name.replace(/^\/data/, '') : ''
    }));
  }
  return processedPlan;
};

/**
 * 获取用户会话列表
 * @returns {Promise<Array>} 会话列表
 */
const getConversations = () => {
  console.log('API: 获取会话列表');
  return request('/api/v1/chat/conversations');
};

/**
 * 获取用户最近的消息（跨会话）
 * @param {number} skip - 跳过记录数
 * @param {number} limit - 返回记录数
 * @param {boolean} useCache - 是否使用缓存，默认为true
 * @returns {Promise<{messages: Array, total: number}>} 最近的消息和总数
 */
const getRecentConversations = async (skip = 0, limit = 5, useCache = true) => {
  console.log(`API: 获取最近消息 (skip=${skip}, limit=${limit})`);

  // If cache is valid and we're requesting the first batch of messages, return cached data
  if (useCache && skip === 0 && !recentConversationsCache.isExpired()) {
    console.log('API: 使用缓存的最近消息');
    return recentConversationsCache.data;
  }

  try {
    // If cache is valid but we're requesting a different page, still make the API call
    const result = await request('/api/v1/chat/recent-conversations', {
      skip,
      limit
    });

    // Cache the result if it's the first page (most common request)
    if (skip === 0) {
      recentConversationsCache.update(result);
    }

    return result;
  } catch (error) {
    console.error('API: 获取最近消息失败:', error);

    // If we have cached data and encounter an error, return the cache as fallback
    if (useCache && recentConversationsCache.data) {
      console.log('API: 请求失败，返回缓存的最近消息');
      return recentConversationsCache.data;
    }

    throw error; // No cache available, propagate the error
  }
};

/**
 * 清除最近消息缓存
 */
const clearRecentConversationsCache = () => {
  recentConversationsCache.clear();
  console.log('API: 已清除最近消息缓存');
};

/**
 * 获取指定会话的历史消息
 * @param {string} sessionId - 会话ID
 * @returns {Promise<Array>} 消息列表
 */
const getConversationMessages = (sessionId) => {
  if (!sessionId) {
    console.error('API: 获取历史消息需要 sessionId');
    return Promise.reject(new Error('Missing sessionId'));
  }
  console.log(`API: 获取会话 ${sessionId} 的历史消息`);
  return request(`/api/v1/chat/conversations/${sessionId}/messages`);
};

/**
 * 删除指定会话
 * @param {string} sessionId - 会话ID
 * @returns {Promise}
 */
const deleteConversation = (sessionId) => {
  if (!sessionId) {
    console.error('API: 删除会话需要 sessionId');
    return Promise.reject(new Error('Missing sessionId'));
  }
  console.log(`API: 删除会话 ${sessionId}`);
  // Assuming 'del' is the function for DELETE requests in request.js
  return del(`/api/v1/chat/conversations/${sessionId}`);
};

/**
 * 检查会话是否存在 (Simplified - relies on other calls)
 * @param {string} sessionId - 会话ID
 * @returns {Promise<boolean>} 返回Promise，解析为true表示会话存在，false表示不存在
 */
const checkSessionExists = async (sessionId) => {
  if (!sessionId) {
    console.log('API: 无法检查会话存在性，缺少会话ID');
    return false; // Return boolean directly
  }

  const token = wx.getStorageSync('token');
  if (!token) {
    console.log('API: 无法检查会话存在性，未找到认证Token');
    return false; // Return boolean directly
  }

  console.log(`API: 检查会话 ${sessionId} 是否存在 (通过连接API)`);
  try {
    // Use the connect endpoint to check existence implicitly
    const response = await wx.request({
      url: `${BASE_URL}/api/${API_VERSION}/chat/stream/${sessionId}/connect`,
      method: 'GET',
      header: {
        'Authorization': `Bearer ${token}`
      },
      // Add a timeout to avoid hanging indefinitely
      timeout: 5000 // 5 seconds
    });
    // If connect endpoint returns 200, session exists/is ready
    if (response.statusCode === 200) {
        console.log(`API: 会话 ${sessionId} 存在 (connect API)`);
        return true;
    } else {
         console.log(`API: 会话 ${sessionId} 可能不存在或无法连接 (connect API status: ${response.statusCode})`);
         // Attempt to get messages as a fallback check, though connect should be sufficient
         try {
             const messagesResponse = await getConversationMessages(sessionId);
             // If getting messages succeeds (even if empty), the conversation exists
             console.log(`API: 会话 ${sessionId} 存在 (消息 API)`);
             return true;
         } catch (msgError) {
             console.log(`API: 会话 ${sessionId} 不存在 (消息 API 失败)`);
             return false;
         }
    }
  } catch (error) {
    console.error(`API: 检查会话 ${sessionId} 时发生错误:`, error);
    return false;
  }
};

/**
 * 初始化聊天流（HTTP轮询）
 * @param {string} sessionId - 会话ID
 * @param {Object} handlers - 事件处理函数对象 { onOpen, onError }
 * @returns {Promise<void>} Resolves on successful initialization, rejects on error
 */
const initChatStream = async (sessionId, handlers = {}) => {
  if (!sessionId) {
    console.error('Chat: 无法初始化，缺少会话ID');
    if (handlers.onError) handlers.onError({ errMsg: '缺少会话ID' });
    return Promise.reject(new Error('Missing sessionId'));
  }

  const token = wx.getStorageSync('token');
  if (!token) {
    console.error('Chat: 无法初始化，用户未登录或Token无效');
    if (handlers.onError) handlers.onError({ errMsg: '用户未登录或Token无效' });
    return Promise.reject(new Error('User not logged in or token invalid'));
  }

  console.log(`Chat: 初始化流 (${sessionId}) using /connect`);

  return new Promise((resolve, reject) => {
    wx.request({
      url: `${BASE_URL}/api/${API_VERSION}/chat/stream/${sessionId}/connect`,
      method: 'GET',
      header: {
        'Authorization': `Bearer ${token}`
      },
      success: (res) => {
        if (res.statusCode === 200) {
          console.log(`Chat: 会话流初始化成功 (${sessionId})`);
          if (handlers.onOpen) {
            handlers.onOpen({
              type: 'open', // Simulate open event
              data: {
                session_id: sessionId,
                status: 'connected',
                server_message: res.data?.message || '连接成功'
              }
            });
          }
          resolve(); // Resolve the promise on success
        } else {
          console.error(`Chat: 初始化流失败 (${sessionId}), Status: ${res.statusCode}`);
          const error = new Error(`初始化流失败: ${res.statusCode}`);
          if (handlers.onError) handlers.onError({ errMsg: error.message });
          reject(error);
        }
      },
      fail: (err) => {
        console.error(`Chat: 初始化流请求失败 (${sessionId}):`, err);
        if (handlers.onError) handlers.onError(err);
        reject(err);
      }
    });
  });
};

/**
 * 轮询新消息
 * @param {string} sessionId - 会话ID
 * @param {number} lastMessageId - 上次接收到的最后一条消息的ID
 * @param {Object} handlers - 事件处理函数对象 { onMessage, onError }
 * @returns {Promise<void>} Resolves when poll completes, rejects on error
 */
const pollMessages = (sessionId, lastMessageId = 0, handlers = {}) => {
   if (!sessionId) {
    console.error('Chat: 无法轮询，缺少会话ID');
    if (handlers.onError) handlers.onError({ errMsg: '缺少会话ID' });
    return Promise.reject(new Error('Missing sessionId'));
  }

  const token = wx.getStorageSync('token');
  if (!token) {
    console.error('Chat: 无法轮询，用户未登录或Token无效');
    if (handlers.onError) handlers.onError({ errMsg: '用户未登录或Token无效' });
    return Promise.reject(new Error('User not logged in or token invalid'));
  }

  // Ensure lastMessageId is a number
  const validLastMessageId = typeof lastMessageId === 'number' ? lastMessageId : 0;

  return new Promise((resolve, reject) => {
    wx.request({
      url: `${BASE_URL}/api/${API_VERSION}/chat/poll/${sessionId}`,
      method: 'GET',
      data: {
        last_message_id: validLastMessageId
      },
      header: {
        'Authorization': `Bearer ${token}`
      },
      timeout: 30000,
      success: (res) => {
        if (res.statusCode === 200) {
          if (res.data && res.data.messages && res.data.messages.length > 0) {
            // 使用统一的消息处理器处理消息
            const processedMessages = res.data.messages.map(message =>
              messageProcessor.processMessage(message, { processUI: false })
            ).filter(msg => msg !== null);

            if (handlers.onMessage) {
              handlers.onMessage(processedMessages);
            }
          } else {
            if (handlers.onMessage) {
              handlers.onMessage([]);
            }
          }
          resolve();
        } else if (res.statusCode === 404) {
          console.warn(`Chat: 轮询失败 - 会话 ${sessionId} 未找到 (404)`);
          const error = new Error(`会话未找到 (404)`);
          if (handlers.onError) handlers.onError({ errMsg: error.message, statusCode: 404 });
          reject(error);
        } else if (res.statusCode === 422) {
          console.warn(`Chat: 轮询失败 - 无效的 last_message_id (${validLastMessageId}) (422)`);
          const error = new Error(`无效的 last_message_id (422)`);
          if (handlers.onError) handlers.onError({ errMsg: error.message, statusCode: 422 });
          reject(error);
        } else {
          console.error(`Chat: 轮询返回错误状态码 ${res.statusCode}`);
          const error = new Error(`轮询失败: ${res.statusCode}`);
          if (handlers.onError) handlers.onError({ errMsg: error.message, statusCode: res.statusCode });
          reject(error);
        }
      },
      fail: (err) => {
        console.error(`Chat: 轮询请求失败 (${sessionId}):`, err);
        if (handlers.onError) handlers.onError(err);
        reject(err);
      }
    });
  });
};

/**
 * 发送聊天消息 (HTTP)
 * @param {string} sessionId - 会话ID
 * @param {string} messageContent - 消息内容字符串
 * @param {Object} [metaInfo=null] - 可选的元数据，如客户端消息ID
 * @param {Function} [onErrorCallback] - Optional error callback
 * @returns {Promise<boolean>} 是否成功发送
 */
const sendChatMessage = (sessionId, messageContent, metaInfo = null, onErrorCallback) => {
  if (!sessionId) {
    console.error('Chat: 发送消息失败，缺少 sessionId');
    if (onErrorCallback) onErrorCallback({ errMsg: '缺少会话ID，无法发送消息' });
    return Promise.resolve(false);
  }

  if (!messageContent || typeof messageContent !== 'string') {
    console.error('Chat: 发送消息失败，消息内容必须为字符串');
    if (onErrorCallback) onErrorCallback({ errMsg: '消息内容必须为字符串' });
    return Promise.resolve(false);
  }

  const token = wx.getStorageSync('token');
  if (!token) {
    console.error('Chat: 发送消息失败，未找到认证令牌');
    if (onErrorCallback) onErrorCallback({ errMsg: '用户未登录或Token无效' });
    return Promise.resolve(false);
  }

  console.log(`Chat: 发送消息 to ${sessionId}${metaInfo ? ' 带元数据' : ''}`);

  // 准备请求数据，添加元数据支持
  const requestData = {
    message: messageContent
  };

  // 如果提供了元数据，添加到请求中
  if (metaInfo) {
    requestData.meta_info = metaInfo;
    console.log('添加元数据到消息:', metaInfo);
  }

  // 正确处理微信API Promise化
  return new Promise((resolve) => {
    wx.request({
      url: `${BASE_URL}/api/${API_VERSION}/chat/stream/${sessionId}/message`,
      method: 'POST',
      data: requestData,
      header: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      },
      success: (res) => {
        console.log(`Chat: 消息发送响应:`, res);
        if (res.statusCode >= 200 && res.statusCode < 300) {
          console.log(`Chat: 消息发送成功 (${sessionId})`);
          clearRecentConversationsCache();
          resolve(true);
        } else {
          console.error(`Chat: 消息发送失败 (${sessionId}), Status: ${res.statusCode}, Data:`, res.data);
          const error = new Error(`消息发送失败: ${res.statusCode} ${res.data?.message || ''}`);
          if (onErrorCallback) onErrorCallback({ errMsg: error.message, statusCode: res.statusCode });
          resolve(false);
        }
      },
      fail: (err) => {
        console.error(`Chat: 发送消息请求异常 (${sessionId}):`, err);
        if (onErrorCallback) onErrorCallback(err);
        resolve(false);
      }
    });
  });
};
// Removed closeChatWebSocket and closeAllChatWebSockets as they are not needed for HTTP polling

/**
 * 添加消息到指定会话
 * @param {string} sessionId - 会话ID
 * @param {string} content - 消息内容
 * @param {string} role - 消息角色 ('user', 'assistant', 'system', 'tool')
 * @param {Object} [metaInfo] - 附加元数据
 * @returns {Promise<Object>} 成功时返回创建的消息对象，失败时抛出错误
 */
const addMessageToSession = async (sessionId, content, role, metaInfo = null) => {
  if (!sessionId) {
    throw new Error('缺少会话ID (sessionId)');
  }
  if (!content || typeof content !== 'string') {
    throw new Error('消息内容 (content) 必须为非空字符串');
  }
  if (!role || typeof role !== 'string') {
    throw new Error('消息角色 (role) 必须为非空字符串');
  }

  const body = {
    content: content,
    role: role,
    meta_info: metaInfo
  };

  console.log(`API: 添加消息到会话 ${sessionId}`, body);

  try {
    const messageData = await post(`/api/${API_VERSION}/chat/sessions/${sessionId}/messages`, body);
    console.log(`API: 消息成功添加到会话 ${sessionId}`, messageData);
    // Clear cache on successful addition
    clearRecentConversationsCache();
    return messageData;
  } catch (error) {
    console.error(`API: 添加消息到会话 ${sessionId} 失败:`, error);
    throw error; // Re-throw the error from request.js
  }
};

/**
 * 更新现有消息
 * @param {number} messageId - 要更新的消息的ID
 * @param {string} content - 更新后的消息内容
 * @param {string} role - 更新后的消息角色
 * @param {Object} [metaInfo] - 更新后的附加元数据
 * @returns {Promise<Object>} 成功时返回更新后的消息对象，失败时抛出错误
 */
const updateMessage = async (messageId, content, role, metaInfo = null) => {
  console.log(`开始更新消息 - 消息ID: ${messageId}, 内容类型: ${typeof content}, 元数据类型: ${typeof metaInfo}`);

  // 验证参数
  if (!messageId || typeof messageId !== 'number') {
    console.error('无效的消息ID:', messageId, '类型:', typeof messageId);
    throw new Error('无效的消息ID (messageId)');
  }

  if (!content || typeof content !== 'string') {
    console.error('无效的消息内容:', content, '类型:', typeof content);
    throw new Error('消息内容 (content) 必须为非空字符串');
  }

  if (!role || typeof role !== 'string') {
    console.error('无效的消息角色:', role, '类型:', typeof role);
    throw new Error('消息角色 (role) 必须为非空字符串');
  }

  // 确保meta_info是有效的对象
  let safeMetaInfo = metaInfo;
  if (metaInfo !== null && typeof metaInfo !== 'object') {
    console.warn('元数据不是对象类型，将转换为空对象:', metaInfo);
    safeMetaInfo = {};
  }

  // 检查meta_info中的complete_training_plan字段
  if (safeMetaInfo && safeMetaInfo.complete_training_plan) {
    console.log('检查complete_training_plan字段:', {
      'exercises存在': Boolean(safeMetaInfo.complete_training_plan.exercises),
      'workouts存在': Boolean(safeMetaInfo.complete_training_plan.workouts),
    });

    // 如果没有exercises字段，但有workouts字段，从workouts中提取exercises
    if (!safeMetaInfo.complete_training_plan.exercises &&
        safeMetaInfo.complete_training_plan.workouts &&
        Array.isArray(safeMetaInfo.complete_training_plan.workouts) &&
        safeMetaInfo.complete_training_plan.workouts.length > 0) {

      const workout = safeMetaInfo.complete_training_plan.workouts[0];
      if (workout.exercises && Array.isArray(workout.exercises)) {
        safeMetaInfo.complete_training_plan.exercises = [...workout.exercises];
        console.log('从workouts中提取exercises字段，数量:', safeMetaInfo.complete_training_plan.exercises.length);
      } else {
        safeMetaInfo.complete_training_plan.exercises = [];
        console.log('创建空的exercises数组');
      }
    } else if (!safeMetaInfo.complete_training_plan.exercises) {
      safeMetaInfo.complete_training_plan.exercises = [];
      console.log('创建空的exercises数组');
    }
  }

  const body = {
    content: content,
    role: role,
    meta_info: safeMetaInfo
  };

  console.log(`API: 更新消息 ${messageId}, 请求体大小约 ${JSON.stringify(body).length} 字节`);

  try {
    // 记录更详细的请求信息，用于调试
    console.log(`API: 准备更新消息 ${messageId}，请求体:`, {
      'content长度': body.content ? body.content.length : 0,
      'role': body.role,
      'meta_info键': body.meta_info ? Object.keys(body.meta_info) : [],
      'training_params键': body.meta_info?.training_params ? Object.keys(body.meta_info.training_params) : [],
      'collecting_training_params': body.meta_info?.collecting_training_params,
      'paramConfirmed': body.meta_info?.paramConfirmed
    });

    // 使用基础请求函数
    const updatedMessageData = await request(`/api/${API_VERSION}/chat/messages/${messageId}`, 'PUT', body);
    console.log(`API: 消息 ${messageId} 更新成功，服务器返回数据ID:`, updatedMessageData?.id);

    // 验证元数据是否正确更新
    if (safeMetaInfo && updatedMessageData && updatedMessageData.meta_info) {
      console.log(`元数据更新检查:`, {
        '提交的元数据键': Object.keys(safeMetaInfo),
        '返回的元数据键': Object.keys(updatedMessageData.meta_info),
        '训练计划ID在返回中': updatedMessageData.meta_info.complete_training_plan?.id,
        '训练参数在返回中': Boolean(updatedMessageData.meta_info.training_params)
      });

      // 检查训练参数是否正确更新
      if (safeMetaInfo.training_params && updatedMessageData.meta_info.training_params) {
        console.log('训练参数更新检查:', {
          '提交的训练参数键': Object.keys(safeMetaInfo.training_params),
          '返回的训练参数键': Object.keys(updatedMessageData.meta_info.training_params),
          '参数确认状态': updatedMessageData.meta_info.training_params.param_confirmed,
          '收集参数状态': updatedMessageData.meta_info.collecting_training_params
        });
      }
    }

    // 清除缓存
    clearRecentConversationsCache();
    return updatedMessageData;
  } catch (error) {
    console.error(`API: 更新消息 ${messageId} 失败:`, error);

    // 添加更详细的错误信息
    if (error.statusCode) {
      console.error(`HTTP状态码: ${error.statusCode}`);
    }

    if (error.data) {
      console.error('错误响应数据:', error.data);
    }

    // 记录请求体信息，帮助调试
    console.error('失败的请求体信息:', {
      'content长度': body.content ? body.content.length : 0,
      'role': body.role,
      'meta_info键': body.meta_info ? Object.keys(body.meta_info) : []
    });

    // 重新抛出错误
    throw error;
  }
};

module.exports = {
  getConversations,
  getConversationMessages,
  getRecentConversations,     // New function for recent conversations
  clearRecentConversationsCache, // Function to clear the cache
  deleteConversation,
  checkSessionExists,
  initChatStream,       // Renamed from connectChatWebSocket
  pollMessages,         // New function for polling
  sendChatMessage,      // Updated for HTTP POST
  addMessageToSession,  // New function
  updateMessage         // New function
  // Removed close functions
};