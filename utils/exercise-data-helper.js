/**
 * 运动数据工具类
 * @file exercise-data-helper.js
 * @description 提供运动数据处理相关的共享功能，减少代码冗余
 */

const imageHelper = require('./image-helper');

/**
 * 处理运动的图片URL，确保使用完整路径
 * @param {Object} exercise - 运动对象
 * @returns {string} 处理后的图片URL
 */
function processExerciseImageUrl(exercise) {
  let processedImageUrl = '/images/exercises/default.png';

  // 优先处理exercise_image字段，然后是其他字段
  if (exercise.exercise_image) {
    processedImageUrl = imageHelper.getFullImageUrl(exercise.exercise_image);
  } else if (exercise.gif_url) {
    processedImageUrl = imageHelper.getFullImageUrl(exercise.gif_url);
  } else if (exercise.image_url) {
    processedImageUrl = imageHelper.getFullImageUrl(exercise.image_url);
  } else if (exercise.image_name) {
    processedImageUrl = imageHelper.getFullImageUrl(exercise.image_name);
  } else if (exercise.imageUrl) {
    processedImageUrl = imageHelper.getFullImageUrl(exercise.imageUrl);
  }

  console.log('处理后的图片URL:', processedImageUrl);
  return processedImageUrl;
}

/**
 * 生成一致的运动ID
 * @param {Object} exercise - 运动对象
 * @param {number} workoutId - 训练ID
 * @returns {string|number} 生成的ID
 */
function generateExerciseId(exercise, workoutId) {
  // 如果exercise已经有有效的ID，直接使用
  if (exercise.id && !String(exercise.id).startsWith('added_exercise')) {
    return exercise.id;
  }

  // 对于新添加的运动，生成基于时间戳和运动ID的唯一ID
  const timestamp = Date.now();
  const exerciseId = exercise.exercise_id || exercise.id || 'unknown';
  const randomSuffix = Math.floor(Math.random() * 10000);
  
  return `${workoutId}_${exerciseId}_${timestamp}_${randomSuffix}`;
}

/**
 * 创建标准的组数据
 * @param {Object} params - 参数对象
 * @param {string} params.setType - 组类型 ('热身', '正式', '递减')
 * @param {number} params.setCount - 组数
 * @param {number} params.defaultReps - 默认次数
 * @param {number} params.defaultWeight - 默认重量
 * @returns {Array} 组数据数组
 */
function createStandardSets(params) {
  const { setType = '正式', setCount = 3, defaultReps = 12, defaultWeight = 20 } = params;
  const sets = [];

  // 根据组类型创建不同的默认组
  switch(setType) {
    case '热身':
      for (let i = 0; i < setCount; i++) {
        sets.push({
          type: i === 0 ? 'warmup' : (i === setCount - 1 ? 'normal' : 'normal'),
          weight: i === 0 ? defaultWeight * 0.6 : (i === 1 ? defaultWeight * 0.8 : defaultWeight),
          reps: i === 0 ? defaultReps + 3 : defaultReps,
          completed: false
        });
      }
      break;
    case '递减':
      for (let i = 0; i < setCount; i++) {
        sets.push({
          type: i === setCount - 1 ? 'decrease' : 'normal',
          weight: i === setCount - 1 ? defaultWeight * 0.8 : (defaultWeight - i * defaultWeight * 0.1),
          reps: i === setCount - 1 ? defaultReps + 2 : defaultReps,
          completed: false
        });
      }
      break;
    default: // 正式
      for (let i = 0; i < setCount; i++) {
        sets.push({
          type: 'normal',
          weight: defaultWeight,
          reps: defaultReps,
          completed: false
        });
      }
  }

  return sets;
}

/**
 * 处理运动的组记录数据
 * @param {Object} exercise - 运动对象
 * @param {number} setCount - 组数
 * @param {number} reps - 次数
 * @param {number} weight - 重量
 * @returns {Array} 组数据数组
 */
function processSetRecords(exercise, setCount, reps, weight) {
  const sets = [];

  // 如果有set_records，使用set_records数据
  if (exercise.set_records && Array.isArray(exercise.set_records) && exercise.set_records.length > 0) {
    exercise.set_records.forEach(record => {
      sets.push({
        type: record.set_type || 'normal',
        weight: record.weight || weight,
        reps: record.reps || reps,
        completed: record.completed || false
      });
    });
  } else {
    // 创建默认组数据
    for (let i = 0; i < setCount; i++) {
      sets.push({
        type: i === 0 ? 'warmup' : 'normal',
        weight: i === 0 ? weight * 0.8 : weight,
        reps: i === 0 ? reps + 2 : reps,
        completed: false
      });
    }
  }

  return sets;
}

/**
 * 创建标准的运动对象用于UI显示
 * @param {Object} exercise - 原始运动数据
 * @param {Object} options - 选项
 * @returns {Object} 标准化的运动对象
 */
function createStandardExercise(exercise, options = {}) {
  const { calculateTotalVolume } = options;
  
  // 处理图片URL
  const imageUrl = processExerciseImageUrl(exercise);
  
  // 处理组数据
  const setCount = exercise.sets || 3;
  const reps = exercise.reps || 12;
  const weight = exercise.weight || 20;
  const sets = processSetRecords(exercise, setCount, reps, weight);

  // 创建标准运动对象
  const standardExercise = {
    id: exercise.id,
    name: exercise.exercise_name || exercise.name,
    imageUrl: imageUrl,
    category: exercise.exercise_description || exercise.category || '',
    sets: sets,
    isExpanded: false
  };

  // 如果提供了计算总容量的函数，则计算总容量
  if (calculateTotalVolume && typeof calculateTotalVolume === 'function') {
    standardExercise.totalVolume = calculateTotalVolume(sets);
  }

  return standardExercise;
}

/**
 * 创建workout exercise对象
 * @param {Object} params - 参数对象
 * @returns {Object} workout exercise对象
 */
function createWorkoutExercise(params) {
  const {
    exercise,
    workoutId,
    exerciseId,
    order,
    setCount = 3,
    defaultReps = 12,
    defaultWeight = 20,
    setType = '正式'
  } = params;

  const imageUrl = processExerciseImageUrl(exercise);

  const workoutExercise = {
    id: exerciseId,
    exercise_id: exercise.exercise_id || exercise.id,
    workout_id: workoutId,
    sets: setCount,
    reps: defaultReps,
    rest_seconds: 60,
    order: order,
    notes: "",
    exercise_type: "weight_reps",
    weight: defaultWeight,
    exercise_name: exercise.name,
    image_url: imageUrl,
    exercise_description: exercise.category || "",
    set_records: []
  };

  // 创建set_records
  const standardSets = createStandardSets({
    setType,
    setCount,
    defaultReps,
    defaultWeight
  });

  for (let i = 0; i < setCount; i++) {
    const set = standardSets[i];
    const setRecordId = `${exerciseId}_set_${i+1}`;

    workoutExercise.set_records.push({
      id: setRecordId,
      workout_exercise_id: exerciseId,
      set_number: i + 1,
      set_type: set.type,
      weight: set.weight,
      reps: set.reps,
      completed: false,
      notes: ""
    });
  }

  return workoutExercise;
}

module.exports = {
  processExerciseImageUrl,
  generateExerciseId,
  createStandardSets,
  processSetRecords,
  createStandardExercise,
  createWorkoutExercise
};
