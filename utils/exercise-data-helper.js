/**
 * 运动数据工具类
 * @file exercise-data-helper.js
 * @description 提供运动数据处理相关的共享功能，减少代码冗余
 */

const imageHelper = require('./image-helper');

/**
 * 数据类型转换工具函数
 */

/**
 * 安全地将值转换为整数
 * @param {any} value - 要转换的值
 * @param {number} defaultValue - 默认值
 * @returns {number} 转换后的整数
 */
function safeParseInt(value, defaultValue = 12) {
  if (value === null || value === undefined || value === '') {
    return defaultValue;
  }

  const parsed = parseInt(value, 10);

  // 检查是否为有效数字，并且在合理范围内
  if (isNaN(parsed) || parsed < 0 || parsed > 1000) {
    console.warn(`[safeParseInt] 无效的整数值: ${value}, 使用默认值: ${defaultValue}`);
    return defaultValue;
  }

  return parsed;
}

/**
 * 安全地将值转换为浮点数
 * @param {any} value - 要转换的值
 * @param {number} defaultValue - 默认值
 * @returns {number} 转换后的浮点数
 */
function safeParseFloat(value, defaultValue = 20) {
  if (value === null || value === undefined || value === '') {
    return defaultValue;
  }

  const parsed = parseFloat(value);

  // 检查是否为有效数字，并且在合理范围内
  if (isNaN(parsed) || parsed < 0 || parsed > 10000) {
    console.warn(`[safeParseFloat] 无效的浮点数值: ${value}, 使用默认值: ${defaultValue}`);
    return defaultValue;
  }

  // 保留2位小数，避免浮点数精度问题
  return Math.round(parsed * 100) / 100;
}

/**
 * 标准化运动数据的数值字段
 * @param {Object} exercise - 运动对象
 * @returns {Object} 标准化后的运动对象
 */
function normalizeExerciseData(exercise) {
  const normalized = {
    ...exercise,
    reps: safeParseInt(exercise.reps, 12),
    weight: safeParseFloat(exercise.weight, 20),
    sets: safeParseInt(exercise.sets, 3),
    rest_seconds: safeParseInt(exercise.rest_seconds, 60)
  };

  // 如果有 set_records，也要标准化
  if (exercise.set_records && Array.isArray(exercise.set_records)) {
    normalized.set_records = exercise.set_records.map(record => ({
      ...record,
      reps: safeParseInt(record.reps, 12),
      weight: safeParseFloat(record.weight, 20),
      set_number: safeParseInt(record.set_number, 1)
    }));
  }

  console.log(`[normalizeExerciseData] 标准化运动数据: ${exercise.name || exercise.exercise_name || 'Unknown'}`);
  console.log(`  原始 reps: ${exercise.reps} -> 标准化: ${normalized.reps}`);
  console.log(`  原始 weight: ${exercise.weight} -> 标准化: ${normalized.weight}`);

  return normalized;
}

/**
 * 处理运动的图片URL，确保使用完整路径
 * @param {Object} exercise - 运动对象
 * @returns {string} 处理后的图片URL
 */
function processExerciseImageUrl(exercise) {
  let processedImageUrl = '/images/exercises/default.png';

  // 优先处理exercise_image字段，然后是其他字段
  if (exercise.exercise_image) {
    processedImageUrl = imageHelper.getFullImageUrl(exercise.exercise_image);
  } else if (exercise.gif_url) {
    processedImageUrl = imageHelper.getFullImageUrl(exercise.gif_url);
  } else if (exercise.image_url) {
    processedImageUrl = imageHelper.getFullImageUrl(exercise.image_url);
  } else if (exercise.image_name) {
    processedImageUrl = imageHelper.getFullImageUrl(exercise.image_name);
  } else if (exercise.imageUrl) {
    processedImageUrl = imageHelper.getFullImageUrl(exercise.imageUrl);
  }

  console.log('处理后的图片URL:', processedImageUrl);
  return processedImageUrl;
}

/**
 * 生成一致的运动ID
 * @param {Object} exercise - 运动对象
 * @param {number} workoutId - 训练ID
 * @returns {string|number} 生成的ID
 */
function generateExerciseId(exercise, workoutId) {
  // 如果exercise已经有有效的ID，直接使用
  if (exercise.id && !String(exercise.id).startsWith('added_exercise')) {
    return exercise.id;
  }

  // 对于新添加的运动，生成基于时间戳和运动ID的唯一ID
  const timestamp = Date.now();
  const exerciseId = exercise.exercise_id || exercise.id || 'unknown';
  const randomSuffix = Math.floor(Math.random() * 10000);

  return `${workoutId}_${exerciseId}_${timestamp}_${randomSuffix}`;
}

/**
 * 创建标准的组数据
 * @param {Object} params - 参数对象
 * @param {string} params.setType - 组类型 ('热身', '正式', '递减')
 * @param {number} params.setCount - 组数
 * @param {number} params.defaultReps - 默认次数
 * @param {number} params.defaultWeight - 默认重量
 * @returns {Array} 组数据数组
 */
function createStandardSets(params) {
  const { setType = '正式', setCount = 3, defaultReps = 12, defaultWeight = 20 } = params;
  const sets = [];

  // 根据组类型创建不同的默认组
  switch(setType) {
    case '热身':
      for (let i = 0; i < setCount; i++) {
        sets.push({
          type: i === 0 ? 'warmup' : (i === setCount - 1 ? 'normal' : 'normal'),
          weight: i === 0 ? defaultWeight * 0.6 : (i === 1 ? defaultWeight * 0.8 : defaultWeight),
          reps: i === 0 ? defaultReps + 3 : defaultReps,
          completed: false
        });
      }
      break;
    case '递减':
      for (let i = 0; i < setCount; i++) {
        sets.push({
          type: i === setCount - 1 ? 'decrease' : 'normal',
          weight: i === setCount - 1 ? defaultWeight * 0.8 : (defaultWeight - i * defaultWeight * 0.1),
          reps: i === setCount - 1 ? defaultReps + 2 : defaultReps,
          completed: false
        });
      }
      break;
    default: // 正式
      for (let i = 0; i < setCount; i++) {
        sets.push({
          type: 'normal',
          weight: defaultWeight,
          reps: defaultReps,
          completed: false
        });
      }
  }

  return sets;
}

/**
 * 处理运动的组记录数据
 * @param {Object} exercise - 运动对象
 * @param {number} setCount - 组数
 * @param {number} reps - 次数
 * @param {number} weight - 重量
 * @returns {Array} 组数据数组
 */
function processSetRecords(exercise, setCount, reps, weight) {
  const sets = [];

  // 标准化输入参数
  const normalizedSetCount = safeParseInt(setCount, 3);
  const normalizedReps = safeParseInt(reps, 12);
  const normalizedWeight = safeParseFloat(weight, 20);

  // 如果有set_records，使用set_records数据
  if (exercise.set_records && Array.isArray(exercise.set_records) && exercise.set_records.length > 0) {
    exercise.set_records.forEach(record => {
      sets.push({
        type: record.set_type || 'normal',
        weight: safeParseFloat(record.weight, normalizedWeight),
        reps: safeParseInt(record.reps, normalizedReps),
        completed: record.completed || false
      });
    });
  } else {
    // 创建默认组数据
    for (let i = 0; i < normalizedSetCount; i++) {
      const isWarmup = i === 0;
      sets.push({
        type: isWarmup ? 'warmup' : 'normal',
        weight: safeParseFloat(isWarmup ? normalizedWeight * 0.8 : normalizedWeight),
        reps: safeParseInt(isWarmup ? normalizedReps + 2 : normalizedReps),
        completed: false
      });
    }
  }

  return sets;
}

/**
 * 创建标准的运动对象用于UI显示
 * @param {Object} exercise - 原始运动数据
 * @param {Object} options - 选项
 * @returns {Object} 标准化的运动对象
 */
function createStandardExercise(exercise, options = {}) {
  const { calculateTotalVolume } = options;

  // 首先标准化运动数据
  const normalizedExercise = normalizeExerciseData(exercise);

  // 处理图片URL
  const imageUrl = processExerciseImageUrl(normalizedExercise);

  // 处理组数据，使用标准化后的数值
  const sets = processSetRecords(normalizedExercise, normalizedExercise.sets, normalizedExercise.reps, normalizedExercise.weight);

  // 创建标准运动对象
  const standardExercise = {
    id: normalizedExercise.id,
    name: normalizedExercise.exercise_name || normalizedExercise.name,
    imageUrl: imageUrl,
    category: normalizedExercise.exercise_description || normalizedExercise.category || '',
    sets: sets,
    isExpanded: false,
    // 保存原始的 exercise_id 用于后续ID映射
    exercise_id: normalizedExercise.exercise_id
  };

  // 如果提供了计算总容量的函数，则计算总容量
  if (calculateTotalVolume && typeof calculateTotalVolume === 'function') {
    standardExercise.totalVolume = calculateTotalVolume(sets);
  }

  return standardExercise;
}

/**
 * 创建workout exercise对象
 * @param {Object} params - 参数对象
 * @returns {Object} workout exercise对象
 */
function createWorkoutExercise(params) {
  const {
    exercise,
    workoutId,
    exerciseId,
    order,
    setCount = 3,
    defaultReps = 12,
    defaultWeight = 20,
    setType = '正式'
  } = params;

  const imageUrl = processExerciseImageUrl(exercise);

  const workoutExercise = {
    id: exerciseId,
    exercise_id: exercise.exercise_id || exercise.id,
    workout_id: workoutId,
    sets: setCount,
    reps: defaultReps,
    rest_seconds: 60,
    order: order,
    notes: "",
    exercise_type: "weight_reps",
    weight: defaultWeight,
    exercise_name: exercise.name,
    image_url: imageUrl,
    exercise_description: exercise.category || "",
    set_records: []
  };

  // 创建set_records
  const standardSets = createStandardSets({
    setType,
    setCount,
    defaultReps,
    defaultWeight
  });

  for (let i = 0; i < setCount; i++) {
    const set = standardSets[i];
    const setRecordId = `${exerciseId}_set_${i+1}`;

    workoutExercise.set_records.push({
      id: setRecordId,
      workout_exercise_id: exerciseId,
      set_number: i + 1,
      set_type: set.type,
      weight: set.weight,
      reps: set.reps,
      completed: false,
      notes: ""
    });
  }

  return workoutExercise;
}

/**
 * 模板数据处理工具函数
 */

/**
 * 保存完整的 workout exercise 结构
 * @param {Array} templateExercises - 模板运动数据
 * @returns {Array} 保存了完整结构的运动数据
 */
function preserveWorkoutExerciseStructure(templateExercises) {
  if (!templateExercises || !Array.isArray(templateExercises)) {
    return [];
  }

  return templateExercises.map((exercise, index) => {
    // 标准化运动数据
    const normalizedExercise = normalizeExerciseData(exercise);

    console.log(`[preserveWorkoutExerciseStructure] 处理运动 ${index}:`);
    console.log(`  原始ID: ${exercise.id}, exercise_id: ${exercise.exercise_id}`);

    // 保存完整的 workout exercise 结构
    const preservedStructure = {
      // 原始数据保存
      original_data: { ...exercise },

      // ID映射信息
      workout_exercise_id: exercise.id,  // 这是 workout exercise 的 ID (如 221)
      actual_exercise_id: exercise.exercise_id || exercise.id, // 这是实际的 exercise ID (如 8)

      // 标准化后的数据
      normalized_data: normalizedExercise,

      // UI显示用的数据
      display_name: exercise.exercise_name || exercise.name,
      display_category: exercise.exercise_description || exercise.category || '',

      // 索引信息
      index: index
    };

    console.log(`  映射结果: workout_exercise_id=${preservedStructure.workout_exercise_id}, actual_exercise_id=${preservedStructure.actual_exercise_id}`);

    return preservedStructure;
  });
}

/**
 * 从保存的结构中创建正确的更新数据
 * @param {Object} preservedExercise - 保存的运动结构
 * @param {Object} currentExerciseData - 当前的运动数据
 * @returns {Object} 正确映射的更新数据
 */
function createCorrectUpdateData(preservedExercise, currentExerciseData) {
  const { workout_exercise_id, actual_exercise_id, normalized_data } = preservedExercise;

  // 使用当前数据，但确保ID映射正确
  const updateData = {
    exercise_id: actual_exercise_id, // 使用正确的 exercise ID
    order: preservedExercise.index + 1,
    sets: safeParseInt(currentExerciseData.sets?.length || normalized_data.sets, 3),
    reps: safeParseInt(currentExerciseData.reps || normalized_data.reps, 12),
    weight: safeParseFloat(currentExerciseData.weight || normalized_data.weight, 20),
    rest_seconds: safeParseInt(currentExerciseData.rest_seconds || normalized_data.rest_seconds, 60),
    notes: currentExerciseData.notes || normalized_data.notes || '',
    exercise_type: currentExerciseData.exercise_type || normalized_data.exercise_type || 'weight_reps'
  };

  // 处理 set_records，确保 workout_exercise_id 正确
  if (currentExerciseData.sets && Array.isArray(currentExerciseData.sets)) {
    updateData.set_records = currentExerciseData.sets.map((set, setIndex) => ({
      id: set.id || `${workout_exercise_id}_set_${Date.now()}_${setIndex}`,
      workout_exercise_id: workout_exercise_id, // 使用正确的 workout exercise ID
      set_number: setIndex + 1,
      set_type: set.type || 'normal',
      weight: safeParseFloat(set.weight, updateData.weight),
      reps: safeParseInt(set.reps, updateData.reps),
      completed: set.completed || false,
      notes: set.notes || ''
    }));
  }

  console.log(`[createCorrectUpdateData] 创建更新数据:`);
  console.log(`  exercise_id: ${updateData.exercise_id}`);
  console.log(`  set_records中的workout_exercise_id: ${workout_exercise_id}`);

  return updateData;
}

module.exports = {
  // 数据类型转换函数
  safeParseInt,
  safeParseFloat,
  normalizeExerciseData,
  // 模板数据处理函数
  preserveWorkoutExerciseStructure,
  createCorrectUpdateData,
  // 原有函数
  processExerciseImageUrl,
  generateExerciseId,
  createStandardSets,
  processSetRecords,
  createStandardExercise,
  createWorkoutExercise
};
