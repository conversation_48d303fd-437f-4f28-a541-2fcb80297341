/**
 * 动作列表工具类
 * @file exercise-list-helper.js
 * @description 提供动作列表相关的共享功能，减少代码冗余
 */

// 引入API模块 - 使用更健壮的导入方式
let exerciseAdapter;
try {
  try {
    // 首先尝试直接导入exercise-adapter.js
    exerciseAdapter = require('../api/exercise-adapter');
    console.log('成功导入exercise-adapter模块');
  } catch (directImportError) {
    console.warn('直接导入exercise-adapter失败，尝试从index导入', directImportError);
    
    // 尝试从index导入
    const api = require('../api/index');
    exerciseAdapter = api.exerciseAdapter || api.exercise || api.exerciseLibrary;
    
    if (!exerciseAdapter) {
      console.warn('未找到exerciseAdapter，尝试导入exercise-library');
      exerciseAdapter = require('../api/exercise-library');
    }
  }
  
  // 验证exerciseAdapter是否具有所需方法
  if (!exerciseAdapter || typeof exerciseAdapter.fetchExerciseList !== 'function') {
    throw new Error('导入的exerciseAdapter模块缺少fetchExerciseList方法');
  }
} catch (error) {
  console.error('加载API模块失败，将使用模拟数据:', error);
  // 创建模拟的适配器，确保函数签名一致
  exerciseAdapter = {
    fetchExerciseList: async (params) => {
      console.log('使用模拟的API调用，参数:', params);
      return { 
        success: false, 
        errorMessage: 'API模块加载失败', 
        dataList: [] 
      };
    }
  };
}

const { BASE_URL } = require('../api/request');

// 器材分类定义 - 移除ID为0的"全部"选项
const EQUIPMENT_CATEGORIES = [
  { id: 2, name: '自重' },
  { id: 4, name: '哑铃' },
  { id: 1, name: '杠铃' },
  { id: 3, name: '绳索' },
  { id: 6, name: '固定器械' },
  { id: 7, name: '悍马器械' },
  { id: 8, name: '史密斯' },
  { id: 5, name: 'EZ杠铃' },
  { id: 11, name: '弹力带' },
  { id: 19, name: '阻力带' },
  { id: 25, name: 'TRX' },
  { id: 12, name: '战绳' },
  { id: 13, name: '波速球' },
  { id: 15, name: '壶铃' },
  { id: 16, name: '药球' },
  { id: 20, name: '泡沫轴' },
  { id: 21, name: '筋膜球' },
  { id: 23, name: '瑜伽球' },
  { id: 24, name: '训练棍' },
  { id: 18, name: '雪橇' },
  { id: 9, name: '负重' }
];

// 身体部位分类定义 - 与 exe-list 页面保持一致
const BODY_PART_CATEGORIES = [
  { id: 2, name: '胸部', full_name: '胸部' },
  { id: 4, name: '背部', full_name: '背部' },
  { id: 1, name: '腿部', full_name: '腿部' },
  { id: 6, name: '肩部', full_name: '肩部' },
  { id: 3, name: '臀部', full_name: '臀部' },
  { id: 12, name: '腰腹部', full_name: '腰腹部' },
  { id: 5, name: '手臂', full_name: '手臂' },
  { id: 17, name: '二头', full_name: '二头肌' },
  { id: 18, name: '三头', full_name: '三头肌' },
  { id: 19, name: '股四头肌', full_name: '股四头肌' },
  { id: 20, name: '腘绳肌', full_name: '腘绳肌' },
  { id: 7, name: '小臂', full_name: '小臂' },
  { id: 8, name: '小腿', full_name: '小腿' },
  { id: 9, name: '颈部', full_name: '颈部' },
  { id: 10, name: '有氧', full_name: '有氧' },
  { id: 13, name: '爆发力', full_name: '爆发力' },
  { id: 14, name: '力量举', full_name: '力量举' },
  { id: 15, name: '瑜伽', full_name: '瑜伽' },
  { id: 16, name: '拉伸', full_name: '拉伸' }
];

// 默认选择的身体部位和器材 - 与 exe-list 页面保持一致
const DEFAULT_BODY_PART_ID = 2; // 胸部
const DEFAULT_EQUIPMENT_ID = 1; // 杠铃

/**
 * 获取器材分类列表
 * @returns {Array} 器材分类列表
 */
function getEquipmentCategories() {
  return EQUIPMENT_CATEGORIES;
}

/**
 * 获取身体部位分类列表
 * @returns {Array} 身体部位分类列表
 */
function getBodyPartCategories() {
  return BODY_PART_CATEGORIES;
}

/**
 * 获取默认身体部位ID
 * @returns {Number} 默认身体部位ID
 */
function getDefaultBodyPartId() {
  return DEFAULT_BODY_PART_ID;
}

/**
 * 获取默认器材ID
 * @returns {Number} 默认器材ID
 */
function getDefaultEquipmentId() {
  return DEFAULT_EQUIPMENT_ID;
}

/**
 * 初始化分类数据
 * @returns {Object} 初始化的分类数据
 */
function initCategories() {
  // 初始化身体部位映射
  const bodyPartMapping = {};
  BODY_PART_CATEGORIES.forEach(item => {
    bodyPartMapping[item.id] = item.full_name;
  });

  // 初始化器材映射
  const equipmentMapping = {};
  EQUIPMENT_CATEGORIES.forEach(item => {
    equipmentMapping[item.id] = item.name;
  });

  // 设置肌肉组选择状态
  const muscleGroups = BODY_PART_CATEGORIES.map(item => ({
    ...item,
    selected: item.id === DEFAULT_BODY_PART_ID
  }));

  // 设置器材选择状态
  const categories = EQUIPMENT_CATEGORIES.map(item => ({
    ...item,
    selected: item.id === DEFAULT_EQUIPMENT_ID
  }));

  return {
    bodyPartMapping,
    equipmentMapping,
    muscleGroups,
    categories,
    selectedBodyPartId: DEFAULT_BODY_PART_ID,
    selectedEquipmentId: DEFAULT_EQUIPMENT_ID
  };
}

/**
 * 获取动作列表数据
 * @param {Object} params - 请求参数
 * @param {Number|String} params.bodyPartId - 身体部位ID
 * @param {Number|String} params.equipmentId - 器材ID
 * @param {Number} params.page - 页码
 * @param {Boolean} params.useCache - 是否使用缓存数据
 * @param {Object} params.preloadedData - 预加载的数据
 * @returns {Promise} 包含动作列表数据的Promise
 */
async function fetchExerciseList(params) {
  try {
    const { bodyPartId, equipmentId, page = 1, useCache = false, preloadedData = {} } = params;

    // 确保 bodyPartId 是数字类型
    const bodyPartIdNum = Number(bodyPartId);

    // 定义特殊的身体部位ID列表，这些部位不需要按器材筛选
    const specialBodyPartIds = [8, 10, 13, 14, 15, 16]; // 全身、有氧、爆发力、力量举、瑜伽、拉伸
    const isSpecialBodyPart = specialBodyPartIds.includes(bodyPartIdNum);

    // 特殊处理身体部位，不传递器材ID
    const actualEquipmentId = isSpecialBodyPart ? '' : (equipmentId ? equipmentId.toString() : '');

    console.log('fetchExerciseList 参数:', {
      bodyPartId: bodyPartId.toString(),
      equipmentId: actualEquipmentId,
      page: page,
      isSpecialBodyPart
    });

    // 检查是否有缓存数据
    if (useCache) {
      const key = `${bodyPartId}_${equipmentId}`;
      const cachedData = preloadedData[key];

      if (cachedData &&
          cachedData.exercises &&
          cachedData.exercises.length > 0 &&
          !cachedData.error &&
          Date.now() - (cachedData.timestamp || 0) < 3600000) { // 1小时内的缓存有效
        console.log(`使用缓存数据: ${key}, 数据条数: ${cachedData.exercises.length}`);
        return {
          exercisesList: cachedData.exercises,
          hasMore: cachedData.hasMore,
          fromCache: true
        };
      }
    }

    // 再次验证API适配器的可用性
    if (!exerciseAdapter || typeof exerciseAdapter.fetchExerciseList !== 'function') {
      throw new Error('exerciseAdapter不可用或缺少fetchExerciseList方法');
    }

    try {
      // 准备API请求参数
      const apiParams = {
        bodyPartId: bodyPartId.toString(),
        equipmentId: actualEquipmentId,
        page: page,
        timestamp: Date.now() // 添加时间戳避免缓存
      };
      
      // 调用API适配器获取数据
      const res = await exerciseAdapter.fetchExerciseList(apiParams);
      
      console.log('API响应:', {
        success: res?.success,
        dataCount: res?.dataList?.length || 0,
        hasMore: res?.hasMore
      });

      if (!res || !res.success) {
        throw new Error(res.errorMessage || '获取数据失败');
      }

      // 处理返回的数据
      const exercises = res.dataList.map(item => {
        try {
          // 处理图片URL，添加BASE_URL前缀
          let fullImageUrl = item.image_name ? `${BASE_URL}/exercises/images/${item.image_name}` : '';
          let fullGifUrl = item.gif_url ? `${BASE_URL}/exercises/gifs/${item.gif_url}` : '';

          // 添加难度文本
          let levelText = '初级';
          if (item.level === 2) levelText = '中级';
          if (item.level === 3) levelText = '高级';

          return {
            ...item,
            full_image_url: fullImageUrl,
            full_gif_url: fullGifUrl,
            levelText
          };
        } catch (err) {
          console.error('处理动作数据时出错:', err, item);
          return item;
        }
      }).filter(Boolean); // 过滤掉可能的无效数据

      return {
        exercisesList: exercises,
        hasMore: res.hasMore,
        fromCache: false
      };
    } catch (apiError) {
      console.error('API获取动作列表失败，使用模拟数据:', apiError);

      // 使用模拟数据
      const mockExercises = getMockExercises(bodyPartIdNum);

      return {
        exercisesList: mockExercises,
        hasMore: false,
        fromCache: false,
        isMock: true
      };
    }
  } catch (error) {
    console.error('获取动作列表失败:', error);

    // 使用模拟数据作为最后的备选
    const mockExercises = getMockExercises(Number(bodyPartId) || 1);

    return {
      exercisesList: mockExercises,
      hasMore: false,
      fromCache: false,
      isMock: true,
      error: error.message
    };
  }
}

/**
 * 预加载特定分类的数据
 * @param {Number} bodyPartId - 身体部位ID
 * @param {Number} equipmentId - 器材ID
 * @param {Boolean} forceRefresh - 是否强制刷新
 * @returns {Promise<Object>} 预加载结果
 */
async function preloadCategory(bodyPartId, equipmentId, forceRefresh = false) {
  try {
    // 确保参数为数字类型
    const bodyPartIdNum = Number(bodyPartId);
    const equipmentIdNum = Number(equipmentId);

    if (isNaN(bodyPartIdNum) || isNaN(equipmentIdNum)) {
      throw new Error(`无效的参数: 身体部位=${bodyPartId}, 器材=${equipmentId}`);
    }

    // 检查缓存
    const cacheKey = `${bodyPartIdNum}_${equipmentIdNum}`;
    const cachedData = wx.getStorageSync(`preload_${cacheKey}`);

    // 如果有缓存且不强制刷新，使用缓存
    if (cachedData && !forceRefresh) {
      const parsedData = JSON.parse(cachedData);
      // 缓存有效期检查 (24小时)
      if (parsedData.timestamp && Date.now() - parsedData.timestamp < 24 * 60 * 60 * 1000) {
        console.log(`使用缓存的预加载数据 [${cacheKey}]`);
        return {
          key: cacheKey,
          data: parsedData
        };
      }
    }

    // 使用同一个exerciseAdapter实例
    if (!exerciseAdapter || typeof exerciseAdapter.fetchExerciseList !== 'function') {
      throw new Error('exerciseAdapter不可用或缺少fetchExerciseList方法');
    }

    try {
      // 准备API请求参数
      const params = {
        bodyPartId: bodyPartIdNum,
        equipmentId: equipmentIdNum,
        page: 1,
        timestamp: Date.now() // 添加时间戳避免缓存
      };

      // 调用API适配器获取数据
      const res = await exerciseAdapter.fetchExerciseList(params);

      // 检查API响应
      if (!res || !res.success) {
        throw new Error(res.errorMessage || 'API返回了无效的数据');
      }

      // 转换数据格式
      const exercises = res.dataList.map(item => {
        // 确保所有必要的字段都存在
        if (item) {
          try {
            // 处理图片URL，添加BASE_URL前缀
            let fullImageUrl = item.image_name ? `${BASE_URL}/exercises/images/${item.image_name}` : '';
            let fullGifUrl = item.gif_url ? `${BASE_URL}/exercises/gifs/${item.gif_url}` : '';

            return {
              ...item,
              full_image_url: fullImageUrl,
              full_gif_url: fullGifUrl
            };
          } catch (urlError) {
            console.error('处理图片URL出错:', urlError);
          }
          return item;
        }
      }).filter(Boolean);

      return {
        key: `${bodyPartIdNum}_${equipmentIdNum}`,
        data: {
          exercises: exercises,
          hasMore: res.hasMore,
          total: res.total || exercises.length,
          timestamp: Date.now(),
          error: false
        }
      };
    } catch (apiError) {
      console.error(`API预加载数据失败，使用模拟数据 [身体部位=${bodyPartId}, 器材=${equipmentId}]:`, apiError);

      // 使用模拟数据
      const mockExercises = getMockExercises(bodyPartIdNum);

      return {
        key: `${bodyPartId}_${equipmentId}`,
        data: {
          exercises: mockExercises,
          hasMore: false,
          total: mockExercises.length,
          timestamp: Date.now(),
          error: false
        }
      };
    }
  } catch (error) {
    console.error(`预加载分类数据失败 [身体部位=${bodyPartId}, 器材=${equipmentId}]:`, error);

    // 使用模拟数据作为最后的备选
    const mockExercises = getMockExercises(Number(bodyPartId) || 1);

    return {
      key: `${bodyPartId}_${equipmentId}`,
      data: {
        exercises: mockExercises,
        hasMore: false,
        total: mockExercises.length,
        timestamp: Date.now(),
        error: true
      }
    };
  }
}

/**
 * 组织动作数据，按类别分组
 * @param {Array} exercisesList - 动作列表
 * @returns {Object} 组织后的数据
 */
function organizeExercises(exercisesList) {
  if (!exercisesList || exercisesList.length === 0) {
    return {
      exerciseGroups: [],
      showEmptyState: true
    };
  }

  // 按类别分组
  const groupedExercises = {};
  exercisesList.forEach(exercise => {
    const category = exercise.move_cate || '未分类';
    if (!groupedExercises[category]) {
      groupedExercises[category] = [];
    }
    groupedExercises[category].push(exercise);
  });

  // 转换为数组格式
  const exerciseGroups = Object.keys(groupedExercises).map((category, index) => ({
    index: index + 1,
    category,
    exercises: groupedExercises[category]
  }));

  return {
    exerciseGroups,
    showEmptyState: exerciseGroups.length === 0
  };
}

/**
 * 处理图片加载错误
 * @param {Array} exercisesList - 动作列表
 * @param {String} id - 动作ID
 * @returns {Array} 更新后的动作列表
 */
function handleImageError(exercisesList, id) {
  const updatedList = [...exercisesList];
  const exerciseIndex = updatedList.findIndex(item => item.id == id);

  if (exerciseIndex !== -1) {
    // 替换为默认图片，使用完整URL
    updatedList[exerciseIndex].full_image_url = `${BASE_URL}/exercises/images/default.jpg`;
  }

  return updatedList;
}

/**
 * 处理已选动作图片加载错误
 * @param {Object} selectedExercises - 已选动作对象
 * @param {String} id - 动作ID
 * @returns {Object} 更新后的已选动作对象
 */
function handleSelectedImageError(selectedExercises, id) {
  const updatedSelected = { ...selectedExercises };

  if (updatedSelected[id]) {
    // 替换为默认图片，使用完整URL
    updatedSelected[id].imageUrl = `${BASE_URL}/exercises/images/default.jpg`;
  }

  return updatedSelected;
}

/**
 * 获取模拟数据
 * @param {Number} bodyPartId - 身体部位ID
 * @returns {Array} 模拟的动作数据
 */
function getMockExercises(bodyPartId) {
  // 使用默认头像作为占位图
  const DEFAULT_AVATAR = '/images/defaults/default-avatar.png';
  // 使用完整的URL路径
  const DEFAULT_IMAGE_URL = `${BASE_URL}/exercises/images/default.jpg`;
  const DEFAULT_GIF_URL = `${BASE_URL}/exercises/gifs/default.gif`;

  // 根据身体部位返回不同的模拟数据
  const mockData = {
    // 胸部
    1: [
      {
        id: '101',
        name: '杠铃卧推',
        move_cate: '胸部',
        level: 2,
        levelText: '中级',
        full_image_url: DEFAULT_IMAGE_URL,
        full_gif_url: DEFAULT_GIF_URL
      },
      {
        id: '102',
        name: '哑铃飞鸟',
        move_cate: '胸部',
        level: 1,
        levelText: '初级',
        full_image_url: DEFAULT_IMAGE_URL,
        full_gif_url: DEFAULT_GIF_URL
      },
      {
        id: '103',
        name: '俯卧撑',
        move_cate: '胸部',
        level: 1,
        levelText: '初级',
        full_image_url: DEFAULT_IMAGE_URL,
        full_gif_url: DEFAULT_GIF_URL
      }
    ],
    // 背部
    2: [
      {
        id: '201',
        name: '引体向上',
        move_cate: '背部',
        level: 2,
        levelText: '中级',
        full_image_url: DEFAULT_IMAGE_URL,
        full_gif_url: DEFAULT_GIF_URL
      },
      {
        id: '202',
        name: '杠铃划船',
        move_cate: '背部',
        level: 2,
        levelText: '中级',
        full_image_url: DEFAULT_IMAGE_URL,
        full_gif_url: DEFAULT_GIF_URL
      }
    ],
    // 肩部
    3: [
      {
        id: '301',
        name: '肩上推举',
        move_cate: '肩部',
        level: 2,
        levelText: '中级',
        full_image_url: DEFAULT_IMAGE_URL,
        full_gif_url: DEFAULT_GIF_URL
      },
      {
        id: '302',
        name: '侧平举',
        move_cate: '肩部',
        level: 1,
        levelText: '初级',
        full_image_url: DEFAULT_IMAGE_URL,
        full_gif_url: DEFAULT_GIF_URL
      }
    ],
    // 手臂
    4: [
      {
        id: '401',
        name: '二头弯举',
        move_cate: '手臂',
        level: 1,
        levelText: '初级',
        full_image_url: DEFAULT_IMAGE_URL,
        full_gif_url: DEFAULT_GIF_URL
      },
      {
        id: '402',
        name: '三头下压',
        move_cate: '手臂',
        level: 1,
        levelText: '初级',
        full_image_url: DEFAULT_IMAGE_URL,
        full_gif_url: DEFAULT_GIF_URL
      }
    ],
    // 腿部
    5: [
      {
        id: '501',
        name: '深蹲',
        move_cate: '腿部',
        level: 2,
        levelText: '中级',
        full_image_url: DEFAULT_IMAGE_URL,
        full_gif_url: DEFAULT_GIF_URL
      },
      {
        id: '502',
        name: '腿举',
        move_cate: '腿部',
        level: 1,
        levelText: '初级',
        full_image_url: DEFAULT_IMAGE_URL,
        full_gif_url: DEFAULT_GIF_URL
      }
    ],
    // 臀部
    6: [
      {
        id: '601',
        name: '臀桥',
        move_cate: '臀部',
        level: 1,
        levelText: '初级',
        full_image_url: DEFAULT_IMAGE_URL,
        full_gif_url: DEFAULT_GIF_URL
      }
    ],
    // 核心
    7: [
      {
        id: '701',
        name: '卷腹',
        move_cate: '核心',
        level: 1,
        levelText: '初级',
        full_image_url: DEFAULT_IMAGE_URL,
        full_gif_url: DEFAULT_GIF_URL
      },
      {
        id: '702',
        name: '平板支撑',
        move_cate: '核心',
        level: 1,
        levelText: '初级',
        full_image_url: DEFAULT_IMAGE_URL,
        full_gif_url: DEFAULT_GIF_URL
      }
    ],
    // 全身
    8: [
      {
        id: '801',
        name: '负重深蹲',
        move_cate: '全身',
        level: 3,
        levelText: '高级',
        full_image_url: DEFAULT_IMAGE_URL,
        full_gif_url: DEFAULT_GIF_URL
      },
      {
        id: '802',
        name: '波比跳',
        move_cate: '全身',
        level: 2,
        levelText: '中级',
        full_image_url: DEFAULT_IMAGE_URL,
        full_gif_url: DEFAULT_GIF_URL
      }
    ]
  };

  // 返回对应身体部位的模拟数据，如果没有则返回空数组
  return mockData[bodyPartId] || [];
}

/**
 * 搜索动作
 * @param {Array} exercisesList - 动作列表
 * @param {String} searchQuery - 搜索关键词
 * @returns {Array} 过滤后的动作列表
 */
function searchExercises(exercisesList, searchQuery) {
  if (!searchQuery) {
    return exercisesList;
  }

  return exercisesList.filter(exercise =>
    exercise.name.indexOf(searchQuery) >= 0 ||
    (exercise.move_cate && exercise.move_cate.indexOf(searchQuery) >= 0)
  );
}

module.exports = {
  getEquipmentCategories,
  getBodyPartCategories,
  getDefaultBodyPartId,
  getDefaultEquipmentId,
  initCategories,
  fetchExerciseList,
  preloadCategory,
  organizeExercises,
  handleImageError,
  handleSelectedImageError,
  getMockExercises,
  searchExercises
};
