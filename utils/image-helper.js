/**
 * 图片处理工具类
 * 提供图片URL处理和缓存功能
 */

const requestApi = require('../api/request');

/**
 * 处理图片URL，确保使用完整路径
 * @param {string} imageUrl - 原始图片URL
 * @param {string} defaultImage - 默认图片路径
 * @returns {string} 处理后的图片URL
 */
function getFullImageUrl(imageUrl, defaultImage = '/images/exercises/default.png') {
  if (!imageUrl) {
    return defaultImage;
  }

  // 如果已经是完整URL，直接返回
  if (imageUrl.startsWith('http://') || imageUrl.startsWith('https://')) {
    return imageUrl;
  }

  // 如果是相对路径，添加BASE_URL前缀
  if (imageUrl.startsWith('/')) {
    return requestApi.BASE_URL + imageUrl;
  }

  // 其他情况，添加BASE_URL和/前缀
  return requestApi.BASE_URL + '/' + imageUrl;
}

/**
 * 处理图片加载错误
 * @param {string} imageUrl - 原始图片URL
 * @param {string} defaultImage - 默认图片路径
 * @returns {string} 备用图片URL
 */
function handleImageError(imageUrl, defaultImage = '/images/exercises/default.png') {
  // 如果图片URL包含http但不包含https，尝试将http替换为https
  if (imageUrl && imageUrl.includes('http:') && !imageUrl.includes('https:')) {
    return imageUrl.replace('http:', 'https:');
  }
  
  // 如果无法修复，使用默认图片
  return defaultImage;
}

/**
 * 缓存训练计划数据
 * @param {string} planId - 计划ID
 * @param {Object} planData - 计划数据
 */
function cachePlanData(planId, planData) {
  if (!planId || !planData) {
    console.error('缓存训练计划数据失败: 无效的参数');
    return;
  }
  
  try {
    // 缓存计划数据
    const cacheKey = `plan_${planId}`;
    const cacheData = {
      timestamp: Date.now(),
      data: planData
    };
    
    wx.setStorageSync(cacheKey, cacheData);
    console.log(`训练计划数据已缓存: ${cacheKey}`);
  } catch (error) {
    console.error('缓存训练计划数据失败:', error);
  }
}

/**
 * 获取缓存的训练计划数据
 * @param {string} planId - 计划ID
 * @param {number} maxAge - 最大缓存时间(毫秒)，默认10分钟
 * @returns {Object|null} 缓存的计划数据，如果不存在或已过期则返回null
 */
function getCachedPlanData(planId, maxAge = 10 * 60 * 1000) {
  if (!planId) {
    return null;
  }
  
  try {
    // 获取缓存数据
    const cacheKey = `plan_${planId}`;
    const cacheData = wx.getStorageSync(cacheKey);
    
    if (!cacheData || !cacheData.timestamp || !cacheData.data) {
      return null;
    }
    
    // 检查缓存是否过期
    const now = Date.now();
    if (now - cacheData.timestamp > maxAge) {
      console.log(`缓存数据已过期: ${cacheKey}`);
      return null;
    }
    
    console.log(`使用缓存的训练计划数据: ${cacheKey}`);
    return cacheData.data;
  } catch (error) {
    console.error('获取缓存的训练计划数据失败:', error);
    return null;
  }
}

module.exports = {
  getFullImageUrl,
  handleImageError,
  cachePlanData,
  getCachedPlanData
};
