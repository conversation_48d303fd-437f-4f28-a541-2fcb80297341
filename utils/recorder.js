/**
 * 视频录制工具模块
 * 提供页面录制、视频处理和上传功能
 */

// 录制配置
const RECORD_DURATION = 6; // 固定录制时长（秒）
const FRAME_RATE = 30; // 帧率
const VIDEO_BITRATE = 8000000; // 视频比特率 (8Mbps)

/**
 * 初始化录制器
 * @param {Object} canvas - 画布对象
 * @returns {Object} 录制器实例
 */
function initRecorder(canvas) {
  try {
    // 检查环境是否支持录制功能
    if (!wx.createMediaRecorder) {
      throw new Error('当前环境不支持录制功能');
    }

    // 创建录制器
    const recorder = wx.createMediaRecorder(canvas, {
      fps: FRAME_RATE,
      videoBitsPerSecond: VIDEO_BITRATE,
      width: canvas.width,
      height: canvas.height
    });

    return recorder;
  } catch (error) {
    console.error('初始化录制器失败:', error);
    throw error;
  }
}

/**
 * 开始录制
 * @param {Object} canvas - 画布对象
 * @param {Function} onProgress - 进度回调函数
 * @param {Function} onComplete - 完成回调函数
 * @param {Function} onError - 错误回调函数
 */
function startRecording(canvas, onProgress, onComplete, onError) {
  try {
    // 初始化录制器
    const recorder = initRecorder(canvas);
    let recordedChunks = [];
    
    // 设置数据可用事件处理
    recorder.ondataavailable = (event) => {
      if (event.data && event.data.size > 0) {
        recordedChunks.push(event.data);
      }
    };
    
    // 设置录制停止事件处理
    recorder.onstop = () => {
      if (recordedChunks.length === 0) {
        onError(new Error('没有录制到任何数据'));
        return;
      }
      
      // 创建视频Blob
      const videoBlob = new Blob(recordedChunks, { 
        type: recorder.mimeType || 'video/mp4' 
      });
      
      // 将Blob转换为临时文件
      const tempFilePath = wx.env.USER_DATA_PATH + '/temp_video.mp4';
      wx.getFileSystemManager().writeFile({
        filePath: tempFilePath,
        data: videoBlob,
        success: () => {
          onComplete(tempFilePath);
        },
        fail: (error) => {
          onError(error);
        }
      });
    };
    
    // 开始录制
    recorder.start();
    
    // 进度更新
    let currentProgress = 0;
    const progressInterval = 100; // 每100ms更新一次进度
    const progressTimer = setInterval(() => {
      currentProgress += progressInterval;
      const progressPercent = Math.min(Math.floor((currentProgress / (RECORD_DURATION * 1000)) * 100), 99);
      
      onProgress(progressPercent);
      
      if (currentProgress >= RECORD_DURATION * 1000) {
        clearInterval(progressTimer);
        recorder.stop();
      }
    }, progressInterval);
    
    // 返回控制对象
    return {
      stop: () => {
        clearInterval(progressTimer);
        recorder.stop();
      }
    };
  } catch (error) {
    onError(error);
    return null;
  }
}

/**
 * 捕获静态图像
 * @param {Object} canvas - 画布对象
 * @returns {String} 图像的base64数据
 */
function captureStillImage(canvas) {
  try {
    // 创建临时canvas捕获当前帧
    const stillCanvas = wx.createOffscreenCanvas({
      width: canvas.width,
      height: canvas.height
    });
    const stillCtx = stillCanvas.getContext('2d');
    stillCtx.drawImage(canvas, 0, 0);
    
    // 将图像转为base64
    return stillCanvas.toDataURL('image/jpeg', 0.95);
  } catch (error) {
    console.error('捕获静态图像失败:', error);
    return null;
  }
}

module.exports = {
  startRecording,
  captureStillImage,
  RECORD_DURATION
};
