/**
 * 训练计划工具类
 * @file training-plan-helper.js
 * @description 提供训练计划相关的共享功能，减少代码冗余
 */

const imageHelper = require('./image-helper');
const ExerciseDataHelper = require('./exercise-data-helper');

/**
 * 处理传入的计划数据，转换为标准格式
 * @param {Object} plan - 原始计划数据
 * @returns {Object} 处理后的计划数据
 */
function processPlanData(plan) {
  try {
    console.log('处理计划数据:', plan);

    // 创建一个包含workouts的planDetail
    const planDetail = {
      ...plan
    };

    // 确保workouts字段存在且是数组
    if (!planDetail.workouts || !Array.isArray(planDetail.workouts)) {
      planDetail.workouts = [];
    }

    // 如果存在training_params，确保related_plan_id是plan的id
    if (plan.training_params) {
      planDetail.training_params = {
        ...plan.training_params
      };
    }

    // 如果workouts为空但有exercises，创建一个模拟的workout包含这些exercises
    if (planDetail.workouts.length === 0 && plan.exercises && plan.exercises.length > 0) {
      console.log('没有workouts但有exercises，创建模拟workout');

      // 创建一个默认workout
      const workout = {
        id: plan.id, // 使用plan的ID
        training_plan_id: plan.training_params?.related_plan_id || plan.id,
        name: plan.name,
        day_of_week: 1,
        day_number: 1,
        description: plan.description,
        scheduled_date: new Date().toISOString().split('T')[0],
        status: "not_started",
        exercises: []
      };

      // 转换exercises为workout格式
      plan.exercises.forEach((exercise, index) => {
        // 解析组数和次数
        const setsReps = parseSets(exercise.display_sets);
        const setCount = setsReps.setCount || 3;
        const reps = setsReps.reps || 12;

        // 创建workout exercise
        const workoutExercise = {
          id: exercise.id, // 保留exercise原始ID
          exercise_id: exercise.exercise_id || exercise.id,
          workout_id: workout.id,
          sets: setCount,
          reps: reps,
          rest_seconds: 60,
          order: index + 1,
          notes: exercise.notes || "",
          exercise_type: "weight_reps",
          weight: exercise.weight || 20,
          exercise_name: exercise.name,
          image_url: exercise.image_url || '/images/exercises/default.png',
          exercise_description: exercise.description || "",
          set_records: []
        };

        // 创建set_records
        for (let i = 0; i < setCount; i++) {
          workoutExercise.set_records.push({
            id: `${exercise.id}_set_${i+1}`,
            workout_exercise_id: exercise.id,
            set_number: i + 1,
            set_type: i === 0 ? 'warmup' : 'normal',
            weight: i === 0 ? (exercise.weight || 20) * 0.8 : (exercise.weight || 20),
            reps: i === 0 ? reps + 2 : reps,
            completed: false,
            notes: ""
          });
        }

        // 添加到workout
        workout.exercises.push(workoutExercise);
      });

      // 添加workout到planDetail
      planDetail.workouts.push(workout);
    }

    // 确保每个workout中的exercises都有正确的数据
    if (planDetail.workouts && planDetail.workouts.length > 0) {
      console.log('处理workouts中的exercises数据');

      planDetail.workouts.forEach(workout => {
        // 确保exercises字段存在且是数组
        if (!workout.exercises || !Array.isArray(workout.exercises)) {
          console.log(`Workout ${workout.id} 没有exercises数组，创建空数组`);
          workout.exercises = [];
        } else {
          console.log(`Workout ${workout.id} 有 ${workout.exercises.length} 个exercises`);
        }
      });
    }

    console.log('处理后的计划详情:', planDetail);
    return planDetail;
  } catch (error) {
    console.error('处理计划数据失败:', error);
    throw error;
  }
}

/**
 * 解析display_sets字符串为sets数组
 * @param {string} displaySets - 显示的组数字符串，如 "3组x12次"
 * @returns {Object} 解析后的组数和次数
 */
function parseSets(displaySets) {
  try {
    // 解析格式如 "3组x12次" 的字符串
    const match = displaySets ? displaySets.match(/(\d+)组x(\d+)次/) : null;
    if (!match) return { setCount: 3, reps: 12 };

    return {
      setCount: parseInt(match[1]),
      reps: parseInt(match[2])
    };
  } catch (error) {
    console.error('解析组数据失败:', error);
    return { setCount: 3, reps: 12 };
  }
}

/**
 * 根据workouts长度决定显示内容
 * @param {Object} planDetail - 计划详情
 * @param {Object} plan - 原始计划数据
 * @returns {Object} 显示用的计划信息
 */
function getPlanDisplayInfo(planDetail, plan) {
  let title, description, duration;

  // 根据workouts长度决定显示内容
  if (planDetail.workouts && planDetail.workouts.length === 1) {
    // 单日计划：显示workout的name作为标题
    const workout = planDetail.workouts[0];
    title = workout.name || plan.name || '训练计划';
    description = workout.description || plan.description || '';
    duration = workout.estimated_duration || plan.estimated_duration || 60;
    console.log('单日计划，使用workout的name作为标题:', title);
  } else {
    // 多日计划：显示计划的name作为标题
    title = plan.plan_name || plan.name || '训练计划';
    description = plan.description || '';
    duration = plan.estimated_duration || 60;
    console.log('多日计划，使用plan的name作为标题:', title);
  }

  return {
    id: plan.training_params?.related_plan_id || plan.id,
    title: title,
    duration: duration,
    description: description
  };
}

/**
 * 计算单个运动的总容量
 * @param {Array} sets - 组数据数组
 * @returns {number} 总容量
 */
function calculateTotalVolume(sets) {
  return sets.reduce((total, set) => {
    return total + (set.weight * set.reps)
  }, 0)
}

/**
 * 准备更新数据，将UI数据结构转换为后端所需的格式
 * @param {Object} planDetail - 计划详情数据
 * @param {Object} options - 额外选项，如状态更新、日期等
 * @param {string} planId - 计划ID
 * @returns {Object} 符合后端要求的更新数据结构
 */
function prepareUpdateData(planDetail, options = {}, planId) {
  try {
    if (!planDetail || !planId) {
      console.error('计划数据不完整');
      return null;
    }
    console.log('【prepareUpdateData】处理计划数据:', planDetail);

    // 初始化更新数据结构
    const updateData = {
      plan_data: {
        id: planId // 使用正确的planId
      },
      workout_data: [],
      workout_exercise_data: [],
      training_record_data: [],
      set_record_data: []
    };

    // 更新计划基本信息
    if (options.status) {
      updateData.plan_data.status = options.status;
    }

    if (options.is_active !== undefined) {
      updateData.plan_data.is_active = options.is_active;
    }

    if (options.start_date) {
      updateData.plan_data.start_date = options.start_date;
    }

    if (options.end_date) {
      updateData.plan_data.end_date = options.end_date;
    }

    // 如果有workouts数据，处理workout相关数据
    if (planDetail.workouts && planDetail.workouts.length > 0) {
      planDetail.workouts.forEach(workout => {
        // 添加workout数据
        const workoutData = {
          id: workout.id
        };

        // 更新workout状态
        if (options.workout_status) {
          workoutData.status = options.workout_status;
        }

        // 更新workout日期
        if (options.scheduled_date) {
          workoutData.scheduled_date = options.scheduled_date;
        }

        updateData.workout_data.push(workoutData);

        // 处理workout exercises
        if (workout.exercises && workout.exercises.length > 0) {
          workout.exercises.forEach(exercise => {
            // 添加workout exercise数据
            const exerciseData = {
              id: exercise.id,
              workout_id: workout.id
            };

            // 检查是否是新添加的动作（ID包含下划线分隔符）
            const isNewExercise = exercise.id && String(exercise.id).includes('_');
            console.log('[DEBUG] 处理workout_exercise:', exercise.id, '是否为新添加动作:', isNewExercise);

            // 对于新添加的动作，必须包含exercise_id字段
            if (isNewExercise) {
              exerciseData.exercise_id = exercise.exercise_id;
              console.log('[DEBUG] 添加exercise_id字段:', exercise.exercise_id);
            } else if (exercise.exercise_id) {
              // 对于非新添加的动作，如果有exercise_id也添加
              exerciseData.exercise_id = exercise.exercise_id;
            }

            // 如果有更新，添加相应字段
            if (exercise.sets) exerciseData.sets = exercise.sets;
            if (exercise.reps) exerciseData.reps = exercise.reps;
            if (exercise.rest_seconds) exerciseData.rest_seconds = exercise.rest_seconds;
            if (exercise.notes) exerciseData.notes = exercise.notes;
            if (exercise.weight) exerciseData.weight = exercise.weight;

            updateData.workout_exercise_data.push(exerciseData);

            // 处理set records
            if (exercise.set_records && exercise.set_records.length > 0) {
              exercise.set_records.forEach(record => {
                // 添加set record数据
                const recordData = {
                  id: record.id,
                  workout_exercise_id: exercise.id
                };

                // 如果record中有自己的workout_exercise_id字段，优先使用它
                if (record.workout_exercise_id) {
                  recordData.workout_exercise_id = record.workout_exercise_id;
                  console.log('[DEBUG] 使用record中的workout_exercise_id:', record.workout_exercise_id);
                }

                // 如果有更新，添加相应字段
                if (record.set_type) recordData.set_type = record.set_type;
                if (record.weight !== undefined) recordData.weight = record.weight;
                if (record.reps !== undefined) recordData.reps = record.reps;
                if (record.completed !== undefined) recordData.completed = record.completed;
                if (record.notes) recordData.notes = record.notes;

                updateData.set_record_data.push(recordData);
              });
            }
          });
        }

        // 如果需要添加训练记录
        if (options.add_training_record) {
          updateData.training_record_data.push({
            workout_id: workout.id,
            status: options.training_record_status || 'not_started'
          });
        }
      });
    }

    // 移除空数组，减少数据传输量
    if (updateData.workout_data.length === 0) delete updateData.workout_data;
    if (updateData.workout_exercise_data.length === 0) delete updateData.workout_exercise_data;
    if (updateData.training_record_data.length === 0) delete updateData.training_record_data;
    if (updateData.set_record_data.length === 0) delete updateData.set_record_data;

    console.log('准备的更新数据:', updateData);
    return updateData;
  } catch (error) {
    console.error('准备更新数据失败:', error);
    return null;
  }
}

/**
 * 从workouts数据中处理exercises
 * @param {Array} workouts - workouts数组
 * @returns {Object} 处理结果
 */
function processExercisesFromWorkouts(workouts) {
  try {
    console.log('处理workouts中的exercises，workouts数量:', workouts?.length || 0);

    if (!workouts || !Array.isArray(workouts) || workouts.length === 0) {
      console.log('没有workouts数据，设置exercises为空数组');
      return {
        exercises: [],
        planDetail: null,
        isMultiDay: false
      };
    }

    // 处理多日计划的情况
    if (workouts.length > 1) {
      console.log('多日计划，处理每个训练日');

      // 为每个workout添加isExpanded属性和processedExercises数组
      const processedWorkouts = workouts.map(workout => {
        // 确保有exercises数据
        if (!workout.exercises || !Array.isArray(workout.exercises)) {
          workout.exercises = [];
        }

        // 处理每个运动
        const processedExercises = workout.exercises.map(exercise => {
          return ExerciseDataHelper.createStandardExercise(exercise, {
            calculateTotalVolume: calculateTotalVolume
          });
        });

        // 添加处理后的exercises到workout
        return {
          ...workout,
          isExpanded: false, // 默认折叠
          processedExercises: processedExercises
        };
      });

      return {
        exercises: [],
        planDetail: { workouts: processedWorkouts },
        isMultiDay: true
      };
    }
    // 单日计划的情况
    else if (workouts.length === 1) {
      console.log('单日计划，直接处理exercises');
      const workout = workouts[0];

      // 确保有exercises数据
      if (workout.exercises && Array.isArray(workout.exercises)) {
        // 处理每个运动
        const processedExercises = workout.exercises.map(exercise => {
          return ExerciseDataHelper.createStandardExercise(exercise, {
            calculateTotalVolume: calculateTotalVolume
          });
        });

        return {
          exercises: processedExercises,
          planDetail: null,
          isMultiDay: false
        };
      }
    }

    return {
      exercises: [],
      planDetail: null,
      isMultiDay: false
    };
  } catch (error) {
    console.error('处理运动数据失败:', error);
    return {
      exercises: [],
      planDetail: null,
      isMultiDay: false
    };
  }
}

/**
 * 处理选择的动作数据，添加到训练计划中
 * @param {Object} params - 参数对象
 * @returns {Object} 处理结果
 */
function handleSelectedExercises(params) {
  const {
    exercises,
    setType,
    planDetail,
    currentExercises,
    planId
  } = params;

  try {
    console.log('[DEBUG] handleSelectedExercises 开始处理数据:', JSON.stringify({ exercisesCount: exercises.length, setType, planId }));

    // 确保planDetail和workouts存在
    if (!planDetail || !planDetail.workouts || planDetail.workouts.length === 0) {
      console.error('[DEBUG] planDetail数据不完整，无法添加动作');
      throw new Error('planDetail数据不完整');
    }

    // 获取当前workout
    const workout = planDetail.workouts[0];
    const workoutId = workout.id;

    // 将选择的动作添加到当前计划中
    const newExercises = [...currentExercises];

    // 为每个选择的动作创建默认的组数据
    exercises.forEach((exercise, index) => {
      // 处理图片URL
      const processedImageUrl = ExerciseDataHelper.processExerciseImageUrl(exercise);

      // 生成一致的ID
      const exerciseId = ExerciseDataHelper.generateExerciseId(exercise, workoutId);
      console.log('[DEBUG] 生成的运动ID:', exerciseId);

      // 创建默认组数据
      const defaultSets = ExerciseDataHelper.createStandardSets({
        setType: setType,
        setCount: 3,
        defaultReps: 12,
        defaultWeight: 20
      });

      // 创建新的UI运动对象
      const newExercise = {
        id: exerciseId,
        name: exercise.name,
        imageUrl: processedImageUrl,
        category: exercise.category || '',
        isExpanded: false,
        sets: defaultSets,
        totalVolume: calculateTotalVolume(defaultSets)
      };

      // 添加到UI运动列表
      newExercises.push(newExercise);

      // 创建workout exercise对象
      const workoutExercise = ExerciseDataHelper.createWorkoutExercise({
        exercise: exercise,
        workoutId: workoutId,
        exerciseId: exerciseId,
        order: (workout.exercises ? workout.exercises.length : 0) + index + 1,
        setCount: 3,
        defaultReps: 12,
        defaultWeight: 20,
        setType: setType
      });

      // 添加到planDetail的workouts中
      if (!workout.exercises) {
        workout.exercises = [];
      }
      workout.exercises.push(workoutExercise);
    });

    return {
      success: true,
      newExercises: newExercises,
      updatedPlanDetail: planDetail
    };
  } catch (error) {
    console.error('[DEBUG] 处理选择的动作数据失败:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

module.exports = {
  processPlanData,
  parseSets,
  getPlanDisplayInfo,
  calculateTotalVolume,
  prepareUpdateData,
  processExercisesFromWorkouts,
  handleSelectedExercises
};
