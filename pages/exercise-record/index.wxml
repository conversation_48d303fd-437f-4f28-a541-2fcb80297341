<view class="container">
  <!-- 顶部动作信息 -->
  <view class="exercise-info">
    <view class="header">
      <view class="title">{{exerciseName}}</view>
      <view class="mode-indicator" wx:if="{{isAppendMode}}">追加组数模式</view>
    </view>
    
    <!-- 动作动画/图片 -->
    <view class="exercise-media">
      <image 
        class="{{exerciseGif ? 'exercise-gif' : 'exercise-image'}}" 
        src="{{exerciseGif || exerciseImage}}" 
        mode="{{exerciseGif ? 'aspectFill' : 'aspectFit'}}" 
        lazy-load="true"
      />
    </view>
  </view>
  
  <!-- 训练组数记录 -->
  <view class="sets-container">
    <view class="sets-header">
      <text class="set-label">组数</text>
      <text class="weight-label">重量 (kg)</text>
      <text class="reps-label">次数</text>
      <text class="operation-label">操作</text>
    </view>
    
    <block wx:for="{{sets}}" wx:key="index">
      <view class="set-row {{index < originalSetsCount && isAppendMode ? 'original-set' : ''}}">
        <text class="set-label">第{{index + 1}}组</text>
        
        <view class="weight-input-container">
          <slider class="weight-slider" min="0" max="200" value="{{item.weight}}" show-value="{{false}}" block-size="20" activeColor="#07c160" backgroundColor="#e0e0e0" bindchange="onWeightChange" data-index="{{index}}"/>
          <input class="weight-input" type="digit" value="{{item.weight}}" bindinput="onWeightInput" data-index="{{index}}"/>
        </view>
        
        <view class="reps-input-container">
          <slider class="reps-slider" min="0" max="100" value="{{item.reps}}" show-value="{{false}}" block-size="20" activeColor="#07c160" backgroundColor="#e0e0e0" bindchange="onRepsChange" data-index="{{index}}"/>
          <input class="reps-input" type="number" value="{{item.reps}}" bindinput="onRepsInput" data-index="{{index}}"/>
        </view>
        
        <view class="operation-button">
          <view class="delete-set-button" bindtap="deleteSet" data-index="{{index}}">
            <image src="/images/icons/delete.png" class="delete-icon"></image>
          </view>
        </view>
      </view>
    </block>
  </view>
  
  <!-- 添加组数按钮 -->
  <view class="add-set-button" bindtap="addSet">
    <image src="/images/icons/plus.png" class="add-icon"></image>
    <text>添加组数</text>
  </view>
  
  <!-- 保存按钮 -->
  <view class="btn-save-container">
    <button class="btn-save" bindtap="saveRecord">保存记录</button>
  </view>
</view> 