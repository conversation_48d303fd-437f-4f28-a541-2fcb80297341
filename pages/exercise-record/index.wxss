.container {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 30rpx;
  box-sizing: border-box;
  height: 100vh;
  background-color: #f8f8f8;
}

/* 动作信息区域 */
.exercise-info {
  width: 100%;
  background-color: #fff;
  border-radius: 15rpx;
  padding: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
  margin-bottom: 30rpx;
}

.exercise-header {
  display: flex;
  justify-content: center;
  align-items: center;
  padding-bottom: 20rpx;
}

.exercise-name {
  font-size: 32rpx;
  font-weight: bold;
  text-align: center;
}

.exercise-media {
  width: 100%;
  height: 300rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: hidden;
  background-color: #f5f5f5;
  border-radius: 10rpx;
}

.exercise-gif {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.exercise-image {
  max-width: 100%;
  max-height: 300rpx;
  object-fit: contain;
}

/* 训练组数记录区域 */
.sets-container {
  width: 100%;
  margin-top: 30rpx;
  background-color: #fff;
  border-radius: 16rpx;
  padding: 20rpx;
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.1);
}

.sets-header {
  display: flex;
  padding: 15rpx 0;
  border-bottom: 1rpx solid #eee;
  font-weight: bold;
  font-size: 28rpx;
}

.set-label {
  width: 20%;
  text-align: center;
}

.weight-label, .reps-label {
  width: 30%;
  text-align: center;
}

.operation-label {
  width: 20%;
  text-align: center;
}

.set-row {
  display: flex;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f5f5f5;
  align-items: center;
}

.set-row.original-set {
  background-color: rgba(7, 193, 96, 0.05);
}

.weight-input-container, .reps-input-container {
  width: 30%;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.weight-slider, .reps-slider {
  width: 100%;
  margin: 0;
}

.weight-input, .reps-input {
  width: 80rpx;
  height: 60rpx;
  text-align: center;
  background-color: #f8f8f8;
  border-radius: 8rpx;
  margin-top: 10rpx;
  font-size: 28rpx;
}

.operation-button {
  width: 20%;
  display: flex;
  justify-content: center;
}

.delete-set-button {
  width: 60rpx;
  height: 60rpx;
  background-color: #ffebee;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.delete-icon {
  width: 32rpx;
  height: 32rpx;
}

.add-set-button {
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 30rpx auto;
  width: 60%;
  height: 90rpx;
  background-color: #e8f5e9;
  color: #07c160;
  border-radius: 45rpx;
  font-size: 30rpx;
}

.add-icon {
  width: 36rpx;
  height: 36rpx;
  margin-right: 10rpx;
}

.mode-indicator {
  font-size: 24rpx;
  color: #07c160;
  background-color: rgba(7, 193, 96, 0.1);
  padding: 6rpx 16rpx;
  border-radius: 20rpx;
  margin-top: 8rpx;
  display: inline-block;
}

.header {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 20rpx;
}

.title {
  font-size: 36rpx;
  font-weight: bold;
  text-align: center;
}

/* 保存按钮 */
.btn-save-container {
  width: 100%;
  padding: 20rpx 0;
}

.btn-save {
  width: 100%;
  height: 90rpx;
  line-height: 90rpx;
  background-color: #07c160;
  color: #fff;
  font-size: 32rpx;
  border-radius: 45rpx;
} 