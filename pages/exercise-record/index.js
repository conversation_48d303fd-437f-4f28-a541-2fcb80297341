// 引入API配置
const { API_BASE_URL } = require('../../api/config');

Page({
  data: {
    exerciseId: '', // 动作ID
    exerciseName: '', // 动作名称
    exerciseGif: '', // 动作GIF
    exerciseImage: '', // 动作图片
    recordId: '', // 记录ID（编辑模式）
    returnPath: '', // 记录返回路径，用于保存后跳转
    fileIDPrefix: 'cloud://sciencefit-3grq7tkic3524425.7363-sciencefit-3grq7tkic3524425-1328011169/sciencefit/',
    exerciseData: null, // 完整动作数据
    isAppendMode: false, // 是否为追加组数模式
    originalSetsCount: 0, // 原始组数数量
    sets: [
      { weight: 0, reps: 12 },
      { weight: 0, reps: 12 },
      { weight: 0, reps: 12 },
      { weight: 0, reps: 12 }
    ],
    returnToCustomizePlan: false // 是否返回训练计划页面
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    console.log('训练记录页面加载参数:', options);

    const { id, name, recordId, appendSets, returnToCustomizePlan } = options;
    
    // 对传递的名称进行URL解码
    const decodedName = name ? decodeURIComponent(name) : '未命名训练';
    
    // 设置界面标题
    wx.setNavigationBarTitle({
      title: decodedName
    });
    
    this.setData({
      exerciseId: id,
      exerciseName: decodedName,
      recordId: recordId || '',
      isAppendMode: appendSets === 'true',
      returnToCustomizePlan: returnToCustomizePlan === 'true' || false
    });
    
    // 如果是编辑已有记录，获取记录数据
    if (recordId) {
      // 检查eventChannel是否传递了记录数据
      const eventChannel = this.getOpenerEventChannel();
      
      if (eventChannel && typeof eventChannel.on === 'function') {
        eventChannel.on('acceptRecordData', (data) => {
          console.log('接收到记录数据:', data);
          if (data && data.record) {
            this.initRecordData(data.record, data.appendSets);
          } else {
            this.fetchRecordById(recordId);
          }
        });
      } else {
        this.fetchRecordById(recordId);
      }
    }
  },

  /**
   * 初始化记录数据
   */
  initRecordData(record, appendSets) {
    console.log('初始化记录数据:', record, '追加模式:', appendSets);
    
    // 处理已有记录数据
    // 确保名称正确显示
    const exerciseName = record.exeName || record.name || this.data.exerciseName;
    
    const recordData = {
      recordId: record._id,
      exerciseId: record.exercise_id,
      exerciseName: exerciseName,
      date: record.date,
      weight: record.weight || 0,
      reps: record.reps || 0,
      sets: record.sets || [],
      note: record.note || '',
      isEdit: true  // 标记为编辑模式
    };
    
    // 如果是追加组数模式，需要保持已有组数
    if (appendSets) {
      recordData.isAppendMode = true;
    }
    
    this.setData(recordData);
    
    // 更新页面标题
    wx.setNavigationBarTitle({
      title: exerciseName
    });
  },

  /**
   * 获取动作详情
   */
  async fetchExerciseDetail(id) {
    wx.showLoading({
      title: '加载中...',
      mask: true
    });
    
    try {
      // 方法1: 使用云函数获取
      const res = await wx.cloud.callFunction({
        name: 'fetchExerciseDetail',
        data: {
          exerciseId: id
        }
      });
      
      if (res.result && res.result.success && res.result.data) {
        const exerciseData = res.result.data;
        
        // 获取GIF和图片的临时链接
        const gifFileID = exerciseData.gif_url ? `${this.data.fileIDPrefix}${exerciseData.gif_url}` : '';
        const imageFileID = exerciseData.image_name ? `${this.data.fileIDPrefix}${exerciseData.image_name}` : '';
        
        const [gifURL, imageURL] = await Promise.all([
          gifFileID ? this.getCloudFileURL(gifFileID) : '',
          imageFileID ? this.getCloudFileURL(imageFileID) : ''
        ]);
        
        this.setData({
          exerciseData,
          exerciseGif: gifURL,
          exerciseImage: imageURL
        });
        
        wx.hideLoading();
      } else {
        throw new Error('获取动作详情失败');
      }
    } catch (error) {
      console.error('获取动作详情失败:', error);
      
      // 如果云函数调用失败，则尝试直接查询数据库
      try {
        const db = wx.cloud.database();
        console.log('尝试直接从数据库获取动作，ID:', id);
        
        // 注意：这里使用id字段查询，而不是_id
        const result = await db.collection('exercise').where({
          id: Number(id)  // 确保ID为数字类型
        }).get();
        
        if (result.data && result.data.length > 0) {
          const exerciseData = result.data[0];
          console.log('直接从数据库获取成功:', exerciseData);
          
          // 获取GIF和图片的临时链接
          const gifFileID = exerciseData.gif_url ? `${this.data.fileIDPrefix}${exerciseData.gif_url}` : '';
          const imageFileID = exerciseData.image_name ? `${this.data.fileIDPrefix}${exerciseData.image_name}` : '';
          
          const [gifURL, imageURL] = await Promise.all([
            gifFileID ? this.getCloudFileURL(gifFileID) : '',
            imageFileID ? this.getCloudFileURL(imageFileID) : ''
          ]);
          
          this.setData({
            exerciseData,
            exerciseGif: gifURL,
            exerciseImage: imageURL
          });
          
          wx.hideLoading();
          return;
        } else {
          throw new Error('数据库中未找到该动作');
        }
      } catch (dbError) {
        console.error('直接从数据库获取失败:', dbError);
        wx.hideLoading();
        wx.showToast({
          title: '获取动作详情失败',
          icon: 'none'
        });
      }
    }
  },
  
  /**
   * 获取云存储文件的临时访问链接
   */
  async getCloudFileURL(fileID) {
    try {
      const { fileList } = await wx.cloud.getTempFileURL({
        fileList: [fileID]
      });
      return fileList[0].tempFileURL;
    } catch (error) {
      console.error('获取文件访问链接失败:', error);
      return '';
    }
  },
  
  /**
   * 设置媒体URL
   */
  async setupMediaUrls(exerciseData, prefix) {
    try {
      const gifFileID = exerciseData.gif_url ? `${prefix}${exerciseData.gif_url}` : '';
      const imageFileID = exerciseData.image_name ? `${prefix}${exerciseData.image_name}` : '';
      
      if (!gifFileID && !imageFileID) {
        console.log('没有媒体文件需要加载');
        return;
      }
      
      wx.showLoading({
        title: '加载媒体...',
        mask: true
      });
      
      const [gifURL, imageURL] = await Promise.all([
        gifFileID ? this.getCloudFileURL(gifFileID) : '',
        imageFileID ? this.getCloudFileURL(imageFileID) : ''
      ]);
      
      this.setData({
        exerciseGif: gifURL,
        exerciseImage: imageURL
      });
      
      wx.hideLoading();
    } catch (error) {
      console.error('设置媒体URL失败:', error);
      wx.hideLoading();
    }
  },
  
  /**
   * 添加默认组数
   */
  appendDefaultSets() {
    // 获取当前组数
    const currentSets = [...this.data.sets];
    
    // 记录原始组数数量
    this.setData({
      originalSetsCount: currentSets.length
    });
    
    // 追加两组默认组数
    const additionalSets = [
      { weight: 0, reps: 12 },
      { weight: 0, reps: 12 }
    ];
    
    // 更新组数
    this.setData({
      sets: [...currentSets, ...additionalSets]
    });
    
    // 显示提示
    wx.showToast({
      title: '已添加额外的组数',
      icon: 'none'
    });
  },
  
  // 添加组数按钮点击事件
  addSet() {
    const sets = this.data.sets;
    // 复制最后一组的数据作为新组的默认值
    const lastSet = sets.length > 0 ? sets[sets.length - 1] : { weight: 0, reps: 12 };
    const newSet = { weight: lastSet.weight, reps: lastSet.reps };
    
    sets.push(newSet);
    this.setData({ sets });
  },
  
  // 删除组数按钮点击事件
  deleteSet(e) {
    const index = e.currentTarget.dataset.index;
    const sets = this.data.sets;
    
    if (sets.length <= 1) {
      wx.showToast({
        title: '至少需要保留一组',
        icon: 'none'
      });
      return;
    }
    
    sets.splice(index, 1);
    this.setData({ sets });
  },
  
  /**
   * 重量输入事件
   */
  onWeightInput(e) {
    const { index } = e.currentTarget.dataset;
    const { value } = e.detail;
    
    // 更新对应组的重量
    let sets = this.data.sets;
    sets[index].weight = value === '' ? 0 : Number(value);
    
    this.setData({ sets });
  },
  
  /**
   * 次数输入事件
   */
  onRepsInput(e) {
    const { index } = e.currentTarget.dataset;
    const { value } = e.detail;
    
    // 更新对应组的次数
    let sets = this.data.sets;
    sets[index].reps = value === '' ? 0 : Number(value);
    
    this.setData({ sets });
  },
  
  /**
   * 滑块修改重量事件
   */
  onWeightChange(e) {
    const { index } = e.currentTarget.dataset;
    const { value } = e.detail;
    
    // 更新对应组的重量
    let sets = this.data.sets;
    sets[index].weight = Number(value);
    
    this.setData({ sets });
  },
  
  /**
   * 滑块修改次数事件
   */
  onRepsChange(e) {
    const { index } = e.currentTarget.dataset;
    const { value } = e.detail;
    
    // 更新对应组的次数
    let sets = this.data.sets;
    sets[index].reps = Number(value);
    
    this.setData({ sets });
  },
  
  /**
   * 保存训练记录
   */
  saveRecord() {
    const { exerciseId, exerciseName, recordId, isAppendMode, sets, note, returnToCustomizePlan } = this.data;
    
    // 如果没有任何组数，提示用户
    if (!sets || sets.length === 0) {
      wx.showToast({
        title: '请至少添加一组训练',
        icon: 'none'
      });
      return;
    }
    
    // 显示加载提示
    wx.showLoading({
      title: isAppendMode ? '正在追加组数...' : (recordId ? '正在更新...' : '正在保存...')
    });
    
    // 构建保存的数据
    const date = new Date();
    const formattedDate = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
    
    const data = {
      exercise_id: exerciseId,
      date: formattedDate,
      sets: sets,
      notes: note || ''
    };
    
    // 调用后端 API 保存记录
    wx.request({
      url: `${API_BASE_URL}/training-records/${recordId ? recordId + '/' : ''}`,
      method: recordId ? 'PUT' : 'POST',
      data: data,
      header: {
        'Authorization': `Bearer ${wx.getStorageSync('token')}`,
        'Content-Type': 'application/json'
      },
      success: res => {
        wx.hideLoading();
        console.log('保存训练记录结果:', res.data);
        
        if (res.statusCode === 200) {
          wx.showToast({
            title: isAppendMode ? '追加成功' : (recordId ? '更新成功' : '保存成功'),
            icon: 'success'
          });
          
          // 延迟处理导航
          setTimeout(() => {
            // 获取页面栈
            const pages = getCurrentPages();
            
            // 如果需要返回到customizePlan页面
            if (returnToCustomizePlan) {
              console.log('需要返回到训练计划页面');
              
              // 查找训练计划页面在栈中的位置
              const customizePlanIndex = pages.findIndex(page => page.route === 'pages/customizePlan/index');
              
              if (customizePlanIndex !== -1) {
                // 如果训练计划页面在栈中，尝试刷新并返回到该页面
                const customizePlanPage = pages[customizePlanIndex];
                if (customizePlanPage && typeof customizePlanPage.fetchTrainingRecords === 'function') {
                  customizePlanPage.fetchTrainingRecords();
                }
                
                console.log('返回到训练计划页面，跳过页面数:', pages.length - customizePlanIndex - 1);
                wx.navigateBack({
                  delta: pages.length - customizePlanIndex - 1,
                  success: () => {
                    // 使用 getCurrentPages 获取最新的页面栈
                    const currentPages = getCurrentPages();
                    const currentPage = currentPages[currentPages.length - 1];
                    if (currentPage && typeof currentPage.fetchTrainingRecords === 'function') {
                      currentPage.fetchTrainingRecords();
                    }
                  }
                });
              } else {
                // 如果训练计划页面不在栈中，重定向到该页面
                console.log('训练计划页面不在栈中，重定向');
                wx.redirectTo({
                  url: '/pages/customizePlan/index',
                  success: () => {
                    // 使用 getCurrentPages 获取最新的页面栈
                    const currentPages = getCurrentPages();
                    const currentPage = currentPages[currentPages.length - 1];
                    if (currentPage && typeof currentPage.fetchTrainingRecords === 'function') {
                      currentPage.fetchTrainingRecords();
                    }
                  }
                });
              }
              return;
            }
            
            // 默认行为：返回上一页
            const prevPage = pages[pages.length - 2];
            
            // 如果上一页是customizePlan，触发刷新
            if (prevPage && prevPage.route.includes('customizePlan')) {
              prevPage.fetchTrainingRecords();
            }
            
            wx.navigateBack();
          }, 1000);
        } else {
          wx.showToast({
            title: res.data?.detail || '操作失败',
            icon: 'none'
          });
        }
      },
      fail: err => {
        wx.hideLoading();
        console.error('保存训练记录失败:', err);
        wx.showToast({
          title: '保存失败，请重试',
          icon: 'none'
        });
      }
    });
  },

  /**
   * 根据记录ID获取训练记录
   */
  fetchRecordById(recordId) {
    wx.showLoading({
      title: '加载记录...',
    });
    
    // 调用后端 API 获取指定ID的训练记录
    wx.request({
      url: `${API_BASE_URL}/training-records/${recordId}/`,
      method: 'GET',
      header: {
        'Authorization': `Bearer ${wx.getStorageSync('token')}`
      },
      success: res => {
        wx.hideLoading();
        console.log('根据ID获取训练记录结果:', res.data);
        
        if (res.statusCode === 200 && res.data) {
          this.initRecordData(res.data, this.data.isAppendMode);
        } else {
          wx.showToast({
            title: '未找到记录',
            icon: 'none'
          });
        }
      },
      fail: err => {
        wx.hideLoading();
        console.error('获取训练记录失败:', err);
        wx.showToast({
          title: '获取记录失败',
          icon: 'none'
        });
      }
    });
  }
}); 