/* 社区页面样式 */
.community-container {
  padding: 24rpx;
  padding-bottom: 120rpx;
  background-color: #f8f8f8;
  min-height: 100vh;
}

.post-list {
  margin-bottom: 20rpx;
}

/* 加载状态 */
.loading-container {
  padding: 30rpx 0;
  text-align: center;
}

.loading-text {
  color: #999;
  font-size: 26rpx;
}

/* 空状态 */
.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
}

.empty-icon {
  font-size: 80rpx;
  color: #ddd;
  margin-bottom: 20rpx;
}

.empty-text {
  color: #999;
  font-size: 28rpx;
}

/* 加载完毕提示 */
.end-tip {
  text-align: center;
  padding: 30rpx 0;
  color: #999;
  font-size: 24rpx;
}

/* 发布按钮 */
.publish-btn {
  position: fixed;
  right: 40rpx;
  bottom: 80rpx;
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
  background-color: #07C160;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 16rpx rgba(7, 193, 96, 0.3);
  z-index: 999;
}

.publish-icon {
  color: #fff;
  font-size: 50rpx;
  line-height: 1;
} 