<view class="community-container">
  <!-- 帖子列表 -->
  <view class="post-list">
    <block wx:for="{{posts}}" wx:key="id">
      <post-card 
        post="{{item}}" 
        bindtapavatar="onTapAvatar"
        bindtappost="onTapPost"
        bindtapworkout="onTapWorkout"
        bindlike="onLikePost"
        bindloadcomments="onLoadComments"
        bindshare="onSharePost"
        bindedit="onEditPost"
        binddelete="onDeletePost"
        bindreport="onReportPost"
      >
        <!-- 评论区插槽 -->
        <comment-section 
          slot="comments" 
          wx:if="{{showComments && currentPostId === item.id}}"
          comments="{{comments}}"
          postId="{{item.id}}"
          loading="{{commentLoading}}"
          loadedAll="{{commentLoadedAll}}"
          bindsubmit="onSubmitComment"
          bindlike="onLikeComment"
          bindloadmore="onLoadMoreComments"
          bindtapavatar="onTapAvatar"
        />
      </post-card>
    </block>
  </view>

  <!-- 加载状态 -->
  <view class="loading-container" wx:if="{{loading}}">
    <view class="loading-text">加载中...</view>
  </view>

  <!-- 无数据状态 -->
  <view class="empty-container" wx:if="{{!loading && posts.length === 0}}">
    <view class="empty-icon">📱</view>
    <view class="empty-text">暂无动态，快去发布第一条吧！</view>
  </view>

  <!-- 加载完毕提示 -->
  <view class="end-tip" wx:if="{{loadedAll && posts.length > 0}}">
    <text>- 已经到底了 -</text>
  </view>

  <!-- 发布按钮 -->
  <view class="publish-btn" bindtap="navigateToPublish">
    <text class="publish-icon">+</text>
  </view>
</view> 