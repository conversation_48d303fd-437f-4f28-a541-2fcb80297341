<!--pages/customizePlan/index.wxml-->
<view class="page-container">
  <!-- 顶部滚动区域 -->
  <scroll-view class="scroll-container" scroll-y="true" enhanced="true" show-scrollbar="true" bounces="true">
    <view class="container">
      <!-- 标题和日期信息 -->
      <view class="header">
        <view class="header-row">
          <view class="title">自定义训练计划</view>
          <view class="edit-button" bindtap="toggleEditMode">
            {{isEditMode ? '完成' : '删除训练'}}
          </view>
        </view>
        <view class="date">{{currentDate}}</view>
      </view>
      
      <!-- 全选区域 - 仅在编辑模式显示 -->
      <view class="select-all-row" wx:if="{{isEditMode && records.length > 0}}">
        <view class="select-all-checkbox {{isAllSelected ? 'checked' : ''}}" bindtap="toggleSelectAll">
          <image wx:if="{{isAllSelected}}" class="checkbox-icon" src="/images/icons/choose.png" mode="aspectFit"></image>
        </view>
        <view class="select-all-text" bindtap="toggleSelectAll">全选</view>
        <view class="delete-selected-button" bindtap="deleteSelected" wx:if="{{hasSelected}}">删除选中({{selectedCount}})</view>
      </view>
      
      <!-- 训练列表 -->
      <view class="exercise-list">
        <!-- 如果没有数据，显示空状态 -->
        <view class="empty-state" wx:if="{{records.length === 0}}">
          <image class="empty-icon" src="/images/icons/body.png" mode="aspectFit"></image>
          <view class="empty-text">还没有添加训练动作</view>
        </view>
        
        <!-- 列出所有训练动作 -->
        <block wx:for="{{records}}" wx:key="id">
          <view class="exercise-item {{isEditMode ? 'edit-mode' : ''}}">
            <!-- 选择框 - 仅在编辑模式显示 -->
            <view class="exercise-checkbox" wx:if="{{isEditMode}}" catchtap="toggleSelect" data-index="{{index}}">
              <view class="checkbox {{item.selected ? 'checked' : ''}}">
                <image wx:if="{{item.selected}}" class="checkbox-icon" src="/images/icons/choose.png" mode="aspectFit"></image>
              </view>
            </view>
            
            <!-- 训练标题栏 -->
            <view class="exercise-header {{isEditMode ? 'with-checkbox' : ''}}" bindtap="toggleDetail" data-index="{{index}}">
              <view class="exercise-info">
                <view class="exercise-name">{{index+1}}. {{item.exeName}}</view>
                <view class="exercise-stats">{{item.muscleGroup}} | {{item.totalSets}}组</view>
              </view>
              <view class="exercise-icon">
                <image class="arrow-icon" src="/images/icons/{{item.expanded ? 'return.png' : 'right arrow.png'}}" mode="aspectFit"></image>
              </view>
            </view>
            
            <!-- 训练详情区域 -->
            <view class="exercise-detail" wx:if="{{item.expanded}}">
              <!-- 动作图片/GIF展示 -->
              <view class="exercise-media" wx:if="{{item.gifUrl || item.imageUrl}}">
                <image wx:if="{{item.gifUrl}}" class="exercise-gif" src="{{item.gifUrl}}" mode="aspectFit" lazy-load="true"></image>
                <image wx:elif="{{item.imageUrl}}" class="exercise-image" src="{{item.imageUrl}}" mode="aspectFit" lazy-load="true"></image>
              </view>
              
              <!-- 组数详情 -->
              <view class="sets-detail">
                <view class="sets-header">
                  <view class="column-index">组数</view>
                  <view class="column-weight">重量 (kg)</view>
                  <view class="column-reps">次数</view>
                </view>
                <block wx:for="{{item.sets}}" wx:for-item="set" wx:for-index="setIndex" wx:key="setIndex">
                  <view class="set-row">
                    <view class="column-index">{{setIndex + 1}}</view>
                    <view class="column-weight">{{set.weight || '-'}}</view>
                    <view class="column-reps">{{set.reps || '-'}}</view>
                  </view>
                </block>
              </view>
              
              <!-- 操作按钮 -->
              <view class="exercise-actions">
                <view class="action-button edit" bindtap="editExercise" data-index="{{index}}">
                  <image class="action-icon" src="/images/icons/edit.png" mode="aspectFit"></image>
                  <text>编辑</text>
                </view>
                <view class="action-button delete" bindtap="deleteExercise" data-index="{{index}}">
                  <image class="action-icon" src="/images/icons/delete.png" mode="aspectFit"></image>
                  <text>删除</text>
                </view>
              </view>
            </view>
          </view>
        </block>
      </view>
      
      <!-- 底部安全区域，确保内容不被底部按钮遮挡 -->
      <view class="safe-area-bottom"></view>
    </view>
  </scroll-view>
  
  <!-- 分隔线 -->
  <view class="bottom-divider"></view>
  
  <!-- 固定在底部的操作按钮 -->
  <view class="fixed-bottom safe-bottom">
    <view class="button-group">
      <button class="add-exercise-button" bindtap="addExercise">添加训练动作</button>
      <button class="load-template-button" bindtap="openTemplates">加载模板</button>
      <button class="save-template-button" bindtap="saveAsTemplate" disabled="{{records.length === 0}}">保存为模板</button>
    </view>
    <button class="save-plan-button" bindtap="savePlan" disabled="{{records.length === 0}}">保存训练计划</button>
  </view>
</view>