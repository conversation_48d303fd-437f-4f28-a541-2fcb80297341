/* pages/customizePlan/index.wxss */
.page-container {
  height: 100vh;
  width: 100%;
  position: relative;
  display: flex;
  flex-direction: column;
  overflow: hidden; /* 防止页面整体滚动 */
  background-color: #f8f8f8;
}

.scroll-container {
  flex: 1;
  width: 100%;
  height: calc(100vh - 220rpx); /* 增加底部间距，为底部按钮留出空间 */
  padding-bottom: 0;
  box-sizing: border-box;
  overflow: hidden;
}

.container {
  padding: 30rpx;
  padding-bottom: 30rpx; /* 减小底部padding，让内容区域有明确的结束位置 */
  background-color: #f8f8f8;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  min-height: calc(100% - 150rpx); /* 确保内容容器至少填充滚动区域 */
}

/* 头部样式 */
.header {
  margin-bottom: 30rpx;
}

.title {
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
}

.date {
  font-size: 28rpx;
  color: #666;
}

/* 训练列表样式 */
.exercise-list {
  width: 100%;
  margin-bottom: 30rpx;
}

.exercise-item {
  background-color: #fff;
  border-radius: 15rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.exercise-header {
  padding: 20rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.exercise-info {
  flex: 1;
}

.exercise-name {
  font-size: 30rpx;
  font-weight: bold;
  margin-bottom: 6rpx;
}

.exercise-stats {
  font-size: 24rpx;
  color: #666;
}

.exercise-icon {
  margin-left: 20rpx;
}

.arrow-icon {
  width: 32rpx;
  height: 32rpx;
}

/* 动作详情样式 */
.exercise-detail {
  border-top: 1rpx solid #eee;
  padding: 20rpx;
}

.exercise-media {
  width: 100%;
  height: 300rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #f5f5f5;
  border-radius: 10rpx;
  margin-bottom: 20rpx;
  overflow: hidden;
}

.exercise-gif, .exercise-image {
  max-width: 100%;
  max-height: 300rpx;
  object-fit: contain;
}

/* 组数详情样式 */
.sets-detail {
  margin-bottom: 20rpx;
}

.sets-header, .set-row {
  display: flex;
  align-items: center;
  padding: 15rpx 0;
}

.sets-header {
  border-bottom: 1rpx solid #eee;
  font-weight: bold;
  font-size: 24rpx;
}

.set-row {
  border-bottom: 1rpx solid #f5f5f5;
}

.column-index {
  width: 20%;
  text-align: center;
}

.column-weight, .column-reps {
  width: 40%;
  text-align: center;
}

/* 操作按钮样式 */
.exercise-actions {
  display: flex;
  justify-content: flex-end;
  padding-top: 15rpx;
}

.action-button {
  display: flex;
  align-items: center;
  padding: 10rpx 20rpx;
  border-radius: 30rpx;
  margin-left: 20rpx;
  font-size: 24rpx;
}

.action-icon {
  width: 28rpx;
  height: 28rpx;
  margin-right: 8rpx;
}

.edit {
  background-color: #e8f5e9;
  color: #4caf50;
}

.delete {
  background-color: #ffebee;
  color: #f44336;
}

/* 空状态样式 */
.empty-state {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 60rpx 0;
}

.empty-icon {
  width: 120rpx;
  height: 120rpx;
  opacity: 0.5;
  margin-bottom: 20rpx;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
}

/* 添加按钮样式 */
.add-button {
  background-color: #fff;
  border-radius: 15rpx;
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.1);
  padding: 25rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 30rpx;
}

.add-icon {
  width: 36rpx;
  height: 36rpx;
  margin-right: 10rpx;
}

/* 底部留白 - 不再需要，使用容器padding-bottom代替 */
.bottom-space {
  display: none;
}

/* 固定底部按钮 */
.fixed-bottom {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 15rpx 20rpx;
  background-color: #f8f8f8;
  box-shadow: 0 -4rpx 10rpx rgba(0, 0, 0, 0.05);
  z-index: 100;
  box-sizing: border-box;
}

/* 适配底部安全区域的手机 */
.safe-bottom {
  padding-bottom: calc(15rpx + constant(safe-area-inset-bottom));
  padding-bottom: calc(15rpx + env(safe-area-inset-bottom));
}

/* 底部按钮容器 */
.bottom-buttons {
  display: flex;
  width: 100%;
  padding: 20rpx 30rpx;
  box-sizing: border-box;
  align-items: center;
  background-color: #fff;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
}

/* 按钮组样式 */
.button-group {
  display: flex;
  justify-content: space-between;
  margin-bottom: 15rpx;
}

/* 添加训练按钮样式 */
.add-exercise-button {
  flex: 1;
  height: 80rpx;
  line-height: 80rpx;
  background-color: #fff;
  color: #07c160;
  font-size: 28rpx;
  border-radius: 40rpx;
  border: 2rpx solid #07c160;
  margin-right: 15rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
}

/* 保存训练模板按钮样式 */
.save-template-button {
  flex: 1;
  height: 80rpx;
  line-height: 80rpx;
  background-color: #fff;
  color: #ff9800;
  font-size: 28rpx;
  border-radius: 40rpx;
  border: 2rpx solid #ff9800;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
}

.save-template-button[disabled] {
  color: #ffd699;
  border-color: #ffd699;
}

/* 保存计划按钮样式 */
.save-plan-button {
  width: 100%;
  height: 80rpx;
  line-height: 80rpx;
  background-color: #07c160;
  color: #fff;
  font-size: 28rpx;
  border-radius: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
}

.save-plan-button[disabled] {
  background-color: #b6e8c8;
  color: #fff;
}

/* 底部安全区域 */
.safe-area-bottom {
  width: 100%;
  height: 220rpx; /* 增大底部安全区域高度 */
  background-color: transparent;
}

/* 底部分隔线 */
.bottom-divider {
  width: 100%;
  height: 2rpx;
  background-color: #e0e0e0;
  position: fixed;
  bottom: 220rpx; /* 调整分隔线位置 */
  left: 0;
  z-index: 99;
}

/* 标题栏样式调整 */
.header-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10rpx;
}

/* 编辑按钮样式 */
.edit-button {
  font-size: 28rpx;
  color: #07c160;
  padding: 8rpx 20rpx;
  border-radius: 30rpx;
  background-color: rgba(7, 193, 96, 0.1);
}

/* 全选区域样式 */
.select-all-row {
  display: flex;
  align-items: center;
  padding: 20rpx 0;
  margin-bottom: 20rpx;
  border-bottom: 1rpx solid #eee;
}

.select-all-checkbox {
  width: 40rpx;
  height: 40rpx;
  border-radius: 50%;
  border: 2rpx solid #ddd;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-right: 15rpx;
}

.select-all-checkbox.checked {
  background-color: #07c160;
  border-color: #07c160;
}

.select-all-text {
  font-size: 28rpx;
  color: #333;
}

.delete-selected-button {
  margin-left: auto;
  font-size: 28rpx;
  color: #f44336;
  padding: 8rpx 20rpx;
  border-radius: 30rpx;
  background-color: rgba(244, 67, 54, 0.1);
}

/* 复选框样式 */
.exercise-checkbox {
  width: 40rpx;
  height: 40rpx;
  margin-right: 15rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.checkbox {
  width: 40rpx;
  height: 40rpx;
  border-radius: 50%;
  border: 2rpx solid #ddd;
  display: flex;
  justify-content: center;
  align-items: center;
}

.checkbox.checked {
  background-color: #07c160;
  border-color: #07c160;
}

.checkbox-icon {
  width: 24rpx;
  height: 24rpx;
}

/* 编辑模式的行样式 */
.exercise-item.edit-mode {
  display: flex;
  align-items: flex-start;
  padding-left: 15rpx;
}

.exercise-header.with-checkbox {
  flex: 1;
}