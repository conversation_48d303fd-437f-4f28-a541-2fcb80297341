<view class="chat-container">
  <!-- 聊天消息列表 -->
  <scroll-view class="chat-list"
               scroll-y
               scroll-into-view="{{!isRefreshing ? 'msg-' + (messages.length-1) : ''}}"
               enhanced="true"
               show-scrollbar="false"
               refresher-enabled="{{refresherEnabled}}"
               refresher-triggered="{{refresherTriggered}}"
               refresher-background="#f2f2f2"
               bindrefresherrefresh="onRefresherRefresh">
    <!-- 空消息提示 -->
    <view wx:if="{{!messages || messages.length === 0}}" class="empty-chat-container">
      <image class="empty-chat-bot-avatar" src="{{botAvatar}}" />
      <view class="empty-chat-message">
        <text wx:if="{{isFirstLoad || loadingHistory}}">正在加载聊天记录...</text>
        <text wx:else>您好，我是您的健康助手。我可以为您提供专业的健康建议，包括食物分析、饮食建议和运动计划。请随时向我提问！</text>
      </view>
    </view>

    <!-- 加载更多提示 -->
    <view wx:if="{{isLoadingMore && messages.length > 0}}" class="loading-more-indicator">
      <view class="loading-dots">
        <view class="dot"></view>
        <view class="dot"></view>
        <view class="dot"></view>
      </view>
      <text>加载更多消息...</text>
    </view>

    <block wx:for="{{messages}}" wx:for-index="index" wx:for-item="item" wx:key="index">
      <!-- 用户消息 -->
      <view wx:if="{{item.role === 'user'}}" class="message-wrapper user" id="msg-{{index}}">
        <image class="avatar" src="{{userAvatar}}" />
        <view class="message-content">
          <text wx:if="{{item.type === 'text'}}" class="message-text">{{item.content}}</text>
          <image wx:if="{{item.type === 'image'}}"
                 class="message-image"
                 src="{{item.imageUrl || item.content}}"
                 mode="widthFix"
                 bindtap="previewImage"
                 data-src="{{item.imageUrl || item.content}}" />
        </view>
      </view>

      <!-- 机器人消息 -->
      <view wx:else class="message-wrapper bot" id="msg-{{index}}">
        <image class="avatar" src="{{botAvatar}}" />
        <view class="message-content">
          <!-- 只有当不是训练参数收集状态时才显示消息内容 -->
          <view wx:if="{{item.type === 'text' && !item.meta_info.collecting_training_params}}" class="message-text">
            <wemark md="{{item.markdownContent}}"
                   link
                   highlight
                   type="wemark"></wemark>
          </view>

          <!-- 添加训练计划展示 -->
          <template wx:if="{{item.showTrainingPlan}}"
                    is="training-plan"
                    data="{{item: item.trainingPlan}}" />

          <!-- 添加workout展示 -->
          <template wx:if="{{item.showWorkout}}"
                    is="workout"
                    data="{{item: item.workout}}" />

          <!-- 添加训练参数收集UI -->
          <template wx:if="{{item.meta_info.collecting_training_params}}"
                    is="training-params-selection"
                    data="{{item: item}}" />

          <!-- 添加食物分析结果显示 -->
          <view wx:if="{{item.type === 'food_analysis'}}" class="food-analysis {{item.isCompleted ? 'completed' : ''}}" bindtap="navigateToFoodPreview" data-message-id="{{item.id}}" data-is-completed="{{item.isCompleted}}">
            <!-- 添加已完成徽章 -->
            <view wx:if="{{item.isCompleted}}" class="completed-badge">已完成</view>
            <view class="food-summary">
              <view class="food-info">
                <!-- 食物标题和图片 -->
                <view class="food-header">
                  <view class="food-text">
                    <view class="food-title">
                      <text>{{item.mealName || '食物分析结果'}}</text>
                    </view>
                  </view>
                  <image class="thumb-image" src="{{item.thumbImageUrl}}" mode="aspectFill"/>
                </view>

                <!-- 营养成分展示 -->
                <view class="nutrition-details">
                  <!-- 热量 -->
                  <view class="nutrition-item">
                    <image class="nutrition-icon" src="/images/icons/flame.png" />
                    <view class="nutrition-value">
                      <text class="value">{{item.nutritionSummary.calories || item.totalCalories || 0}}</text>
                      <text class="unit">kcal</text>
                    </view>
                  </view>

                  <!-- 蛋白质 -->
                  <view class="nutrition-item">
                    <image class="nutrition-icon" src="/images/icons/meat.png" />
                    <view class="nutrition-value">
                      <text class="value">{{item.nutritionSummary.protein || item.proteinPercentage || 0}}</text>
                      <text class="unit">%</text>
                    </view>
                  </view>

                  <!-- 碳水 -->
                  <view class="nutrition-item">
                    <image class="nutrition-icon" src="/images/icons/leaf.png" />
                    <view class="nutrition-value">
                      <text class="value">{{item.nutritionSummary.carbs || item.carbPercentage || 0}}</text>
                      <text class="unit">%</text>
                    </view>
                  </view>

                  <!-- 脂肪 -->
                  <view class="nutrition-item">
                    <image class="nutrition-icon" src="/images/icons/oil.png" />
                    <view class="nutrition-value">
                      <text class="value">{{item.nutritionSummary.fat || item.fatPercentage || 0}}</text>
                      <text class="unit">%</text>
                    </view>
                  </view>
                </view>

                <view class="tap-hint">
                  <text class="hint-text">{{item.isCompleted ? '已保存到饮食记录' : '点击编辑并保存到饮食记录'}}</text>
                  <image wx:if="{{!item.isCompleted}}" class="arrow-icon" src="/images/icons/arrow-right.png" />
                  <image wx:else class="check-icon" src="/images/icons/check.png" />
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 添加分析进度消息类型 -->
      <view wx:if="{{item.type === 'analyzing'}}" class="message-wrapper bot" id="msg-{{index}}">
        <image class="avatar" src="{{botAvatar}}" />
        <view class="message-content">
          <view class="analyzing-status">
            <view class="analyzing-text">正在分析图片</view>
            <view class="analyzing-dots">
              <view class="dot"></view>
              <view class="dot"></view>
              <view class="dot"></view>
            </view>
            <view class="retry-count" wx:if="{{item.retryCount > 1}}">重试次数: {{item.retryCount}}</view>
          </view>
        </view>
      </view>
    </block>

    <!-- 打字指示器 -->
    <view class="typing-indicator {{isLoading ? 'show' : ''}}" id="typing">
      <view class="typing-wrapper">
        <view class="typing-content">
          <view class="dot"></view>
          <view class="dot"></view>
          <view class="dot"></view>
        </view>
      </view>
    </view>

    <!-- 底部占位，防止输入框遮挡内容 -->
    <view class="bottom-space"></view>
  </scroll-view>

  <!-- 功能标签栏 -->
  <view class="feature-tags">
    <view class="tag-item" bindtap="handleUploadImage">
      <text>食物分析</text>
    </view>
    <view class="tag-item" bindtap="handleDietAdvice">
      <text>饮食建议</text>
    </view>
    <view class="tag-item" bindtap="handleExercisePlan">
      <text>运动计划</text>
    </view>
  </view>

  <!-- 底部输入区域 -->
  <view class="input-area">
    <view class="input-wrapper">
      <view class="voice-btn" bindtap="toggleInputMode">
        <image src="/images/icons/{{isVoiceMode ? 'keyboard' : 'voice'}}.png" />
      </view>

      <!-- 文本输入模式 -->
      <block wx:if="{{!isVoiceMode}}">
        <input class="message-input"
               value="{{inputValue}}"
               bindinput="onInputChange"
               bindconfirm="sendMessage"
               placeholder="输入您的健康问题..."
               placeholder-class="input-placeholder"
               confirm-type="send"
               disabled="{{isLoading}}" />
      </block>

      <!-- 语音输入模式 -->
      <block wx:else>
        <view class="voice-input-area"
              bindtouchstart="startRecording"
              bindtouchend="endRecording"
              bindtouchmove="cancelRecording">
          <text>{{isRecording ? '松开发送' : '按住说话'}}</text>
        </view>
      </block>

      <view class="add-btn">
        <image src="/images/icons/add.png" />
      </view>
    </view>
  </view>
</view>

<!-- 训练计划模板 -->
<template name="training-plan">
  <view class="training-plan-card">
    <view class="plan-header">
      <!-- 当workouts长度为1时，显示workout的name作为标题 -->
      <block wx:if="{{item.workouts && item.workouts.length === 1}}">
        <text class="plan-title">{{item.workouts[0].name}}</text>
        <text class="plan-duration">预计时长：{{item.estimated_duration}}分钟</text>
        <text wx:if="{{item.description}}" class="plan-description">{{item.description}}</text>
      </block>
      <!-- 当workouts长度大于1时，显示计划的name和周期长度 -->
      <block wx:else>
        <text class="plan-title">{{item.name}}</text>
        <text class="plan-duration">周期：{{item.duration_weeks || '未设置'}}周</text>
      </block>
    </view>

    <!-- 动作图片展示区域 - 所有图片在一行 -->
    <view class="exercises-images">
      <block wx:for="{{item.exercises}}" wx:for-item="exercise" wx:for-index="exerciseIndex" wx:key="exerciseIndex" wx:if="{{exerciseIndex < 5}}">
        <image class="exercise-image-inline" src="{{exercise.image_url}}" mode="aspectFill"/>
      </block>
      <!-- 如果动作数量超过5个，显示更多提示 -->
      <view wx:if="{{item.exercises.length > 5}}" class="more-exercises">
        +{{item.exercises.length - 5}}
      </view>
    </view>

    <view class="view-details" bindtap="navigateToTrainingPlan" data-plan-id="{{item.id}}">
      查看详情
    </view>
  </view>
</template>

<!-- 训练参数选择模板 -->
<template name="training-params-selection">
  <view class="training-params-selection">
    <!-- 问题展示 -->
    <view class="params-question">
      <text wx:if="{{item.meta_info.asking_param === 'scenario'}}">您在什么环境下进行训练？</text>
      <text wx:elif="{{item.meta_info.asking_param === 'plan_type'}}">您希望什么类型的训练计划？</text>
      <text wx:elif="{{item.meta_info.asking_param === 'body_part'}}">您想重点训练哪个部位？</text>
    </view>

    <!-- 选项展示 -->
    <view class="params-options">
      <!-- 训练环境选项 -->
      <block wx:if="{{item.meta_info.asking_param === 'scenario'}}">
        <view class="option-item {{item.selectedParam === 'gym' ? 'selected' : ''}}"
              bindtap="handleTrainingParamSelect"
              data-message-id="{{item.id}}"
              data-param="scenario"
              data-value="gym">
          <text>健身房</text>
          <view wx:if="{{item.selectedParam === 'gym'}}" class="selected-icon">✓</view>
        </view>
        <view class="option-item {{item.selectedParam === 'home' ? 'selected' : ''}}"
              bindtap="handleTrainingParamSelect"
              data-message-id="{{item.id}}"
              data-param="scenario"
              data-value="home">
          <text>居家</text>
          <view wx:if="{{item.selectedParam === 'home'}}" class="selected-icon">✓</view>
        </view>
      </block>

      <!-- 计划类型选项 -->
      <block wx:elif="{{item.meta_info.asking_param === 'plan_type'}}">
        <view class="option-item {{item.selectedParam === 'daily' ? 'selected' : ''}}"
              bindtap="handleTrainingParamSelect"
              data-message-id="{{item.id}}"
              data-param="plan_type"
              data-value="daily">
          <text>单日</text>
          <view wx:if="{{item.selectedParam === 'daily'}}" class="selected-icon">✓</view>
        </view>
        <view class="option-item {{item.selectedParam === 'periodic' ? 'selected' : ''}}"
              bindtap="handleTrainingParamSelect"
              data-message-id="{{item.id}}"
              data-param="plan_type"
              data-value="periodic">
          <text>周期</text>
          <view wx:if="{{item.selectedParam === 'periodic'}}" class="selected-icon">✓</view>
        </view>
      </block>

      <!-- 身体部位选项 -->
      <block wx:elif="{{item.meta_info.asking_param === 'body_part'}}">
        <view class="option-item {{item.selectedParam === 'chest' ? 'selected' : ''}}"
              bindtap="handleTrainingParamSelect"
              data-message-id="{{item.id}}"
              data-param="body_part"
              data-value="chest">
          <text>胸部</text>
          <view wx:if="{{item.selectedParam === 'chest'}}" class="selected-icon">✓</view>
        </view>
        <view class="option-item {{item.selectedParam === 'back' ? 'selected' : ''}}"
              bindtap="handleTrainingParamSelect"
              data-message-id="{{item.id}}"
              data-param="body_part"
              data-value="back">
          <text>背部</text>
          <view wx:if="{{item.selectedParam === 'back'}}" class="selected-icon">✓</view>
        </view>
        <view class="option-item {{item.selectedParam === 'legs' ? 'selected' : ''}}"
              bindtap="handleTrainingParamSelect"
              data-message-id="{{item.id}}"
              data-param="body_part"
              data-value="legs">
          <text>腿部</text>
          <view wx:if="{{item.selectedParam === 'legs'}}" class="selected-icon">✓</view>
        </view>
        <view class="option-item {{item.selectedParam === 'shoulders' ? 'selected' : ''}}"
              bindtap="handleTrainingParamSelect"
              data-message-id="{{item.id}}"
              data-param="body_part"
              data-value="shoulders">
          <text>肩部</text>
          <view wx:if="{{item.selectedParam === 'shoulders'}}" class="selected-icon">✓</view>
        </view>
        <view class="option-item {{item.selectedParam === 'glutes' ? 'selected' : ''}}"
              bindtap="handleTrainingParamSelect"
              data-message-id="{{item.id}}"
              data-param="body_part"
              data-value="glutes">
          <text>臀部</text>
          <view wx:if="{{item.selectedParam === 'glutes'}}" class="selected-icon">✓</view>
        </view>
        <view class="option-item {{item.selectedParam === 'arms' ? 'selected' : ''}}"
              bindtap="handleTrainingParamSelect"
              data-message-id="{{item.id}}"
              data-param="body_part"
              data-value="arms">
          <text>手臂</text>
          <view wx:if="{{item.selectedParam === 'arms'}}" class="selected-icon">✓</view>
        </view>
        <view class="option-item {{item.selectedParam === 'abs' ? 'selected' : ''}}"
              bindtap="handleTrainingParamSelect"
              data-message-id="{{item.id}}"
              data-param="body_part"
              data-value="abs">
          <text>腹部</text>
          <view wx:if="{{item.selectedParam === 'abs'}}" class="selected-icon">✓</view>
        </view>
      </block>
    </view>

    <!-- 确认按钮 -->
    <view class="confirm-button-container">
      <view wx:if="{{!item.paramConfirmed}}"
            class="confirm-button {{!item.selectedParam ? 'disabled' : ''}}"
            bindtap="{{item.selectedParam ? 'handleTrainingParamConfirm' : ''}}"
            data-message-id="{{item.id}}">
        确认
      </view>
      <view wx:if="{{item.paramConfirmed}}" class="confirm-button confirmed">
        已确认
      </view>
    </view>
  </view>
</template>

<!-- Workout模板 -->
<template name="workout">
  <view class="training-plan-card">
    <view class="plan-header">
      <text class="plan-title">{{item.name}}</text>
      <text class="plan-duration">预计时长：{{item.estimated_duration}}分钟</text>
      <text wx:if="{{item.description}}" class="plan-description">{{item.description}}</text>
    </view>

    <!-- 动作图片展示区域 - 所有图片在一行 -->
    <view class="exercises-images">
      <block wx:for="{{item.exercises}}" wx:for-item="exercise" wx:for-index="exerciseIndex" wx:key="exerciseIndex" wx:if="{{exerciseIndex < 5}}">
        <image class="exercise-image-inline" src="{{exercise.image_url}}" mode="aspectFill"/>
      </block>
      <!-- 如果动作数量超过5个，显示更多提示 -->
      <view wx:if="{{item.exercises.length > 5}}" class="more-exercises">
        +{{item.exercises.length - 5}}
      </view>
    </view>

    <view class="view-details" bindtap="navigateToWorkoutDetail" data-workout-id="{{item.id}}">
      查看详情
    </view>
  </view>
</template>