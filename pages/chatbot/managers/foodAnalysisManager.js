/**
 * 食物分析管理器
 * 处理食物图片上传和分析
 */
const BaseManager = require('./baseManager');
const foodApi = require('../../../api/food');
const requestApi = require('../../../api/request');

class FoodAnalysisManager extends BaseManager {
  constructor(page) {
    super(page);
    this.lastFoodAnalysis = null;
  }

  /**
   * 处理图片上传
   */
  async handleUploadImage() {
    try {
      // Start loading state
      this.setData({ isLoading: true });
      this.showLoading('图片处理中...', true);

      // Ensure we have a conversation ID
      if (!this.getData('currentConversationId')) {
        await this.page.sessionManager.initChatSession();
      }

      // Choose image
      wx.chooseMedia({
        count: 1,
        mediaType: ['image'],
        sourceType: ['album', 'camera'],
        sizeType: ['compressed'],
        success: async (res) => {
          try {
            const tempFilePath = res.tempFiles[0].tempFilePath;

            // 添加用户图片消息
            const imageResult = this.page.addMessage(
              tempFilePath, // 图片路径作为content
              'user',
              'image',
              { type: 'image', imageUrl: tempFilePath }
            );

            // 获取临时消息对象，供后续更新使用
            const userImageMessage = imageResult.message;

            // 由于上传的是本地图片路径，等待保存到数据库后再继续处理
            let userMessageDbId = null;
            try {
              const savedMessage = await imageResult.promise;
              userMessageDbId = savedMessage.id;
              userImageMessage.id = userMessageDbId; // 更新消息ID为数据库ID
              userImageMessage.dbMessageId = userMessageDbId;
              this.log('用户图片消息已保存到数据库, ID:', userMessageDbId);
            } catch (error) {
              this.error('保存用户图片消息失败:', error);
              this.hideLoading();
              this.showToast('上传图片失败');
              this.setData({ isLoading: false });
              return;
            }

            // Get meal type based on current time
            const mealType = this.getMealTypeByTime();

            // Get current date in YYYY-MM-DD format
            const today = new Date();
            const mealDate = today.toISOString().split('T')[0];

            // 如果结果包含食物分析数据，处理并显示结果
            // 发送到后端进行分析
            this.showLoading('分析食物中...', true);
            try {
              // 调用实际的食物识别API
              const analysisResult = await foodApi.analyzeFoodImage(
                null, // No need for imageData since we're providing imageUrl
                mealType,
                mealDate,
                tempFilePath // Use the compressed image path
              );

              // 使用API返回的实际数据
              const result = analysisResult;

              this.log('食物分析结果:', result);

              // 成功分析图片后，更新用户图片消息的状态
              if (result.image_url) {
                // 构建服务器图像URL
                const serverImageUrl = requestApi.BASE_URL + '/meal-images' + (result.image_url.startsWith('/') ? '' : '/') + result.image_url;
                this.log('服务器图像URL:', serverImageUrl);

                // 确保userImageMessage上有dbMessageId
                if (userMessageDbId && !userImageMessage.dbMessageId) {
                  userImageMessage.dbMessageId = userMessageDbId;
                  this.log('更新userImageMessage.dbMessageId:', userMessageDbId);
                }

                // 更新用户图像消息
                this.page.updateMessageById(userImageMessage.id, {
                  status: 'success',
                  imageUrl: serverImageUrl,
                  content: result.image_url || tempFilePath, // 更新content为服务器路径
                  dbMessageId: userMessageDbId // 确保传递dbMessageId
                }, true); // 同步更新数据库
              }

              // 如果结果包含食物分析数据，处理并显示结果
              if (result.food_items && result.food_items.length > 0) {
                // 计算总热量和营养素
                const nutritionTotals = {
                  calories: result.nutrition_totals.total_calory || 0,
                  protein: result.nutrition_totals.total_calory > 0 ?
                    (result.nutrition_totals.total_protein * 4 / result.nutrition_totals.total_calory * 100).toFixed(1) : 0,
                  fat: result.nutrition_totals.total_calory > 0 ?
                    (result.nutrition_totals.total_fat * 9 / result.nutrition_totals.total_calory * 100).toFixed(1) : 0,
                  carbs: result.nutrition_totals.total_calory > 0 ?
                    (result.nutrition_totals.total_carbohydrate * 4 / result.nutrition_totals.total_calory * 100).toFixed(1) : 0
                };

                // 准备食物分析消息内容
                const analysisContent = JSON.stringify({
                  mealName: result.meal_name,
                  mealType: result.meal_type,
                  isCompleted: false,
                  recognitionId: result.recognition_id,
                  nutritionTotals,
                  thumbImageUrl: result.thumb_image_url
                });

                // 添加机器人食物分析消息
                const analysisResult = this.page.addMessage(
                  analysisContent,
                  'assistant',
                  'food_analysis',
                  {
                    type: 'food_analysis',
                    confirmed: false
                  }
                );

                // 存储最近的食物分析消息，用于详情页
                this.lastFoodAnalysis = {
                  ...analysisResult.message,
                  fullAnalysis: result
                };
                this.setData({ lastFoodAnalysis: this.lastFoodAnalysis });

                // 等待分析消息保存到数据库
                analysisResult.promise.then(savedMessage => {
                  this.log('食物分析结果已保存到数据库, ID:', savedMessage.id);
                }).catch(error => {
                  this.error('保存食物分析结果失败:', error);
                });

                // 添加分析完成提示消息
                setTimeout(() => {
                  this.page.messageManager.addBotMessage('分析完成，点击查看详细。', 'text');
                }, 800);
              } else {
                // 处理无法识别食物的情况
                this.page.messageManager.addBotMessage('抱歉，无法识别图片中的食物，请重新上传清晰的食物图像。', 'text');
              }
            } catch (error) {
              this.error('食物分析失败:', error);

              // 更新消息状态为失败
              this.page.updateMessageById(userImageMessage.id, {
                status: 'failed',
                content: '图片分析失败，请重试'
              });

              this.showToast('食物分析失败');
            }
          } catch (err) {
            this.error('Chat: 图片处理过程中出错:', err);
            this.showToast('图片处理失败');
            this.setData({ isLoading: false });
          } finally {
            this.hideLoading();
            this.setData({ isLoading: false });
          }
        },
        fail: (err) => {
          this.log('Chat: 用户取消了选择图片或选择失败', err);
          this.setData({ isLoading: false });
          this.hideLoading();
        }
      });
    } catch (error) {
      this.error('Chat: 上传图片错误:', error);
      this.showToast(error.message || '上传图片失败');
      this.setData({ isLoading: false });
      this.hideLoading();
    }
  }

  /**
   * 导航到食物预览页面
   * @param {Object} e - 事件对象
   */
  async navigateToFoodPreview(e) {
    try {
      // Get message information from the target element's dataset
      const { messageId, isCompleted } = e.currentTarget.dataset;
      const msgId = messageId || e.currentTarget.dataset.messageId || e.currentTarget.id;

      if (!msgId) {
        this.error("Could not determine the message ID for food preview");
        this.showToast('无法查看详情');
        return;
      }

      this.log('准备跳转到预览页面:', { messageId: msgId, isCompleted });

      // Ensure we have user identifier if needed by preview page (openId or token?)
      if (!this.getData('openId')) {
        await this.page.getOpenId(); // Get openId if still used
      }
      const userIdentifier = this.getData('openId'); // Or perhaps a user ID from token
      if (!userIdentifier) {
        throw new Error('无法获取用户信息');
      }

      if (isCompleted === "true" || isCompleted === true) {
        this.showToast('该记录已确认', 'info');
        return;
      }

      // Find the message in our messages array to get the full data
      const messages = this.getData('messages');
      const message = messages.find(msg => msg.id === msgId);
      if (!message || message.type !== 'food_analysis') {
        this.error("Could not find the food analysis message in the data.");
        this.showToast('无法查看详情');
        return;
      }

      const analysis = JSON.parse(message.content);
      let imageUrl = message.thumbImageUrl;
      const mealType = message.mealType || this.getMealTypeByTime();
      const mealDate = message.mealDate || new Date().toISOString().split('T')[0];
      const recognitionId = analysis?.recognitionId || null;

      // 获取数据库消息ID（如果有的话）
      const dbMessageId = message.dbMessageId || message.id;

      // Construct URL - pass necessary identifiers and data context
      let url = `/pages/foodPreview/preview?mealType=${mealType}&selectedDate=${mealDate}&openId=${userIdentifier}&from=chatbot`;
      if (recognitionId) url += `&recognitionId=${recognitionId}`; // Pass backend analysis ID if available

      // 优先传递数据库消息ID，如果存在
      if (dbMessageId && !isNaN(parseInt(dbMessageId))) {
        url += `&messageId=${dbMessageId}`; // Pass database message ID for updates
        this.log('传递数据库消息ID:', dbMessageId);
      } else {
        url += `&messageId=${msgId}`; // Fallback to local message ID
        this.log('传递本地消息ID:', msgId);
      }

      this.log('跳转URL:', url);

      // 查找临时存储的分析结果
      const { lastFoodAnalysis } = this.data;
      let foodDataToPass = null;

      // 检查消息匹配 (dbMessageId为当前选中消息的ID)
      if (lastFoodAnalysis && lastFoodAnalysis.dbMessageId === dbMessageId) {
        this.log('使用临时存储的食物分析结果');
        foodDataToPass = lastFoodAnalysis.fullAnalysis;
      } else {
        // 解析消息内容获取recognitionId
        let recognitionId = null;
        try {
          const content = JSON.parse(message.content);
          recognitionId = content.recognitionId;
        } catch (e) {
          this.error('解析消息内容失败:', e);
        }

        // 如果有recognitionId，通过API获取完整数据
        if (recognitionId) {
          try {
            // 显示加载状态
            this.showLoading('获取详细数据...', true);

            // 调用API获取完整数据
            const result = await foodApi.getFoodRecognitionById(recognitionId);
            this.log('通过API获取的食物分析结果:', result);
            if (result && result.image_url && !result.image_url.startsWith('http')) {
              imageUrl = requestApi.BASE_URL + '/meal-images' + (result.image_url.startsWith('/') ? '' : '/') + result.image_url;
            }
            foodDataToPass = result;
            foodDataToPass.image_url = imageUrl;
            // 确保food_items或food_item_list中的每个项都有imageUrl
            if (foodDataToPass.food_items && Array.isArray(foodDataToPass.food_items)) {
              foodDataToPass.food_items.forEach(item => {
                // 处理缩略图URL
                if (item.food_details && item.food_details.thumb_image_url && !item.food_details.thumb_image_url.startsWith('http')) {
                  item.image_url = requestApi.BASE_URL + (item.food_details.thumb_image_url.startsWith('/') ? '' : '/') + item.food_details.thumb_image_url;
                }
              });
            } else if (foodDataToPass.food_item_list && Array.isArray(foodDataToPass.food_item_list)) {
              foodDataToPass.food_item_list.forEach(item => {
                // 处理缩略图URL
                if (item.food_details && item.food_details.thumb_image_url && !item.food_details.thumb_image_url.startsWith('http')) {
                  item.image_url = requestApi.BASE_URL + (item.food_details.thumb_image_url.startsWith('/') ? '' : '/') + item.food_details.thumb_image_url;
                }
              });
            }

            this.hideLoading();
          } catch (error) {
            this.error('获取食物分析详情失败:', error);
            this.hideLoading();
            this.showToast('获取详情失败');
            return; // 终止导航
          }
        }
      }

      // 如果仍然没有数据，使用消息中的数据或空对象
      if (!foodDataToPass) {
        foodDataToPass = message.fullAnalysis || {
          food_items: [],
          meal_name: '',
          meal_type: '',
          meal_date: '',
          image_url: '',
          total_nutritional_profile: {
            calory: 0,
            fat: 0,
            protein: 0,
            carbohydrate: 0
          }
        };
      }

      // --- Navigate ---
      wx.navigateTo({
        url: url,
        events: {
          // Listener for data coming back from the preview page
          acceptDataFromOpenedPage: (data) => {
            this.log('接收到来自预览页面的数据:', data);
            if (data && data.action === 'confirmed' && data.messageId) {
              // Handle confirmation - Update the local message state
              this.page.messageManager.handleFoodAnalysisConfirmed(data.messageId, data.updatedFoodData);
            }
          }
        },
        success: (res) => {
          this.log('页面跳转成功，准备发送数据');
          // Send initial data to the preview page
          res.eventChannel.emit('acceptDataFromOpenerPage', {
            foodData: foodDataToPass,
          });
        },
        fail: (error) => {
          this.error('页面跳转失败:', error);
          this.showToast('无法打开预览');
        }
      });
    } catch (error) {
      this.error('跳转失败:', error);
      this.showToast(error.message || '跳转失败');
    }
  }

  /**
   * 根据当前时间获取餐食类型
   * @returns {string} 餐食类型
   */
  getMealTypeByTime() {
    const now = new Date();
    const hour = now.getHours();
    if (hour >= 5 && hour < 10) return 'breakfast'; // Adjusted ranges
    if (hour >= 11 && hour < 14) return 'lunch';
    if (hour >= 17 && hour < 21) return 'dinner';
    return 'snack'; // Default to snack
  }
}

module.exports = FoodAnalysisManager;
