/**
 * 训练计划管理器
 * 处理训练计划相关功能
 */
const BaseManager = require('./baseManager');

class TrainingPlanManager extends BaseManager {
  constructor(page) {
    super(page);
  }

  /**
   * 导航到训练计划详情页
   * @param {Object} e - 事件对象
   */
  async navigateToTrainingPlan(e) {
    let planId = e.currentTarget.dataset.planId;
    this.log('训练计划ID:', planId);
    if (!planId) {
      this.error('无法获取训练计划ID');
      this.showToast('无法查看详情');
      return;
    }

    // 查找当前消息
    const messageId = e.currentTarget.dataset.messageId;
    const messages = this.getData('messages');
    const message = messageId
      ? messages.find(msg => msg.id === messageId)
      : messages.find(msg => msg.trainingPlan && msg.trainingPlan.id === planId);

    if (!message) {
      this.error('无法找到相关消息');
      this.showToast('无法获取计划详情');
      return;
    }

    // 从meta_info中获取训练计划ID
    if (message.meta_info?.training_params?.related_plan_id) {
      planId = message.meta_info.training_params.related_plan_id;
      this.log('使用training_params.related_plan_id作为planId:', planId);
    } else if (message.trainingPlan?.training_params?.related_plan_id) {
      planId = message.trainingPlan.training_params.related_plan_id;
      this.log('使用trainingPlan.training_params.related_plan_id作为planId:', planId);
    }

    try {
      // 显示加载提示
      wx.showLoading({ title: '加载计划数据...' });

      // 使用API获取最新的计划详情
      const trainingPlanApi = require('../../../api/training-plan');
      const planDetail = await trainingPlanApi.getPlanDetail(planId);

      if (!planDetail) {
        throw new Error('获取计划详情失败');
      }

      // 获取计划的所有训练
      const workouts = await trainingPlanApi.getPlanWorkouts(planId);

      if (!workouts || !Array.isArray(workouts)) {
        throw new Error('获取计划训练列表失败');
      }

      // 将workouts添加到planDetail中
      planDetail.workouts = workouts;

      this.log('获取到的最新计划详情:', planDetail);
      this.log('获取到的训练列表:', workouts);

      // 确保训练参数存在
      if (message.meta_info?.training_params) {
        planDetail.training_params = message.meta_info.training_params;
      }

      wx.hideLoading();

      // 跳转到训练计划详情页
      wx.navigateTo({
        url: `/pages/training/plan-detail/index?planId=${planId}`,
        success: (res) => {
          // 通过eventChannel向被打开页面传送数据，包括消息ID
          res.eventChannel.emit('acceptDataFromOpenerPage', {
            plan: planDetail, // 使用最新的计划数据
            messageId: message.id // 传递消息ID，用于后续更新meta_info
          });
        },
        fail: (error) => {
          this.error('跳转到训练计划详情页失败:', error);
          this.showToast('无法打开详情页');
        }
      });
    } catch (error) {
      wx.hideLoading();
      this.error('获取最新计划数据失败:', error);
      this.showToast('获取计划数据失败');

      // 如果API获取失败，尝试使用缓存数据作为备选方案
      this.log('尝试使用缓存数据作为备选方案');

      // 尝试使用trainingPlan字段作为备选方案
      if (message.trainingPlan) {
        const completePlan = message.trainingPlan;
        this.log('使用trainingPlan字段数据作为备选:', completePlan);

        wx.navigateTo({
          url: `/pages/training/plan-detail/index?planId=${planId}`,
          success: (res) => {
            // 通过eventChannel向被打开页面传送数据，包括消息ID
            res.eventChannel.emit('acceptDataFromOpenerPage', {
              plan: completePlan,
              messageId: message.id // 传递消息ID，用于后续更新meta_info
            });
          },
          fail: (error) => {
            this.error('跳转到训练计划详情页失败:', error);
            this.showToast('无法打开详情页');
          }
        });
        return;
      }

      // 兼容旧版数据结构 - 如果有complete_training_plan字段
      if (message.meta_info?.complete_training_plan) {
        const completePlan = {
          ...message.meta_info.complete_training_plan
        };

        // 确保训练参数存在
        if (message.meta_info.training_params) {
          completePlan.training_params = message.meta_info.training_params;
        }

        this.log('从meta_info.complete_training_plan获取到备选训练计划数据:', completePlan);

        wx.navigateTo({
          url: `/pages/training/plan-detail/index?planId=${planId}`,
          success: (res) => {
            // 通过eventChannel向被打开页面传送数据，包括消息ID
            res.eventChannel.emit('acceptDataFromOpenerPage', {
              plan: completePlan,
              messageId: message.id // 传递消息ID，用于后续更新meta_info
            });
          },
          fail: (error) => {
            this.error('跳转到训练计划详情页失败:', error);
            this.showToast('无法打开详情页');
          }
        });
        return;
      }

      this.error('无法获取完整的训练计划数据');
      this.showToast('无法获取完整计划');
    }
  }

  /**
   * 处理训练计划消息的展示
   * @param {Object} message - 消息对象
   * @returns {Object} 处理后的消息对象
   */
  async processTrainingPlanMessage(message) {
    if (!message) return message;

    // 创建一个新的消息对象，避免修改原始对象
    const processedMessage = { ...message };

    try {
      // 检查是否是训练计划消息 - 通过training_params.related_plan_id判断
      if (processedMessage.meta_info?.training_params?.related_plan_id) {
        const planId = processedMessage.meta_info.training_params.related_plan_id;
        this.log('发现训练计划消息，planId:', planId);

        try {
          // 使用API获取训练计划数据
          const trainingPlanApi = require('../../../api/training-plan');

          // 获取计划详情
          const planDetail = await trainingPlanApi.getPlanDetail(planId);

          if (!planDetail) {
            throw new Error('获取计划详情失败');
          }

          // 获取计划的所有训练
          const workouts = await trainingPlanApi.getPlanWorkouts(planId);

          if (!workouts || !Array.isArray(workouts)) {
            throw new Error('获取计划训练列表失败');
          }

          this.log('获取到的计划详情:', planDetail);
          this.log('获取到的训练列表:', workouts.length);

          // 导入图片处理工具
          let imageHelper;
          try {
            imageHelper = require('../../../utils/image-helper');
          } catch (error) {
            this.error('导入image-helper失败:', error);
          }

          // 创建用于展示的训练计划数据
          const trainingPlan = {
            id: planId,
            name: planDetail.name,
            description: planDetail.description,
            estimated_duration: planDetail.estimated_duration,
            duration_weeks: planDetail.duration_weeks,
            // 添加来自meta_info的训练参数
            training_params: processedMessage.meta_info.training_params || {},
            // 添加状态标志
            isCompleted: processedMessage.meta_info.training_params?.complete === true,
            status: processedMessage.meta_info.training_params?.status || 'not_started',
            // 添加workouts字段
            workouts: workouts
          };

          // 处理exercises字段 - 从第一个workout中提取exercises
          if (workouts && workouts.length > 0) {
            const workout = workouts[0];
            if (workout.exercises && Array.isArray(workout.exercises)) {
              trainingPlan.exercises = workout.exercises.map(exercise => {
                // 处理图片URL
                let imageUrl = '/images/exercise-placeholder.png';

                if (imageHelper) {
                  // 优先使用exercise_image字段
                  if (exercise.exercise_image) {
                    imageUrl = imageHelper.getFullImageUrl(exercise.exercise_image);
                  } else if (exercise.gif_url) {
                    imageUrl = imageHelper.getFullImageUrl(exercise.gif_url);
                  } else if (exercise.image_url) {
                    imageUrl = imageHelper.getFullImageUrl(exercise.image_url);
                  } else if (exercise.image_name) {
                    imageUrl = imageHelper.getFullImageUrl(exercise.image_name);
                  }
                } else {
                  imageUrl = exercise.gif_url || exercise.image_url || exercise.image_name || '/images/exercise-placeholder.png';
                }

                return {
                  ...exercise,
                  display_sets: `${exercise.sets}组x${exercise.reps}次`,
                  image_url: imageUrl
                };
              });
            } else {
              trainingPlan.exercises = [];
            }
          } else {
            trainingPlan.exercises = [];
          }

          // 设置trainingPlan字段
          processedMessage.trainingPlan = trainingPlan;

          // 设置showTrainingPlan标志，用于UI显示
          processedMessage.showTrainingPlan = true;

          this.log('处理后的训练计划数据:', {
            id: trainingPlan.id,
            name: trainingPlan.name,
            exercisesCount: trainingPlan.exercises?.length || 0
          });
        } catch (error) {
          this.error('处理训练计划消息失败:', error);
          // 如果API获取失败，不显示训练计划
          processedMessage.showTrainingPlan = false;
        }
      } else if (processedMessage.meta_info?.complete_training_plan) {
        // 兼容旧版数据结构 - 如果仍有complete_training_plan字段
        this.log('使用旧版数据结构处理训练计划消息');
        const plan = processedMessage.meta_info.complete_training_plan;

        // 导入图片处理工具
        let imageHelper;
        try {
          imageHelper = require('../../../utils/image-helper');
        } catch (error) {
          this.error('导入image-helper失败:', error);
        }

        // 创建用于展示的训练计划数据
        const trainingPlan = {
          id: plan.training_params?.related_plan_id || plan.id,
          name: plan.name,
          description: plan.description,
          estimated_duration: plan.estimated_duration,
          duration_weeks: plan.duration_weeks,
          // 添加来自meta_info的训练参数
          training_params: processedMessage.meta_info.training_params || {},
          // 添加状态标志
          isCompleted: processedMessage.meta_info.training_params?.complete === true,
          status: processedMessage.meta_info.training_params?.status || 'not_started'
        };

        // 处理exercises字段
        if (Array.isArray(plan.exercises)) {
          // 如果有exercises字段，使用它
          trainingPlan.exercises = plan.exercises.map(exercise => {
            // 处理图片URL
            let imageUrl = '/images/exercise-placeholder.png';

            if (imageHelper) {
              // 优先使用exercise_image字段
              if (exercise.exercise_image) {
                imageUrl = imageHelper.getFullImageUrl(exercise.exercise_image);
              } else if (exercise.gif_url) {
                imageUrl = imageHelper.getFullImageUrl(exercise.gif_url);
              } else if (exercise.image_url) {
                imageUrl = imageHelper.getFullImageUrl(exercise.image_url);
              } else if (exercise.image_name) {
                imageUrl = imageHelper.getFullImageUrl(exercise.image_name);
              }
            } else {
              imageUrl = exercise.gif_url || exercise.image_url || exercise.image_name || '/images/exercise-placeholder.png';
            }

            return {
              ...exercise,
              display_sets: `${exercise.sets}组x${exercise.reps}次`,
              image_url: imageUrl
            };
          });
        } else if (plan.workouts && Array.isArray(plan.workouts) && plan.workouts.length > 0) {
          // 如果有workouts字段，从第一个workout中提取exercises
          const workout = plan.workouts[0];
          if (workout.exercises && Array.isArray(workout.exercises)) {
            trainingPlan.exercises = workout.exercises.map(exercise => {
              // 处理图片URL
              let imageUrl = '/images/exercise-placeholder.png';

              if (imageHelper) {
                // 优先使用exercise_image字段
                if (exercise.exercise_image) {
                  imageUrl = imageHelper.getFullImageUrl(exercise.exercise_image);
                } else if (exercise.gif_url) {
                  imageUrl = imageHelper.getFullImageUrl(exercise.gif_url);
                } else if (exercise.image_url) {
                  imageUrl = imageHelper.getFullImageUrl(exercise.image_url);
                } else if (exercise.image_name) {
                  imageUrl = imageHelper.getFullImageUrl(exercise.image_name);
                }
              } else {
                imageUrl = exercise.gif_url || exercise.image_url || exercise.image_name || '/images/exercise-placeholder.png';
              }

              return {
                ...exercise,
                display_sets: `${exercise.sets}组x${exercise.reps}次`,
                image_url: imageUrl
              };
            });
          } else {
            trainingPlan.exercises = [];
          }

          // 添加workouts字段，保持与plan-detail页面的数据结构一致
          trainingPlan.workouts = plan.workouts;
        } else {
          trainingPlan.exercises = [];
        }

        // 设置trainingPlan字段
        processedMessage.trainingPlan = trainingPlan;

        // 设置showTrainingPlan标志，用于UI显示
        processedMessage.showTrainingPlan = true;

        this.log('处理后的旧版训练计划数据:', {
          id: trainingPlan.id,
          name: trainingPlan.name,
          exercisesCount: trainingPlan.exercises?.length || 0
        });
      }
    } catch (error) {
      this.error('处理训练计划消息时发生异常:', error);
      // 确保返回原始消息
      return message;
    }

    return processedMessage;
  }

  /**
   * 刷新所有训练计划数据
   * 检查所有消息中的训练计划，并根据planId获取最新数据
   * @param {boolean} [showLoading=true] - 是否显示加载提示
   * @returns {Promise<Array>} 更新的消息ID数组
   */
  async refreshTrainingPlans(showLoading = true) {
    this.log('开始刷新所有训练计划数据');

    try {
      // 获取所有消息
      const messages = this.getData('messages');
      if (!messages || messages.length === 0) {
        this.log('没有消息，无需刷新训练计划');
        return [];
      }

      // 找出包含训练计划的消息 - 主要通过training_params.related_plan_id判断
      const trainingPlanMessages = messages.filter(msg =>
        msg.meta_info?.training_params?.related_plan_id ||
        msg.meta_info?.complete_training_plan // 兼容旧版数据结构
      );

      if (trainingPlanMessages.length === 0) {
        this.log('没有包含训练计划的消息，无需刷新');
        return [];
      }

      this.log(`找到 ${trainingPlanMessages.length} 条包含训练计划的消息`);

      if (showLoading) {
        wx.showLoading({ title: '更新训练计划...' });
      }

      // 用于存储已处理的planId，避免重复处理
      const processedPlanIds = new Set();
      // 用于存储更新成功的消息ID
      const updatedMessageIds = [];

      // 逐个处理训练计划消息
      for (const message of trainingPlanMessages) {
        try {
          // 获取planId，优先使用training_params.related_plan_id
          let planId = message.meta_info?.training_params?.related_plan_id;

          // 如果没有related_plan_id，尝试使用complete_training_plan.id (兼容旧版数据结构)
          if (!planId && message.meta_info?.complete_training_plan?.id) {
            planId = message.meta_info.complete_training_plan.id;
          }

          // 如果没有有效的planId，跳过此消息
          if (!planId) {
            this.log(`消息 ${message.id} 没有有效的planId，跳过`);
            continue;
          }

          // 如果已经处理过此planId，跳过
          if (processedPlanIds.has(planId)) {
            this.log(`planId ${planId} 已经处理过，跳过`);
            continue;
          }

          // 标记为已处理
          processedPlanIds.add(planId);

          this.log(`开始更新消息 ${message.id} 的训练计划 ${planId}`);

          // 调用processTrainingPlanMessage处理消息
          // 由于processTrainingPlanMessage现在是异步的，它会自动获取最新的计划数据
          const updatedMessage = await this.processTrainingPlanMessage(message);

          if (updatedMessage.showTrainingPlan) {
            // 更新消息列表中的消息
            const messageIndex = messages.findIndex(msg => msg.id === message.id);
            if (messageIndex !== -1) {
              messages[messageIndex] = updatedMessage;
              this.setData({ messages });

              // 更新缓存
              if (this.page.cacheManager) {
                this.page.cacheManager.saveChatMessageToCache(updatedMessage);
              } else {
                // 使用备用方法
                this.page.saveChatMessageToCache(updatedMessage);
              }

              updatedMessageIds.push(message.id);
              this.log(`成功更新消息 ${message.id} 的训练计划`);
            }
          } else {
            this.log(`更新消息 ${message.id} 的训练计划失败`);
          }
        } catch (error) {
          this.error(`处理消息 ${message.id} 的训练计划时出错:`, error);
          // 继续处理下一条消息，不中断整个流程
        }
      }

      if (showLoading) {
        wx.hideLoading();
      }

      this.log(`训练计划刷新完成，成功更新 ${updatedMessageIds.length} 条消息`);
      return updatedMessageIds;
    } catch (error) {
      if (showLoading) {
        wx.hideLoading();
      }
      this.error('刷新训练计划数据失败:', error);
      return [];
    }
  }

  /**
   * 更新训练计划消息
   * 当用户在训练计划详情页点击"加入计划"或"开始训练"按钮时，
   * 通过API获取最新的计划详情，并更新聊天消息中的训练计划数据
   * @param {string|number} messageId - 消息ID
   * @param {string|number} planId - 计划ID
   * @param {Object} statusInfo - 状态信息，如 {status: 'active'}
   * @param {boolean} [showLoading=true] - 是否显示加载提示
   * @returns {Promise<boolean>} 更新是否成功
   */
  async updateTrainingPlanMessage(messageId, planId, statusInfo = {}, showLoading = true) {
    if (!messageId || !planId) {
      this.error('更新训练计划消息失败：缺少必要参数');
      return false;
    }

    this.log('准备更新训练计划消息:', { messageId, planId, statusInfo });

    try {
      // 显示加载状态（如果需要）
      if (showLoading) {
        wx.showLoading({ title: '更新训练计划...' });
      }

      // 查找消息
      const messages = this.getData('messages');
      const messageIndex = messages.findIndex(msg => msg.id === messageId);

      if (messageIndex === -1) {
        throw new Error('无法找到要更新的消息: ' + messageId);
      }

      const message = messages[messageIndex];

      // 确保消息有meta_info字段
      if (!message.meta_info) {
        message.meta_info = {};
      }

      // 确保training_params存在
      if (!message.meta_info.training_params) {
        message.meta_info.training_params = {};
      }

      // 更新training_params字段
      message.meta_info.training_params = {
        ...message.meta_info.training_params,
        complete: true,
        related_plan_id: planId,
        // 合并传入的状态信息
        ...statusInfo
      };

      // 移除旧的complete_training_plan字段，使用新的数据结构
      if (message.meta_info.complete_training_plan) {
        this.log('移除旧的complete_training_plan字段');
        delete message.meta_info.complete_training_plan;
      }

      // 使用processTrainingPlanMessage处理消息，它会自动获取最新的计划数据
      const updatedMessage = await this.processTrainingPlanMessage(message);

      // 记录处理后的消息中的训练计划数据
      if (updatedMessage.trainingPlan) {
        this.log('处理后的训练计划数据:', {
          name: updatedMessage.trainingPlan.name,
          estimated_duration: updatedMessage.trainingPlan.estimated_duration,
          description: updatedMessage.trainingPlan.description
        });
      } else {
        this.log('处理后的消息中没有trainingPlan字段');
      }

      // 更新消息列表
      messages[messageIndex] = updatedMessage;
      this.setData({ messages });

      // 更新缓存
      if (this.page.cacheManager) {
        this.page.cacheManager.saveChatMessageToCache(updatedMessage);
      } else {
        // 使用备用方法
        this.page.saveChatMessageToCache(updatedMessage);
      }

      // 更新数据库
      // 尝试获取有效的数据库消息ID
      let dbId = null;

      // 首先检查message.dbMessageId
      if (message.dbMessageId) {
        dbId = message.dbMessageId;
        this.log('从message.dbMessageId获取到ID:', dbId);
      }
      // 然后检查message.id (如果是数字类型)
      else if (message.id && typeof message.id === 'number') {
        dbId = message.id;
        this.log('从message.id获取到ID:', dbId);
      }
      // 最后检查messageId参数 (如果是数字类型)
      else if (messageId && !isNaN(parseInt(messageId, 10))) {
        dbId = parseInt(messageId, 10);
        this.log('从messageId参数获取到ID:', dbId);
      }

      if (dbId) {
        const numericDbId = parseInt(dbId, 10);

        if (isNaN(numericDbId)) {
          this.error('无法更新数据库消息：无效的ID类型', dbId);
          throw new Error('无法更新数据库消息：无效的ID类型 ' + dbId);
        }

        this.log('准备更新数据库消息:', {
          messageId,
          dbId: numericDbId,
          content: message.content,
          role: message.role,
          metaInfoKeys: Object.keys(message.meta_info || {})
        });

        // 确保meta_info是有效的对象
        const metaInfo = message.meta_info || {};
        console.log('meta_info:', metaInfo);

        // 记录关键字段，用于调试
        this.log('meta_info中的关键字段:', {
          'training_params存在': Boolean(metaInfo.training_params),
          'training_params.related_plan_id存在': Boolean(metaInfo.training_params?.related_plan_id)
        });

        try {
          const chatApi = require('../../../api/chatAI');

          // 添加重试机制
          let retryCount = 0;
          const maxRetries = 2;
          let updateSuccess = false;

          while (!updateSuccess && retryCount <= maxRetries) {
            try {
              if (retryCount > 0) {
                this.log(`尝试第 ${retryCount} 次重试更新数据库消息...`);
              }

              const updatedMessage = await chatApi.updateMessage(
                numericDbId,
                message.content,
                message.role,
                message.meta_info
              );

              this.log('数据库消息更新成功，返回数据:', {
                id: updatedMessage?.id,
                hasMetaInfo: Boolean(updatedMessage?.meta_info),
                metaInfoKeys: updatedMessage?.meta_info ? Object.keys(updatedMessage.meta_info) : []
              });

              updateSuccess = true;

              // 更新本地消息的dbMessageId，确保后续更新能找到正确的ID
              if (!message.dbMessageId && updatedMessage?.id) {
                message.dbMessageId = updatedMessage.id;
                this.log(`更新本地消息的dbMessageId为 ${updatedMessage.id}`);
              }
            } catch (updateError) {
              retryCount++;
              this.error(`更新数据库消息失败 (尝试 ${retryCount}/${maxRetries}):`, updateError);

              if (retryCount > maxRetries) {
                throw updateError; // 重试次数用完，抛出错误
              }

              // 等待一段时间再重试
              await new Promise(resolve => setTimeout(resolve, 1000));
            }
          }
        } catch (error) {
          this.error('更新数据库消息最终失败:', error);
          // 不抛出错误，允许继续执行，但记录失败
          wx.showToast({
            title: '数据同步失败，请稍后重试',
            icon: 'none'
          });
        }
      } else {
        this.error('消息没有有效的数据库ID，无法更新数据库');
        // 尝试记录更多信息以便调试
        this.log('消息对象信息:', {
          hasId: Boolean(message.id),
          idType: typeof message.id,
          hasDbMessageId: Boolean(message.dbMessageId),
          messageIdParam: messageId,
          messageIdParamType: typeof messageId
        });
      }

      if (showLoading) {
        wx.hideLoading();
      }
      this.log('训练计划消息更新完成');
      return true;
    } catch (error) {
      if (showLoading) {
        wx.hideLoading();
      }
      this.error('更新训练计划消息失败:', error);
      wx.showToast({
        title: '更新训练计划失败',
        icon: 'none'
      });
      return false;
    }
  }
}

module.exports = TrainingPlanManager;
