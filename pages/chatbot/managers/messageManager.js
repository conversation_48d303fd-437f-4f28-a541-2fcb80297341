/**
 * 消息管理器
 * 处理消息的发送、接收和处理
 */
const BaseManager = require('./baseManager');
const messageProcessor = require('../../../utils/messageProcessor');
const chatApi = require('../../../api/chatAI');

class MessageManager extends BaseManager {
  constructor(page) {
    super(page);
    this.tempUserMessageId = null;
    this.tempUserRealId = null;
    this.lastMessageSent = null;
    this.pollIntervalId = null;
  }

  /**
   * 处理单条消息
   * @param {Object} message - 原始消息对象
   * @returns {Object} 处理后的消息对象
   */
  async processMessage(message) {
    if (!message) return null;

    try {
      // 使用messageProcessor进行基本处理
      const processedMessage = messageProcessor.processMessage(message, { processUI: true });

      if (!processedMessage) return null;

      // 检查是否是workout消息，且需要进一步处理
      if (processedMessage.hasWorkout && !processedMessage.showWorkout) {
        try {
          // 处理workout数据
          this.log('检测到workout消息，处理workout数据:', processedMessage.workoutId);
          const updatedMessage = await this.processWorkoutMessage(processedMessage);

          if (updatedMessage) {
            return updatedMessage;
          } else {
            this.log('workout处理后返回null，使用基本处理结果');
            return processedMessage;
          }
        } catch (error) {
          this.error('处理workout消息失败:', error);
          // 如果处理失败，返回原始处理结果
          return processedMessage;
        }
      }

      // 检查是否是训练计划消息，且需要进一步处理
      if ((processedMessage.hasTrainingPlan ||
           processedMessage.meta_info?.training_params?.related_plan_id) &&
          !processedMessage.showTrainingPlan &&
          this.page.trainingPlanManager) {
        try {
          // 使用trainingPlanManager处理训练计划数据
          this.log('检测到训练计划消息，使用trainingPlanManager处理:', processedMessage.id);
          const updatedMessage = await this.page.trainingPlanManager.processTrainingPlanMessage(processedMessage);

          if (updatedMessage) {
            return updatedMessage;
          } else {
            this.log('trainingPlanManager处理后返回null，使用基本处理结果');
            return processedMessage;
          }
        } catch (error) {
          this.error('处理训练计划消息失败:', error);
          // 如果处理失败，返回原始处理结果
          return processedMessage;
        }
      }

      return processedMessage;
    } catch (error) {
      this.error('处理消息时出错:', error, '消息内容:', JSON.stringify(message).substring(0, 200) + '...');

      // 如果处理失败，尝试返回一个基本的消息对象
      if (message) {
        return {
          id: message.id || `error_${Date.now()}`,
          role: message.role || 'unknown',
          type: message.type || 'text',
          content: message.content || '消息处理失败',
          meta_info: message.meta_info || {}
        };
      }
      return null;
    }
  }

  /**
   * 发送消息
   * @param {string|object} customMessage - 可以是自定义消息字符串或事件对象
   */
  sendMessage(customMessage) {
    this.log('sendMessage 被调用，参数类型:', typeof customMessage);

    // 如果没有消息且输入框为空，则不发送
    if (!customMessage && !this.getData('inputValue').trim()) {
      this.log('没有消息内容，不发送');
      return;
    }

    // 确保消息是字符串类型
    let messageContent;

    if (customMessage) {
      // 检查是否为事件对象 (来自bindtap等事件)
      if (customMessage.type && customMessage.currentTarget) {
        this.log('检测到事件对象，使用输入框内容');
        messageContent = this.getData('inputValue').trim();
      } else if (typeof customMessage === 'string') {
        // 已经是字符串
        messageContent = customMessage.trim();
      } else if (customMessage.toString) {
        // 尝试转换为字符串
        messageContent = customMessage.toString().trim();
      } else {
        this.error('无法处理的消息类型:', customMessage);
        this.showToast('消息格式错误');
        return;
      }
    } else {
      // 使用输入框内容
      messageContent = this.getData('inputValue').trim();
    }

    // 最后检查消息是否为空
    if (!messageContent) {
      this.log('处理后消息为空，不发送');
      return;
    }

    this.log('准备发送消息:', messageContent);

    // 显示加载状态并清空输入框
    this.setData({
      isLoading: true,
      inputValue: ''
    });

    // 检查是否有当前会话ID
    const currentConversationId = this.getData('currentConversationId');
    if (!currentConversationId) {
      this.error('发送失败: 没有会话ID');
      this.showToast('发送失败，请重新连接');
      this.setData({ isLoading: false });
      return;
    }

    // 创建一个客户端消息ID，用于匹配服务器返回的消息
    const clientMessageId = `client_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;

    // 添加临时用户消息到UI (不保存到数据库)
    const userMessage = {
      id: clientMessageId,
      role: 'user',
      type: 'text',
      content: messageContent,
      created_at: new Date().toISOString(),
      isTemporary: true, // 标记为临时消息
      clientMessageId: clientMessageId // 保存客户端ID用于匹配
    };

    // 将临时消息添加到消息列表
    const messages = this.getData('messages');

    // 由于processMessage现在是异步的，先使用基本处理
    const basicProcessedMessage = messageProcessor.processMessage(userMessage, { processUI: true });

    const updatedMessages = [...messages, basicProcessedMessage];
    this.setData({
      messages: updatedMessages,
      lastMessageSent: messageContent, // 存储最后发送的消息内容
      tempUserMessageId: clientMessageId // 存储临时ID以便后续更新
    });

    // 异步处理完整的消息（如果需要）
    this.processMessage(userMessage).then(fullyProcessedMessage => {
      if (fullyProcessedMessage && fullyProcessedMessage !== basicProcessedMessage) {
        // 如果处理结果不同，更新消息
        const messageIndex = this.getData('messages').findIndex(msg => msg.id === clientMessageId);
        if (messageIndex !== -1) {
          const newMessages = [...this.getData('messages')];
          newMessages[messageIndex] = fullyProcessedMessage;
          this.setData({ messages: newMessages });
        }
      }
    }).catch(error => {
      this.error('异步处理消息失败:', error);
    });

    // 滚动到底部显示临时消息
    this.page.scrollToBottom();

    // 准备简化的元数据，只包含客户端ID和消息类型
    const metaInfo = {
      type: 'text',
      clientMessageId: clientMessageId
    };

    this.log('发送消息的元数据:', metaInfo);

    // 使用sendChatMessage发送消息到服务器，并传递元数据
    chatApi.sendChatMessage(
      currentConversationId,
      messageContent,
      metaInfo, // 传递简化的元数据
      (error) => {
        this.error('发送消息失败:', error);
        this.showToast('发送失败:' + (error.errMsg || error.message || '未知错误'));

        // 不删除临时消息，而是标记为发送失败状态
        const errorMsg = error.errMsg || error.message || '发送失败';
        const updatedMessage = this.page.updateMessageById(clientMessageId, {
          status: 'failed',
          error: errorMsg,
          isTemporary: false
        });

        // 保存失败状态到缓存
        if (updatedMessage) {
          this.page.cacheManager.saveChatMessageToCache(updatedMessage);
        }

        this.setData({ isLoading: false });
      }
    ).then(success => {
      if (success) {
        this.log('消息发送成功，开始轮询响应');

        // 启动轮询获取回复
        this.startResponsePolling();
      } else {
        // 不删除临时消息，而是标记为发送失败状态
        const updatedMessage = this.page.updateMessageById(clientMessageId, {
          status: 'failed',
          error: '发送失败',
          isTemporary: false
        });

        // 保存失败状态到缓存
        if (updatedMessage) {
          this.page.cacheManager.saveChatMessageToCache(updatedMessage);
        }

        this.setData({ isLoading: false });
      }
    });
  }

  /**
   * 开始轮询获取服务器响应
   */
  startResponsePolling() {
    // 清除任何现有的轮询
    if (this.pollIntervalId) {
      clearInterval(this.pollIntervalId);
    }

    // 设置轮询参数
    let pollingCount = 0;
    const maxPollingAttempts = 30; // 最多轮询30次
    const pollingInterval = 1000; // 每秒轮询一次

    // 开始轮询
    const pollIntervalId = setInterval(() => {
      pollingCount++;

      // 如果超过最大尝试次数，停止轮询
      if (pollingCount > maxPollingAttempts) {
        clearInterval(pollIntervalId);
        this.setData({
          isLoading: false,
          pollIntervalId: null
        });
        this.showToast('获取回复超时');
        return;
      }

      // 使用pollMessages函数获取新消息
      chatApi.pollMessages(
        this.getData('currentConversationId'),
        this.getData('lastMessageId'),
        {
          onMessage: (messages) => this.handlePolledMessages(messages),
          onError: (error) => this.handlePollingError(error, pollIntervalId)
        }
      ).catch(err => {
        this.error('轮询请求失败:', err);
        // 错误处理，但继续轮询
      });
    }, pollingInterval);

    // 保存轮询ID以便后续可以清除
    this.setData({ pollIntervalId });
    this.pollIntervalId = pollIntervalId;
  }

  /**
   * 处理轮询返回的消息
   * @param {Array} messages - 轮询返回的消息数组
   */
  async handlePolledMessages(messages) {
    if (!messages || messages.length === 0) return;

    this.log('轮询收到新消息:', messages);

    // 处理新消息
    let latestMessageId = this.getData('lastMessageId');
    const processedMessages = [];

    // 处理每条新消息 - 使用for...of循环以支持异步处理
    for (const msg of messages) {
      // 由于processMessage现在是异步的，使用await
      const processedMsg = await this.processMessage(msg);

      this.log("处理后的轮询信息：", processedMsg);

      // 如果已经有服务器返回的用户消息ID，且匹配到了这条消息，则跳过（避免重复显示）
      if (this.tempUserRealId && msg.id === this.tempUserRealId) {
        this.log('跳过已有的用户消息:', msg.id);

        // 仍然更新最后消息ID
        if (msg.id > latestMessageId) {
          latestMessageId = msg.id;
        }
        continue;
      }

      // 添加通用去重检查
      // 检查消息ID是否已存在于当前消息列表中
      const messageExists = this.getData('messages').some(existingMsg =>
        existingMsg.id === msg.id || existingMsg.dbMessageId === msg.id
      );

      if (messageExists) {
        this.log('跳过已存在的消息ID:', msg.id);
        // 仍然更新最后消息ID
        if (msg.id > latestMessageId) {
          latestMessageId = msg.id;
        }
        continue;
      }

      // 检查元数据中的clientMessageId是否匹配
      if (msg.meta_info && msg.meta_info.clientMessageId &&
          msg.meta_info.clientMessageId === this.getData('tempUserMessageId')) {
        this.log('找到匹配的临时消息服务器版本:', msg.id);

        // 保存真实ID用于后续跳过
        this.setData({
          tempUserRealId: msg.id
        });
        this.tempUserRealId = msg.id;

        // 仅更新临时消息，不添加到新消息列表
        if (processedMsg) {
          processedMsg.isTemporary = false; // 标记为非临时
        }

        // 用服务器版本更新临时消息
        const updatedMessage = this.page.updateMessageById(this.getData('tempUserMessageId'), {
          id: msg.id,
          isTemporary: false,
          dbMessageId: msg.id,
          meta_info: msg.meta_info
        });

        // 确保更新后的消息同步到缓存
        if (updatedMessage) {
          this.log('用户消息ID已更新并保存到缓存:', updatedMessage.id);
          this.page.cacheManager.saveChatMessageToCache(updatedMessage);
        }

        // 更新最后消息ID
        if (msg.id > latestMessageId) {
          latestMessageId = msg.id;
        }
        continue;
      }

      if (processedMsg) {
        processedMessages.push(processedMsg);

        // 更新最后收到的消息ID
        if (msg.id && typeof msg.id === 'number' && msg.id > latestMessageId) {
          latestMessageId = msg.id;
        }
      }
    }

    if (processedMessages.length > 0) {
      // 只添加不是临时消息匹配的新消息（通常是助手回复）
      let updatedMessages = [...this.getData('messages')];

      // 如果有助手消息，添加到消息列表并停止轮询
      const assistantMessages = processedMessages.filter(m => m.role === 'assistant');
      if (assistantMessages.length > 0) {
        // 添加新的助手消息
        updatedMessages.push(...assistantMessages);

        // 找到回复消息，停止轮询
        clearInterval(this.pollIntervalId);
        this.setData({
          pollIntervalId: null,
          isLoading: false,
          messages: updatedMessages,
          lastMessageId: latestMessageId,
          tempUserMessageId: null,
          tempUserRealId: null,
          lastMessageSent: null
        });
        this.pollIntervalId = null;
        this.tempUserMessageId = null;
        this.tempUserRealId = null;
        this.lastMessageSent = null;

        // 保存这些消息到缓存
        this.page.cacheManager.saveChatMessagesToCache(assistantMessages);

        // 滚动到底部
        this.page.scrollToBottom();
        return;
      }

      // 如果还没有助手回复，但有其他新消息，更新显示并继续轮询
      if (processedMessages.length > 0) {
        // 添加非助手消息
        const otherMessages = processedMessages.filter(m => m.role !== 'assistant');
        if (otherMessages.length > 0) {
          updatedMessages.push(...otherMessages);
          this.setData({
            messages: updatedMessages,
            lastMessageId: latestMessageId
          });
        } else {
          // 只更新最后消息ID
          this.setData({ lastMessageId: latestMessageId });
        }
      }
    } else if (latestMessageId > this.getData('lastMessageId')) {
      // 虽然没有新消息添加，但最后消息ID有更新
      this.setData({ lastMessageId: latestMessageId });
    }
  }

  /**
   * 处理轮询错误
   * @param {Error} error - 错误对象
   * @param {number} pollIntervalId - 轮询间隔ID
   */
  handlePollingError(error, pollIntervalId) {
    this.error('轮询出错:', error);
    if (error.statusCode === 404) {
      // 会话不存在，停止轮询
      clearInterval(pollIntervalId);
      this.setData({
        isLoading: false,
        pollIntervalId: null
      });
      this.pollIntervalId = null;
      this.showToast('会话已失效，请重新连接');
    }
  }

  /**
   * 添加机器人消息
   * @param {string} text - 消息文本内容
   * @param {string} [type='text'] - 消息类型
   * @return {Object} 添加的消息对象
   */
  addBotMessage(text, type = 'text') {
    if (!text || !text.trim()) {
      this.log('尝试添加空消息，已忽略');
      return null;
    }

    // 使用通用的添加消息函数
    const result = this.page.addMessage(text, 'assistant', type);
    return result.message;
  }

  /**
   * 处理食物分析消息的确认
   * @param {number} messageId - 消息ID
   * @param {Object} updatedFoodData - 更新的食物数据
   */
  handleFoodAnalysisConfirmed(messageId, updatedFoodData) {
    const messages = this.getData('messages');
    const messageIndex = messages.findIndex(msg => msg.id === messageId);

    if (messageIndex !== -1) {
      const message = messages[messageIndex];
      // 更新meta_info
      message.meta_info = {
        ...message.meta_info,
        ...updatedFoodData,
        is_completed: true
      };

      // 重新处理消息
      const updatedMessage = this.processMessage(message);

      // 更新消息列表
      messages[messageIndex] = updatedMessage;
      this.setData({ messages });

      // 更新缓存
      this.page.app.updateChatMessageInCache(messageId, updatedMessage); // 使用app全局方法更新缓存
    }
  }

  /**
   * 更新训练参数
   * @param {string|number} messageId - 消息ID
   * @param {Object} updateData - 要更新的数据
   * @param {string} selectedOptionText - 用户选择的选项文本
   */
  async updateTrainingParam(messageId, updateData, selectedOptionText) {
    this.log('更新训练参数:', { messageId, updateData, selectedOptionText });

    // 显示加载状态
    this.showLoading('正在更新...');

    try {
      // 查找消息
      const messages = this.getData('messages');
      const messageIndex = messages.findIndex(msg => msg.id === messageId);

      if (messageIndex === -1) {
        throw new Error('找不到消息: ' + messageId);
      }

      const message = messages[messageIndex];

      // 确保meta_info存在
      if (!message.meta_info) {
        message.meta_info = {};
      }

      // 确保training_params存在
      if (!message.meta_info.training_params) {
        message.meta_info.training_params = {};
      }

      // 记录当前的asking_param值，用于日志
      const askingParam = message.meta_info.asking_param;
      this.log('当前asking_param:', askingParam);

      // 检查是否是确认操作
      const isConfirmation = message.paramConfirmed === true;
      this.log('是否是确认操作:', isConfirmation);

      // 如果不是确认操作，只更新UI状态，不发送到服务器
      if (!isConfirmation) {
        this.hideLoading();
        return;
      }

      // 更新meta_info
      Object.keys(updateData).forEach(key => {
        if (key.includes('.')) {
          // 处理嵌套属性，如 'training_params.scenario'
          const [parent, child] = key.split('.');
          if (!message.meta_info[parent]) {
            message.meta_info[parent] = {};
          }
          message.meta_info[parent][child] = updateData[key];
        } else {
          message.meta_info[key] = updateData[key];
        }
      });

      // 确保paramConfirmed状态也被保存到meta_info中
      message.meta_info.paramConfirmed = isConfirmation;

      // 记录更新后的meta_info状态
      this.log('更新后的meta_info:', {
        'collecting_training_params': message.meta_info.collecting_training_params,
        'training_params': message.meta_info.training_params,
        'paramConfirmed': message.meta_info.paramConfirmed
      });

      // 重新处理消息
      const updatedMessage = await this.processMessage(message);

      // 更新消息列表
      messages[messageIndex] = updatedMessage;
      this.setData({ messages });

      // 更新数据库中的消息
      let dbUpdateSuccess = false;
      let updatedMessageData = null;

      if (message.dbMessageId || message.id) {
        const dbId = message.dbMessageId || message.id;

        if (!dbId || isNaN(parseInt(dbId))) {
          this.error('无效的数据库消息ID:', dbId);
          this.showToast('更新失败: 无效的消息ID');
        } else {
          try {
            const chatApi = require('../../../api/chatAI');
            const numericDbId = parseInt(dbId);

            this.log('准备更新数据库消息:', {
              'dbId': numericDbId,
              'content': message.content,
              'meta_info键': Object.keys(message.meta_info)
            });

            // 使用chatApi.updateMessage并获取返回的updatedMessageData
            updatedMessageData = await chatApi.updateMessage(
              numericDbId,
              message.content,
              message.role,
              message.meta_info
            );

            if (updatedMessageData && updatedMessageData.id) {
              dbUpdateSuccess = true;

              // 验证返回的数据中是否包含我们期望的更新
              const hasTrainingParams = updatedMessageData.meta_info &&
                                       updatedMessageData.meta_info.training_params;
              const paramConfirmed = hasTrainingParams &&
                                    updatedMessageData.meta_info.training_params.param_confirmed === true;

              this.log('数据库消息更新成功:', {
                'dbId': numericDbId,
                'returnedId': updatedMessageData.id,
                'hasTrainingParams': hasTrainingParams,
                'paramConfirmed': paramConfirmed,
                'collecting_training_params': updatedMessageData.meta_info?.collecting_training_params
              });

              // 如果返回的数据与我们期望的不符，记录警告
              if (!hasTrainingParams || !paramConfirmed) {
                this.log('警告: 数据库更新可能不完整', {
                  'meta_info': updatedMessageData.meta_info
                });
              }

              // 更新本地消息的dbMessageId，确保后续更新能找到正确的ID
              if (!message.dbMessageId && updatedMessageData.id) {
                message.dbMessageId = updatedMessageData.id;
                updatedMessage.dbMessageId = updatedMessageData.id;
                this.log(`更新本地消息的dbMessageId为 ${updatedMessageData.id}`);
              }
            } else {
              this.error('数据库消息更新返回无效数据:', updatedMessageData);
            }
          } catch (error) {
            this.error('更新数据库消息失败:', error);
            // 显示错误提示，但继续执行流程
            this.showToast('数据同步失败，请稍后重试');
          }
        }
      } else {
        this.error('无法更新数据库: 消息没有有效的ID');
      }

      // 更新缓存 - 使用从数据库返回的数据或本地更新的数据
      const finalUpdatedMessage = updatedMessageData || updatedMessage;
      this.page.app.updateChatMessageInCache(messageId, finalUpdatedMessage);

      // 隐藏加载状态
      this.hideLoading();

      // 根据数据库更新结果提供反馈
      if (dbUpdateSuccess) {
        this.log('参数选择已成功保存到数据库');
      } else {
        this.log('参数选择可能未保存到数据库，但UI已更新');
      }

      // 发送用户选择的选项作为新消息
      this.sendMessage(selectedOptionText);

    } catch (error) {
      this.error('更新训练参数失败:', error);
      this.hideLoading();
      this.showToast('更新失败: ' + error.message);
    }
  }

  /**
   * 清除轮询
   */
  clearPolling() {
    if (this.pollIntervalId) {
      clearInterval(this.pollIntervalId);
      this.pollIntervalId = null;
      this.setData({ pollIntervalId: null });
    }
  }

  /**
   * 处理workout消息
   * @param {Object} message - 消息对象
   * @returns {Promise<Object>} 处理后的消息对象
   */
  async processWorkoutMessage(message) {
    if (!message || !message.workoutId) {
      this.error('处理workout消息失败: 无效的消息或workoutId');
      return message;
    }

    try {
      this.log('开始处理workout消息:', message.workoutId);

      // 导入workout API
      const workoutApi = require('../../../api/workout');

      // 获取workout详情
      const workoutDetail = await workoutApi.getWorkoutDetail(message.workoutId);

      if (!workoutDetail) {
        this.error('获取workout详情失败:', message.workoutId);
        return message;
      }



      // 导入图片处理工具
      const imageHelper = require('../../../utils/image-helper');

      // 处理workout本身的图片URL
      if (workoutDetail.image_url) {
        workoutDetail.image_url = imageHelper.getFullImageUrl(workoutDetail.image_url);
      }

      // 处理exercises的图片和视频URL
      if (workoutDetail.exercises && workoutDetail.exercises.length > 0) {
        // 处理每个练习的图片和视频URL
        console.log('处理workout中的exercises数据');
        workoutDetail.exercises.forEach(exercise => {
          if (exercise.exercise_image) {
            exercise.image_url = imageHelper.getFullImageUrl(exercise.exercise_image);
            console.log('处理后的图片URL:', exercise.image_url);
          }
          if (exercise.video_url) {
            exercise.video_url = imageHelper.getFullImageUrl(exercise.video_file);
          }
        });
      }
      this.log('获取到workout详情:', workoutDetail);
      // 更新消息对象
      const updatedMessage = {
        ...message,
        workout: workoutDetail,
        showWorkout: true
      };

      return updatedMessage;
    } catch (error) {
      this.error('处理workout消息出错:', error);
      return message;
    }
  }
}

module.exports = MessageManager;
