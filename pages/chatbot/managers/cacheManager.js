/**
 * 缓存管理器
 * 处理消息的缓存和加载
 */
const BaseManager = require('./baseManager');

class CacheManager extends BaseManager {
  constructor(page) {
    super(page);
    this.maxCachedMessages = 20;
  }

  /**
   * 从缓存加载聊天历史记录
   * @returns {Promise<Array>} 加载的消息数组
   */
  async loadChatHistoryFromCache() {
    try {
      this.setData({
        loadingHistory: true
      });

      // 从app全局数据或本地存储获取缓存的聊天记录
      const cachedMessages = this.page.app.getCachedChatMessages();
      this.log('从缓存加载聊天记录，条数:', cachedMessages?.length || 0);

      if (!cachedMessages || cachedMessages.length === 0) {
        this.log('缓存中无聊天记录');
        this.setData({
          loadingHistory: false
        });
        return [];
      }

      // 处理缓存的消息
      const processedMessages = [];
      let lastMessageId = 0;

      // 使用基本处理先显示消息
      const basicProcessedMessages = [];

      // 先使用同步方式处理消息，以便快速显示
      for (const msg of cachedMessages) {
        try {
          // 预处理消息，确保meta_info和complete_training_plan字段存在
          const safeMsg = { ...msg };

          // 确保meta_info存在
          if (!safeMsg.meta_info) {
            safeMsg.meta_info = {};
          }

          // 如果有complete_training_plan字段，确保exercises字段存在
          if (safeMsg.meta_info.complete_training_plan) {
            const plan = safeMsg.meta_info.complete_training_plan;

            // 如果没有exercises字段，但有workouts字段，从workouts中提取exercises
            if (!plan.exercises && plan.workouts &&
                Array.isArray(plan.workouts) && plan.workouts.length > 0) {
              const workout = plan.workouts[0];
              if (workout.exercises && Array.isArray(workout.exercises)) {
                plan.exercises = [...workout.exercises];
                this.log('从缓存消息的workouts中提取exercises字段，数量:', plan.exercises.length);
              } else {
                plan.exercises = [];
                this.log('缓存消息中创建空的exercises数组');
              }
            } else if (!plan.exercises) {
              plan.exercises = [];
              this.log('缓存消息中创建空的exercises数组');
            }
          }

          // 使用messageProcessor直接处理，避免异步
          const messageProcessor = require('../../../utils/messageProcessor');
          const basicMessage = messageProcessor.processMessage(safeMsg, { processUI: true });

          if (basicMessage) {
            basicProcessedMessages.push(basicMessage);
            // 跟踪最大消息 ID 用于记录
            if (safeMsg.id && typeof safeMsg.id === 'number' && safeMsg.id > lastMessageId) {
              lastMessageId = safeMsg.id;
            }
          }
        } catch (processError) {
          this.error('基本处理缓存消息时出错，跳过此消息:', processError, '消息内容:', JSON.stringify(msg).substring(0, 200) + '...');
          // 继续处理下一条消息，不中断整个循环
          continue;
        }
      }

      // 先显示基本处理的消息
      if (basicProcessedMessages.length > 0) {
        this.log('基本处理后的缓存消息:', basicProcessedMessages.length);

        this.setData({
          messages: basicProcessedMessages,
          lastMessageId: lastMessageId || 0
        });

        // 初始加载时滚动到底部，但下拉刷新时不滚动
        if (this.page.data.isFirstLoad && !this.page.data.isRefreshing) {
          this.page.uiManager?.scrollToBottom();
        }
      }

      // 然后异步处理每条消息，获取完整的处理结果
      for (const msg of cachedMessages) {
        try {
          // 使用messageManager的异步processMessage方法
          if (this.page.messageManager) {
            const message = await this.page.messageManager.processMessage(msg);
            if (message) {
              processedMessages.push(message);
            }
          }
        } catch (processError) {
          this.error('异步处理缓存消息时出错，跳过此消息:', processError);
          // 继续处理下一条消息，不中断整个循环
          continue;
        }
      }

      // 更新消息列表，使用完整处理的结果
      if (processedMessages.length > 0) {
        this.log('完整处理后的缓存消息:', processedMessages.length);

        this.setData({
          messages: processedMessages,
          lastMessageId: lastMessageId || 0
        });

        // 初始加载时滚动到底部，但下拉刷新时不滚动
        if (this.page.data.isFirstLoad && !this.page.data.isRefreshing) {
          this.page.uiManager?.scrollToBottom();
        }
      }

      this.log('聊天记录加载完成');
      return processedMessages;
    } catch (err) {
      this.error('加载聊天记录失败:', err);
      this.showToast('加载历史消息失败');
      throw err;
    } finally {
      this.setData({
        loadingHistory: false
      });
    }
  }

  /**
   * 保存新消息到缓存
   * @param {Object} message - 要保存的消息对象
   */
  saveChatMessageToCache(message) {
    if (!message) return;

    // 使用app全局方法保存消息
    this.page.app.saveChatMessageToCache(message, this.maxCachedMessages);
  }

  /**
   * 将多个消息保存到缓存
   * @param {Array} messages - 要保存的消息数组
   */
  saveChatMessagesToCache(messages) {
    if (!messages || !Array.isArray(messages) || messages.length === 0) return;

    messages.forEach(message => {
      this.saveChatMessageToCache(message);
    });
  }

  /**
   * 设置最大缓存消息数量
   * @param {number} maxCount - 最大缓存消息数量
   */
  setMaxCachedMessages(maxCount) {
    if (typeof maxCount === 'number' && maxCount > 0) {
      this.maxCachedMessages = maxCount;
      this.setData({ maxCachedMessages: maxCount });
    }
  }
}

module.exports = CacheManager;
