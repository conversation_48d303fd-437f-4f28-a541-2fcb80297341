/**
 * 会话管理器
 * 处理聊天会话的创建、连接和管理
 */
const BaseManager = require('./baseManager');
const chatApi = require('../../../api/chatAI');

class SessionManager extends BaseManager {
  constructor(page) {
    super(page);
    this.reconnectAttempts = 0;
    this.maxReconnectAttempts = 5;
  }

  /**
   * 初始化或重用聊天会话
   * @returns {Promise<void>}
   */
  async initChatSession() {
    this.showLoading('初始化会话...', true);
    try {
      const cachedSessionId = wx.getStorageSync('lastSessionId');
      this.log("Cached session ID:", cachedSessionId);

      if (cachedSessionId) {
        // 尝试检查会话是否存在
        let exists = false;
        try {
          exists = await chatApi.checkSessionExists(cachedSessionId);
        } catch (checkError) {
          this.error("Error checking session:", checkError);
          // 会话检查出错，直接创建新会话
          exists = false;
        }

        if (exists) {
          this.log(`Session ${cachedSessionId} exists. Reusing.`);
          this.setData({ currentConversationId: cachedSessionId });
          await this.startChatStream(); // Start chat stream with existing ID
        } else {
          this.log(`Session ${cachedSessionId} not found or invalid. Creating new one.`);
          this.createNewSession(); // Create new if check fails
        }
      } else {
        this.log("No cached session ID found. Using new one.");
        wx.setStorageSync('lastSessionId', this.getData('currentConversationId')); // Store the ID
        await this.startChatStream(); // Start with the new ID created in onLoad
      }
    } catch (error) {
      this.error("Error initializing chat session:", error);
      this.showToast('初始化会话失败');
    } finally {
      this.hideLoading();
    }
  }

  /**
   * 创建新会话
   */
  createNewSession() {
    const newSessionId = `conv_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
    this.log("Creating new session:", newSessionId);

    wx.setStorageSync('lastSessionId', newSessionId); // Store the new ID

    this.setData({
      currentConversationId: newSessionId,
      isLoading: false, // Reset loading state
      currentBotMessageId: null, // Reset bot message tracking
    });

    // 注意：我们不再清空消息，而是保持缓存的消息显示
    // 这样用户在创建新会话时依然能看到历史消息
  }

  /**
   * 初始化聊天流
   * @returns {Promise<void>}
   */
  async startChatStream() {
    const currentConversationId = this.getData('currentConversationId');
    if (!currentConversationId) {
      this.error("Chat: Cannot start stream without a conversation ID.");
      this.showToast('无法连接聊天服务');
      return;
    }

    this.setData({ isLoading: true }); // Show loading indicator during connection attempt

    try {
      // Initialize chat stream
      await chatApi.initChatStream(currentConversationId, {
        onOpen: this.handleChatReady.bind(this),
        onError: this.handleChatError.bind(this)
      });
    } catch (error) {
      this.error("Chat: Failed to start chat stream:", error);
      this.setData({ isLoading: false });
      this.showToast('连接聊天服务失败');
    }
  }

  /**
   * 处理聊天准备就绪
   * @param {Object} event - 事件对象
   */
  async handleChatReady(event) {
    this.log('Chat: Stream ready.', event);
    this.setData({ isLoading: false });

    const serverMessage = event.data?.server_message || '连接成功';
    this.showToast(serverMessage, 'success', 1500);

    // 不再加载历史记录，因为已经从缓存加载

    // If there's a pending message, send it now
    const pendingMessage = this.getData('pendingMessage');
    if (pendingMessage) {
      setTimeout(() => {
        this.setData({ pendingMessage: null });
        this.page.messageManager.sendMessage(pendingMessage);
      }, 500);
    }
  }

  /**
   * 处理聊天错误
   * @param {Error} err - 错误对象
   */
  handleChatError(err) {
    this.error('Chat: Stream initialization error:', err);
    this.showToast(`连接异常: ${err.errMsg || '请稍后重试'}`);
    this.setData({ isLoading: false });
    this.hideLoading();
  }

  /**
   * 从服务器更新缓存的聊天记录
   * 当需要强制刷新缓存内容时调用
   * @returns {Promise<void>}
   */
  async updateCachedChatMessages() {
    try {
      this.showLoading('更新聊天记录...', true);

      // 直接调用app中的刷新方法
      await this.page.app.refreshChatMessagesFromServer();

      // 重新从缓存加载消息到页面
      this.page.cacheManager.loadChatHistoryFromCache();

      this.hideLoading();
      this.showToast('聊天记录已更新', 'success');
    } catch (error) {
      this.error('更新聊天记录失败:', error);
      this.hideLoading();
      this.showToast('更新聊天记录失败');
    }
  }
}

module.exports = SessionManager;
