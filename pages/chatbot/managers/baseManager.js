/**
 * 基础管理器类
 * 为所有管理器提供通用功能
 */
class BaseManager {
  /**
   * 构造函数
   * @param {Object} page - 页面实例
   */
  constructor(page) {
    this.page = page;
  }

  /**
   * 获取页面数据
   * @param {string} key - 数据键名
   * @returns {any} 数据值
   */
  getData(key) {
    return this.page.data[key];
  }

  /**
   * 设置页面数据
   * @param {Object} data - 要设置的数据对象
   * @param {Function} [callback] - 设置完成后的回调函数
   */
  setData(data, callback) {
    this.page.setData(data, callback);
  }

  /**
   * 显示加载提示
   * @param {string} title - 提示文本
   * @param {boolean} [mask=true] - 是否显示遮罩
   */
  showLoading(title, mask = true) {
    wx.showLoading({ title, mask });
  }

  /**
   * 隐藏加载提示
   */
  hideLoading() {
    wx.hideLoading();
  }

  /**
   * 显示提示框
   * @param {string} title - 提示文本
   * @param {string} [icon='none'] - 图标类型
   * @param {number} [duration=2000] - 显示时长
   */
  showToast(title, icon = 'none', duration = 2000) {
    wx.showToast({ title, icon, duration });
  }

  /**
   * 打印日志
   * @param {string} message - 日志消息
   * @param {any} [data] - 附加数据
   */
  log(message, data) {
    if (data) {
      console.log(`[${this.constructor.name}] ${message}:`, data);
    } else {
      console.log(`[${this.constructor.name}] ${message}`);
    }
  }

  /**
   * 打印错误
   * @param {string} message - 错误消息
   * @param {Error|any} [error] - 错误对象
   */
  error(message, error) {
    if (error) {
      console.error(`[${this.constructor.name}] ${message}:`, error);
    } else {
      console.error(`[${this.constructor.name}] ${message}`);
    }
  }
}

module.exports = BaseManager;
