/**
 * UI管理器
 * 处理UI相关操作，如滚动、输入和录音
 */
const BaseManager = require('./baseManager');

class UIManager extends BaseManager {
  constructor(page) {
    super(page);
    this.initRecordingManager();
  }

  /**
   * 初始化录音管理器
   */
  initRecordingManager() {
    const recordingManager = wx.getRecorderManager();

    // 录音管理器事件处理
    recordingManager.onStart(() => {
      this.log('录音开始');
      this.setData({ isRecording: true });
    });

    recordingManager.onStop((res) => {
      this.log('录音结束', res);
      this.setData({ isRecording: false });

      // 如果有录音文件，可以考虑上传到语音识别服务
      if (res.tempFilePath) {
        // 显示加载状态
        this.showLoading('正在识别...', true);

        // 这里可以添加语音识别逻辑，将语音转为文本
        // 示例: 直接显示"语音消息"作为占位内容，实际项目中应接入语音识别服务
        setTimeout(() => {
          this.hideLoading();
          this.page.messageManager.sendMessage("我刚刚发送了一条语音消息");
        }, 1000);
      }
    });

    recordingManager.onError((error) => {
      this.error('录音失败:', error);
      this.showToast('录音失败');
      this.setData({ isRecording: false });
    });

    this.setData({ recordingManager });
  }

  /**
   * 滚动到底部
   */
  scrollToBottom() {
    // 如果当前正在刷新状态，不执行滚动
    if (this.getData('isRefreshing')) {
      console.log('正在刷新状态，不执行滚动到底部');
      return;
    }

    setTimeout(() => {
      const query = wx.createSelectorQuery().in(this.page);
      query.select('.chat-list').boundingClientRect();
      query.exec((res) => {
        if (res && res[0]) {
          wx.pageScrollTo({
            scrollTop: res[0].height,
            duration: 300
          });
        }
      });
    }, 100);
  }

  /**
   * 处理输入变化
   * @param {Object} e - 事件对象
   */
  onInputChange(e) {
    this.setData({
      inputValue: e.detail.value
    });
  }

  /**
   * 切换输入模式（文字/语音）
   */
  toggleInputMode() {
    this.setData({
      isVoiceMode: !this.getData('isVoiceMode')
    });
  }

  /**
   * 开始录音
   * @param {Object} e - 事件对象
   */
  startRecording(e) {
    if (!this.getData('isVoiceMode')) return;

    this.log('开始录音');
    const options = {
      duration: 60000, // 最长录音时间，单位ms
      sampleRate: 16000, // 采样率
      numberOfChannels: 1, // 录音通道数
      encodeBitRate: 64000, // 编码码率
      format: 'mp3' // 音频格式
    };

    // 开始录音
    this.getData('recordingManager').start(options);

    // 更新UI状态
    this.setData({ isRecording: true });

    // 保存起始Y坐标用于检测上滑取消
    if (e.touches && e.touches[0]) {
      this.startRecordingY = e.touches[0].clientY;
    }
  }

  /**
   * 结束录音
   * @param {Object} e - 事件对象
   */
  endRecording(e) {
    if (!this.getData('isVoiceMode') || !this.getData('isRecording')) return;

    this.log('结束录音');
    this.getData('recordingManager').stop();
  }

  /**
   * 取消录音
   * @param {Object} e - 事件对象
   */
  cancelRecording(e) {
    if (!this.getData('isVoiceMode') || !this.getData('isRecording')) return;

    // 检测是否为上滑取消
    const touch = e.touches[0];
    const startY = this.startRecordingY || touch.clientY;
    const currentY = touch.clientY;

    // 如果上滑超过一定距离，取消录音
    if (startY - currentY > 100) {
      this.log('取消录音');
      this.getData('recordingManager').stop();
      this.showToast('已取消', 'none');
      this.setData({ isRecording: false });
    }
  }

  /**
   * 处理发送按钮点击事件
   */
  onSendButtonTap() {
    // 调用消息管理器的发送消息方法
    this.page.messageManager.sendMessage();
  }

  /**
   * 处理饮食建议功能
   */
  handleDietAdvice() {
    this.showToast('饮食建议功能开发中', 'none');
    // 向聊天发送一条系统消息，提示用户该功能正在开发中
    this.page.messageManager.sendMessage("请给我一些健康饮食的建议");
  }

  /**
   * 处理运动计划功能
   */
  handleExercisePlan() {
    this.showToast('运动计划功能开发中', 'none');
    // 向聊天发送一条系统消息，提示用户该功能正在开发中
    this.page.messageManager.sendMessage("请推荐一个适合我的运动计划");
  }

  /**
   * 处理训练参数选择
   * @param {Object} e - 事件对象
   */
  handleTrainingParamSelect(e) {
    const { messageId, param, value } = e.currentTarget.dataset;

    if (!messageId || !param || !value) {
      this.error('训练参数选择缺少必要参数:', { messageId, param, value });
      this.showToast('参数选择失败');
      return;
    }

    this.log('用户选择了训练参数:', { messageId, param, value });

    // 获取中文选项名称
    let selectedOptionText = '';
    switch (param) {
      case 'scenario':
        selectedOptionText = value === 'gym' ? '健身房' : '居家';
        break;
      case 'plan_type':
        selectedOptionText = value === 'daily' ? '单日' : '周期';
        break;
      case 'body_part':
        const bodyPartMap = {
          'chest': '胸部',
          'back': '背部',
          'legs': '腿部',
          'shoulders': '肩部',
          'glutes': '臀部',
          'arms': '手臂',
          'abs': '腹部'
        };
        selectedOptionText = bodyPartMap[value] || value;
        break;
      default:
        selectedOptionText = value;
    }

    // 获取当前消息列表
    const messages = this.getData('messages');
    const messageIndex = messages.findIndex(msg => msg.id === messageId);

    if (messageIndex === -1) {
      this.error('找不到消息:', messageId);
      this.showToast('选择失败');
      return;
    }

    // 更新消息，标记选中的选项
    const updatedMessages = [...messages];
    updatedMessages[messageIndex] = {
      ...updatedMessages[messageIndex],
      selectedParam: value,
      selectedParamText: selectedOptionText,
      selectedParamType: param,
      selectedParamId: value // 保存选项ID
    };

    // 确保meta_info存在
    if (!updatedMessages[messageIndex].meta_info) {
      updatedMessages[messageIndex].meta_info = {};
    }

    // 将选项ID更新到meta_info中
    if (!updatedMessages[messageIndex].meta_info.training_params) {
      updatedMessages[messageIndex].meta_info.training_params = {};
    }

    // 临时保存选择，但不发送到服务器
    updatedMessages[messageIndex].meta_info.training_params[`selected_${param}`] = value;

    // 更新UI
    this.setData({ messages: updatedMessages });
    this.log('已更新选项ID到message中:', { param, value });
  }

  /**
   * 处理训练参数确认
   * @param {Object} e - 事件对象
   */
  handleTrainingParamConfirm(e) {
    const { messageId } = e.currentTarget.dataset;

    if (!messageId) {
      this.error('训练参数确认缺少必要参数:', { messageId });
      this.showToast('确认失败');
      return;
    }

    // 获取当前消息列表
    const messages = this.getData('messages');
    const messageIndex = messages.findIndex(msg => msg.id === messageId);

    if (messageIndex === -1) {
      this.error('找不到消息:', messageId);
      this.showToast('确认失败');
      return;
    }

    const message = messages[messageIndex];

    if (!message.selectedParam || !message.selectedParamType) {
      this.error('没有选择参数:', message);
      this.showToast('请先选择一个选项');
      return;
    }

    this.log('用户确认了训练参数:', {
      messageId,
      param: message.selectedParamType,
      value: message.selectedParam,
      text: message.selectedParamText
    });

    // 标记为已确认
    const updatedMessages = [...messages];
    updatedMessages[messageIndex] = {
      ...updatedMessages[messageIndex],
      paramConfirmed: true
    };

    // 更新UI
    this.setData({ messages: updatedMessages });

    // 确保meta_info和training_params存在
    if (!message.meta_info) {
      message.meta_info = {};
    }
    if (!message.meta_info.training_params) {
      message.meta_info.training_params = {};
    }

    // 构建更新数据 - 包含所有必要的参数
    const updateData = {
      // 添加选定的参数到training_params
      [`training_params.${message.selectedParamType}`]: message.selectedParam,
      // 添加已确认状态到training_params
      'training_params.param_confirmed': true,
      // 添加选定的参数类型到training_params，用于后续参考
      'training_params.last_confirmed_param': message.selectedParamType,
      // 关闭参数收集状态
      collecting_training_params: false
    };

    // 如果有临时选择的参数，确保它们也被包含在更新中
    if (message.meta_info.training_params) {
      Object.keys(message.meta_info.training_params).forEach(key => {
        if (key.startsWith('selected_') && !updateData[`training_params.${key}`]) {
          updateData[`training_params.${key}`] = message.meta_info.training_params[key];
        }
      });
    }

    this.log('发送到数据库的更新数据:', updateData);

    // 调用消息管理器更新消息
    this.page.messageManager.updateTrainingParam(messageId, updateData, message.selectedParamText);
  }
}

module.exports = UIManager;
