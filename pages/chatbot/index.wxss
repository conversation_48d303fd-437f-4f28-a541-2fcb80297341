/* 容器样式 */
.chat-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f7f7f7;
  position: fixed;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  width: 100vw;
  overflow: hidden;
}

/* 聊天区域 */
.chat-list {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
  -webkit-overflow-scrolling: touch;
  padding-bottom: 240rpx; /* 添加底部内边距，防止内容被输入框遮挡 */
}

/* 空消息提示样式 */
.empty-chat-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 60rpx 40rpx;
  margin-top: 40rpx;
}

.empty-chat-bot-avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.empty-chat-message {
  background-color: #fff;
  padding: 30rpx;
  border-radius: 24rpx;
  max-width: 80%;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.empty-chat-message text {
  font-size: 30rpx;
  color: #333;
  line-height: 1.6;
}

/* 加载更多提示样式 */
.loading-more-indicator {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 30rpx 0;
  opacity: 0.7;
}

.loading-dots {
  display: flex;
  justify-content: center;
  margin-bottom: 10rpx;
}

.loading-dots .dot {
  width: 16rpx;
  height: 16rpx;
  background-color: #07c160;
  border-radius: 50%;
  margin: 0 6rpx;
  animation: bounce 1s infinite;
}

.loading-more-indicator text {
  font-size: 24rpx;
  color: #666;
}

/* 底部占位区域 */
.bottom-space {
  height: 140rpx; /* 确保底部有足够的空间 */
  width: 100%;
}

/* 功能标签栏 */
.feature-tags {
  position: fixed;
  bottom: 160rpx;
  left: 0;
  right: 0;
  display: flex;
  justify-content: space-around;
  padding: 16rpx 24rpx;
  z-index: 99;
}

.tag-item {
  background-color: #fff;
  padding: 12rpx 32rpx;
  border-radius: 32rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
}

.tag-item:active {
  transform: scale(0.95);
  background-color: #f9f9f9;
}

.tag-item text {
  font-size: 26rpx;
  color: #07c160;
  font-weight: 500;
}

/* 消息样式 */
.message-wrapper {
  display: flex;
  align-items: flex-start;
  margin-bottom: 24rpx;
  animation: fadeIn 0.3s ease;
  padding: 0 24rpx;
}

.message-wrapper.user {
  flex-direction: row-reverse;
}

.message-wrapper.user .message-content {
  align-items: flex-end;
}

.avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  flex-shrink: 0;
  background-color: #fff;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.avatar image {
  width: 100%;
  height: 100%;
}

.message-content {
  max-width: 70%;
  margin: 0 16rpx;
  display: flex;
  flex-direction: column;
}

.message-text {
  padding: 20rpx 24rpx;
  border-radius: 24rpx;
  font-size: 28rpx;
  line-height: 1.5;
  word-break: break-word;
  animation: slideIn 0.3s ease;
}

.message-text strong {
  font-weight: bold;
}

.message-text h1 {
  font-size: 40rpx;
  font-weight: bold;
  margin: 20rpx 0;
}

.message-text h2 {
  font-size: 36rpx;
  font-weight: bold;
  margin: 16rpx 0;
}

.message-text h3 {
  font-size: 32rpx;
  font-weight: bold;
  margin: 12rpx 0;
}

.message-text .list-item {
  margin: 8rpx 0;
  padding-left: 16rpx;
}

.user .message-text {
  background-color: #07c160;
  color: #fff;
  border-top-left-radius: 24rpx;
  border-top-right-radius: 4rpx;
  border-bottom-left-radius: 24rpx;
  border-bottom-right-radius: 24rpx;
}

.bot .message-text {
  background-color: #fff;
  color: #333;
  border-top-left-radius: 4rpx;
  border-top-right-radius: 24rpx;
  border-bottom-left-radius: 24rpx;
  border-bottom-right-radius: 24rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

/* 图片消息样式 */
.message-image {
  max-width: 400rpx;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

/* 健康数据卡片样式 */
.health-card {
  background-color: #ffffff;
  border-radius: 12rpx;
  padding: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.card-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #333333;
  margin-bottom: 12rpx;
}

.card-content {
  font-size: 26rpx;
  color: #666666;
  line-height: 1.5;
}

/* 图片预览区域 */
.preview-container {
  position: fixed;
  bottom: 120rpx;
  left: 24rpx;
  right: 24rpx;
  background-color: #ffffff;
  padding: 16rpx;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.preview-image {
  width: 200rpx;
  height: 200rpx;
  border-radius: 8rpx;
}

.cancel-btn {
  position: absolute;
  top: -16rpx;
  right: -16rpx;
  width: 40rpx;
  height: 40rpx;
  background-color: rgba(0, 0, 0, 0.5);
  color: #ffffff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
}

/* 输入区域 */
.input-area {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #fff;
  padding: 20rpx 24rpx;
  padding-bottom: calc(20rpx + env(safe-area-inset-bottom));
  box-shadow: 0 -2rpx 8rpx rgba(0, 0, 0, 0.05);
  z-index: 100;
}

.input-area::before {
  content: '';
  position: absolute;
  top: -20rpx;
  left: 0;
  right: 0;
  height: 20rpx;
  background: linear-gradient(to bottom, rgba(247, 247, 247, 0), rgba(247, 247, 247, 1));
  pointer-events: none;
}

.input-wrapper {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.voice-btn {
  width: 64rpx;
  height: 64rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.voice-btn image {
  width: 44rpx;
  height: 44rpx;
  opacity: 0.7;
}

.message-input {
  flex: 1;
  height: 72rpx;
  background-color: #f5f5f5;
  border-radius: 36rpx;
  padding: 0 24rpx;
  font-size: 28rpx;
}

.input-placeholder {
  color: #999;
}

.add-btn {
  width: 64rpx;
  height: 64rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.add-btn image {
  width: 44rpx;
  height: 44rpx;
  opacity: 0.7;
}

/* 语音输入区域 */
.voice-input-area {
  flex: 1;
  height: 72rpx;
  background-color: #f5f5f5;
  border-radius: 36rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.voice-input-area text {
  font-size: 28rpx;
  color: #666;
}

.voice-input-area:active {
  background-color: #e0e0e0;
}

/* 打字指示器 */
.typing-indicator {
  opacity: 0;
  transform: translateY(20rpx);
  transition: all 0.3s ease;
  padding: 0 24rpx;
  margin-left: 96rpx;
}

.typing-indicator.show {
  opacity: 1;
  transform: translateY(0);
}

.typing-wrapper {
  display: flex;
  align-items: flex-start;
}

.typing-content {
  background-color: #fff;
  padding: 12rpx 24rpx;
  border-radius: 24rpx;
  border-top-left-radius: 4rpx;
  display: flex;
  align-items: center;
  gap: 8rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.dot {
  width: 12rpx;
  height: 12rpx;
  background-color: #bbb;
  border-radius: 50%;
  animation: bounce 1s infinite;
}

.dot:nth-child(2) { animation-delay: 0.2s; }
.dot:nth-child(3) { animation-delay: 0.4s; }

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(10rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes bounce {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-6rpx); }
}

/* 训练参数选择样式 */
.training-params-selection {
  width: 100%;
  max-width: 600rpx;
  background: #fff;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  padding: 20rpx;
  margin-top: 10rpx;
  margin-bottom: 10rpx;
}

.params-question {
  font-size: 30rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 20rpx;
}

.params-options {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
}

.option-item {
  background-color: #f5f5f5;
  padding: 12rpx 24rpx;
  border-radius: 30rpx;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: relative;
}

.option-item:active {
  background-color: #e0e0e0;
  transform: scale(0.98);
}

.option-item.selected {
  background-color: #07c160;
  color: #fff;
}

.option-item text {
  font-size: 28rpx;
  color: #333;
}

.option-item.selected text {
  color: #fff;
}

.selected-icon {
  font-size: 24rpx;
  color: #fff;
  margin-left: 10rpx;
}

.confirm-button-container {
  display: flex;
  justify-content: flex-end;
  margin-top: 20rpx;
}

.confirm-button {
  background-color: #07c160;
  color: #fff;
  padding: 10rpx 30rpx;
  border-radius: 30rpx;
  font-size: 28rpx;
  transition: all 0.2s ease;
}

.confirm-button:active {
  opacity: 0.8;
  transform: scale(0.98);
}

.confirm-button.confirmed {
  background-color: #999;
  opacity: 0.7;
}

.confirm-button.disabled {
  background-color: #ccc;
  opacity: 0.5;
  pointer-events: none;
}

/* 食物分析卡片样式 */
.food-analysis {
  width: 100%;
  max-width: 600rpx;
  background: #fff;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  transition: all 0.2s ease;
  position: relative;
}

.food-analysis:active {
  transform: scale(0.98);
  opacity: 0.9;
}

/* 已完成状态样式 */
.food-analysis.completed {
  background-color: #f9f9f9;
  transform: none !important;
  opacity: 1 !important;
}

.food-analysis.completed:active {
  transform: none;
  opacity: 1;
}

.completed-badge {
  position: absolute;
  top: 16rpx;
  right: 16rpx;
  background-color: #07c160;
  color: white;
  font-size: 20rpx;
  padding: 4rpx 12rpx;
  border-radius: 20rpx;
  font-weight: 500;
}

.food-summary {
  padding: 20rpx;
}

.food-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 16rpx;
  margin-bottom: 16rpx;
}

.food-text {
  flex: 1;
}

.food-title {
  font-size: 30rpx;
  font-weight: 500;
  color: #333;
}

.thumb-image {
  width: 120rpx;
  height: 120rpx;
  border-radius: 8rpx;
  flex-shrink: 0;
}

/* 新的营养信息样式 */
.nutrition-details {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 20rpx 0;
  padding: 10rpx 0;
  border-top: 1px solid #f0f0f0;
  border-bottom: 1px solid #f0f0f0;
}

.nutrition-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
}

.nutrition-icon {
  width: 40rpx;
  height: 40rpx;
  margin-bottom: 8rpx;
}

.nutrition-value {
  display: flex;
  align-items: baseline;
}

.value {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
}

.unit {
  font-size: 24rpx;
  color: #666;
  margin-left: 4rpx;
}

.tap-hint {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  margin-top: 16rpx;
}

.hint-text {
  font-size: 24rpx;
  color: #07c160;
  font-weight: 500;
}

/* 已完成状态下的提示文本 */
.completed .hint-text {
  color: #999;
}

.arrow-icon {
  width: 32rpx;
  height: 32rpx;
  margin-left: 8rpx;
}

.check-icon {
  width: 32rpx;
  height: 32rpx;
  margin-left: 8rpx;
}

/* Markdown 样式 */
.md-paragraph {
  margin-bottom: 20rpx;
  line-height: 1.6;
}

.md-bold {
  font-weight: bold;
}

.md-italic {
  font-style: italic;
}

.md-code {
  font-family: monospace;
  background-color: rgba(0, 0, 0, 0.05);
  padding: 2rpx 8rpx;
  border-radius: 6rpx;
}

/* 有序列表样式 */
.md-ol-container {
  margin: 16rpx 0;
  padding-left: 20rpx;
}

.md-ol {
  display: flex;
  margin-bottom: 12rpx;
  align-items: flex-start;
}

.md-ol-number {
  min-width: 50rpx;
  margin-right: 8rpx;
  color: inherit;
  font-weight: 500;
}

.md-ol-content {
  flex: 1;
  line-height: 1.5;
}

/* 无序列表样式 */
.md-ul-container {
  margin: 16rpx 0;
  padding-left: 20rpx;
}

.md-li {
  display: flex;
  margin-bottom: 12rpx;
  align-items: flex-start;
}

.md-li-bullet {
  min-width: 30rpx;
  margin-right: 8rpx;
  color: inherit;
}

.md-li-content {
  flex: 1;
  line-height: 1.5;
}

/* 嵌套列表样式 */
.md-li-nested {
  margin-left: 30rpx;
  margin-top: 10rpx;
}

/* 列表项延续内容 */
.md-li-continuation {
  margin-left: 40rpx;
  margin-bottom: 10rpx;
  line-height: 1.5;
}

/* 适配聊天消息中的Markdown样式 */
.bot .message-text .md-paragraph:last-child {
  margin-bottom: 0;
}

.bot .message-text .md-ol-container,
.bot .message-text .md-ul-container {
  margin-top: 10rpx;
  margin-bottom: 10rpx;
}

/* wemark 组件样式覆盖 */
.message-text .wemark_wrapper {
  padding: 0;
  margin: 0;
  line-height: 1.6;
  font-size: inherit;
  color: inherit;
}

.message-text .wemark_block_p {
  margin-bottom: 16rpx;
}

.message-text .wemark_block_p:last-child {
  margin-bottom: 0;
}

.message-text .wemark_inline_bold {
  font-weight: bold;
}

.message-text .wemark_inline_italic {
  font-style: italic;
}

.message-text .wemark_inline_code {
  background: rgba(0, 0, 0, 0.05);
  padding: 2rpx 8rpx;
  border-radius: 4rpx;
  font-family: monospace;
}

.message-text .wemark_block_ul,
.message-text .wemark_block_ol {
  margin: 16rpx 0;
  padding-left: 16rpx;
}

.message-text .wemark_block_ul:last-child,
.message-text .wemark_block_ol:last-child {
  margin-bottom: 0;
}

.message-text .wemark_block_ul_li_p,
.message-text .wemark_block_ol_li_p {
  display: flex;
  margin-bottom: 10rpx;
}

.message-text .wemark_block_ul_li_p:before {
  content: "•";
  margin-right: 12rpx;
  display: inline-block;
}

.message-text .wemark_block_ol_li_p:before {
  margin-right: 12rpx;
  min-width: 28rpx;
}

.bot .message-text .wemark_wrapper {
  color: #333;
}

.user .message-text .wemark_wrapper {
  color: #fff;
}

/* 训练计划卡片样式 */
.training-plan-card {
  background: #fff;
  border-radius: 12rpx;
  padding: 20rpx;
  margin: 20rpx 0;
  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.1);
}

.plan-header {
  padding: 10rpx 0 20rpx;
  border-bottom: 1rpx solid #eee;
}

.plan-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 8rpx;
}

.plan-duration {
  font-size: 24rpx;
  color: #666;
  display: block;
  margin-bottom: 6rpx;
}

.plan-description {
  font-size: 24rpx;
  color: #666;
  display: block;
  margin-top: 6rpx;
  line-height: 1.4;
}

/* 新的动作图片横向展示样式 */
.exercises-images {
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
  overflow-x: auto;
  padding: 20rpx 0;
  gap: 10rpx;
  align-items: center;
}

.exercise-image-inline {
  width: 100rpx;
  height: 100rpx;
  border-radius: 8rpx;
  flex-shrink: 0;
  background-color: #ffffff; /* 修改为白色背景 */
  object-fit: cover;
}

.more-exercises {
  width: 60rpx;
  height: 100rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #ffffff; /* 修改为白色背景 */
  border-radius: 8rpx;
  color: #666;
  font-size: 24rpx;
  flex-shrink: 0;
}

/* 保留旧样式以兼容 */
.exercises-list {
  display: none; /* 隐藏旧的列表样式 */
}

.exercise-item {
  display: flex;
  padding: 16rpx 0;
  border-bottom: 1rpx solid #f5f5f5;
}

.exercise-item:last-child {
  border-bottom: none;
}

.exercise-image {
  width: 120rpx;
  height: 120rpx;
  border-radius: 8rpx;
  margin-right: 20rpx;
  background-color: #ffffff; /* 修改为白色背景 */
}

.exercise-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.exercise-name {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
}

.exercise-sets {
  font-size: 24rpx;
  color: #666;
}

.view-details {
  text-align: right;
  padding: 16rpx 0 0;
  color: #007AFF;
  font-size: 26rpx;
}
