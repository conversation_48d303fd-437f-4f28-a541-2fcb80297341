// 获取应用实例
const app = getApp()
const authApi = require('../../api/auth');
const chatApi = require('../../api/chatAI');
const foodApi = require('../../api/food');
const requestApi = require('../../api/request');
const messageProcessor = require('../../utils/messageProcessor');

// 导入管理器
const MessageManager = require('./managers/messageManager');
const SessionManager = require('./managers/sessionManager');
const UIManager = require('./managers/uiManager');
const FoodAnalysisManager = require('./managers/foodAnalysisManager');
const TrainingPlanManager = require('./managers/trainingPlanManager');
const CacheManager = require('./managers/cacheManager');

Page({
  data: {
    userAvatar: '/images/icons/user-avatar.png',  // 用户头像
    botAvatar: '/images/icons/bot-avatar.png',    // 机器人头像
    messages: [],           // 聊天消息数组
    inputValue: '',        // 输入框的值
    previewImage: '',      // 预览图片
    isRecording: false,    // 是否正在录音
    isVoiceMode: false,    // 是否处于语音输入模式
    isLoading: false,      // 是否正在加载回复
    systemMessage: null,  // System prompt
    currentConversationId: null, // Session ID for WebSocket and API calls
    userInfo: null, // User info
    fileId: '', // For uploaded original image
    thumbFileId: '', // For uploaded thumbnail image
    loading: false, // General loading state
    mealTypeCN: '未知',
    openId: '',  // User openId
    socketTask: null, // WebSocket task instance
    isConnected: false, // WebSocket connection status
    currentBotMessageId: null, // Track the ID of the message being streamed
    isPageActive: true, // Flag to control auto-reconnect on background close
    reconnectAttempts: 0, // Track reconnection attempts
    maxReconnectAttempts: 5, // Max attempts before giving up
    pollIntervalId: null, // 轮询间隔ID
    lastMessageId: 0, // 最后收到的消息ID
    pendingMessage: null, // 待发送的消息
    loadingHistory: false, // Added for loading history state
    maxCachedMessages: 20, // 最大缓存消息数量
    recordingManager: null, // 录音管理器
    lastFoodAnalysis: null, // 存储最近一次的食物分析消息
    isLoadingMore: false,        // 是否正在加载更多消息
    hasMoreMessages: true,       // 是否有更多历史消息
    oldestMessageId: 0,          // 当前加载的最早消息ID
    messagesPerPage: 15,         // 每页加载消息数量
    localMessagesExhausted: false, // 本地缓存是否已耗尽
    refresherEnabled: true,      // 是否启用下拉刷新
    refresherTriggered: false,   // 下拉刷新是否被触发
    isFirstLoad: true,           // 是否是首次加载
    isRefreshing: false,         // 是否正在下拉刷新加载历史消息
  },

  onLoad() {
    // 初始化应用实例引用
    this.app = app;

    // 从本地存储获取上次使用的会话ID
    const lastSessionId = wx.getStorageSync('lastSessionId');

    // 如果没有上次的会话ID，生成一个新的
    const newConversationId = lastSessionId || `conv_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
    console.log('使用会话ID:', newConversationId, lastSessionId ? '(从缓存恢复)' : '(新生成)');

    // System message might be configured on the backend per session
    const systemMessage = {
      role: "system",
      content: "你是一位专业的健康顾问，专注于为用户提供运动和饮食方面的建议。请提供科学、可行的健康建议，使用友好、专业的语气，避免给出医疗诊断。遇到专业医疗问题时，建议用户咨询医生。"
    };

    // 初始化数据
    this.setData({
      currentConversationId: newConversationId,
      systemMessage: systemMessage,
      isPageActive: true,
      reconnectAttempts: 0,
      isFirstLoad: true,
    }, async () => {
      // 初始化各个管理器
      this.initManagers();

      try {
        await this.getOpenId(); // Ensure openId is available if still needed for food preview

        // 检查全局数据中是否已有聊天记录
        const globalMessages = this.app.globalData.chatMessages;
        if (globalMessages && globalMessages.length > 0) {
          console.log('从全局数据加载聊天记录，条数:', globalMessages.length);
          // 使用全局数据中的聊天记录
          await this.cacheManager.loadChatHistoryFromCache();
        } else {
          console.log('全局数据中没有聊天记录，尝试从服务器加载');
          // 尝试从服务器加载聊天记录
          try {
            await this.app.loadChatHistoryFromServer(true);
            // 加载成功后，从缓存加载到页面
            await this.cacheManager.loadChatHistoryFromCache();
          } catch (loadError) {
            console.error('从服务器加载聊天记录失败:', loadError);
            // 如果服务器加载失败，仍然尝试从本地缓存加载
            await this.cacheManager.loadChatHistoryFromCache();
          }
        }

        // 保存会话ID到本地存储
        if (!lastSessionId) {
          wx.setStorageSync('lastSessionId', newConversationId);
        }

        // 初始化聊天会话
        await this.sessionManager.initChatSession();

        // 刷新训练计划数据
        console.log('[DEBUG] onLoad 完成，开始刷新训练计划数据');
        try {
          await this.refreshTrainingPlans();
          console.log('[DEBUG] onLoad 中刷新训练计划数据完成');
        } catch (error) {
          console.error('[DEBUG] onLoad 中刷新训练计划数据失败:', error);
        }

        // 首次加载完成
        this.setData({ isFirstLoad: false });
      } catch (error) {
        console.error("onLoad initialization failed:", error);
        wx.showToast({ title: '初始化聊天失败', icon: 'none' });
        this.setData({ isFirstLoad: false });
      }
    });
  },

  /**
   * 初始化所有管理器
   */
  initManagers() {
    // 创建各个管理器实例
    this.messageManager = new MessageManager(this);
    this.sessionManager = new SessionManager(this);
    this.uiManager = new UIManager(this);
    this.foodAnalysisManager = new FoodAnalysisManager(this);
    this.trainingPlanManager = new TrainingPlanManager(this);
    this.cacheManager = new CacheManager(this);
  },

  async onShow() {
    // Called when page shows (including navigating back)
    this.setData({ isPageActive: true });

    // 如果是首次加载，onLoad中已经处理了聊天记录，这里不需要重复加载
    if (this.data.isFirstLoad) {
      console.log('首次加载中，onShow不重复加载聊天记录');
      return;
    }

    // 从缓存重新加载聊天记录，以获取可能在其他页面更新的消息
    // 确保管理器已经初始化
    if (!this.cacheManager || !this.trainingPlanManager) {
      console.log('管理器尚未初始化，正在初始化...');
      this.initManagers();
    }

    // 检查全局数据中是否有聊天记录
    const globalMessages = this.app.globalData.chatMessages;
    if (!globalMessages || globalMessages.length === 0) {
      console.log('全局数据中没有聊天记录，尝试从服务器加载');
      try {
        // 尝试从服务器加载聊天记录
        await this.app.loadChatHistoryFromServer(true);
      } catch (error) {
        console.error('从服务器加载聊天记录失败:', error);
      }
    }

    // 加载聊天记录
    if (this.cacheManager) {
      await this.cacheManager.loadChatHistoryFromCache();
    } else {
      console.error('无法初始化缓存管理器');
      // 使用旧方法作为备选
      await this.loadChatHistoryFromCache();
    }

    // 刷新训练计划数据
    console.log('[DEBUG] onShow 完成，开始刷新训练计划数据');
    try {
      this.refreshTrainingPlans().then(() => {
        console.log('[DEBUG] onShow 中刷新训练计划数据完成');
      }).catch(error => {
        console.error('[DEBUG] onShow 中刷新训练计划数据失败:', error);
      });
    } catch (error) {
      console.error('[DEBUG] onShow 中启动刷新训练计划数据失败:', error);
    }
  },

  /**
   * 刷新所有训练计划数据
   * 检查所有消息中的训练计划，并根据planId获取最新数据
   */
  async refreshTrainingPlans() {
    console.log('[DEBUG] 开始刷新训练计划数据');
    console.log('[DEBUG] 当前消息数量:', this.data.messages?.length || 0);

    try {
      // 确保trainingPlanManager已初始化
      if (!this.trainingPlanManager) {
        console.log('trainingPlanManager不可用，正在初始化...');
        this.initManagers();
      }

      if (this.trainingPlanManager) {
        // 获取当前消息列表
        const messages = this.data.messages;
        if (!messages || messages.length === 0) {
          console.log('[DEBUG] 没有消息，无需刷新训练计划');
          return;
        }

        // 找出包含训练计划的消息
        const trainingPlanMessages = messages.filter(msg =>
          msg.hasTrainingPlan ||
          msg.meta_info?.training_params?.related_plan_id ||
          msg.meta_info?.complete_training_plan
        );

        if (trainingPlanMessages.length === 0) {
          console.log('[DEBUG] 没有包含训练计划的消息，无需刷新');
          return;
        }

        console.log(`[DEBUG] 找到 ${trainingPlanMessages.length} 条包含训练计划的消息，开始处理`);

        // 处理每条训练计划消息
        const updatedMessages = [];
        for (const message of trainingPlanMessages) {
          try {
            // 使用trainingPlanManager处理消息
            const updatedMessage = await this.trainingPlanManager.processTrainingPlanMessage(message);
            if (updatedMessage && updatedMessage.showTrainingPlan) {
              updatedMessages.push(updatedMessage);
            }
          } catch (error) {
            console.error(`处理消息 ${message.id} 的训练计划时出错:`, error);
          }
        }

        // 更新消息列表
        if (updatedMessages.length > 0) {
          // 创建一个新的消息数组，替换已更新的消息
          const newMessages = [...messages];
          for (const updatedMessage of updatedMessages) {
            const index = newMessages.findIndex(msg => msg.id === updatedMessage.id);
            if (index !== -1) {
              newMessages[index] = updatedMessage;
            }
          }

          // 更新页面数据
          this.setData({ messages: newMessages });
          console.log(`[DEBUG] 成功更新 ${updatedMessages.length} 条训练计划消息`);

          // 更新缓存
          for (const message of updatedMessages) {
            if (this.cacheManager) {
              this.cacheManager.saveChatMessageToCache(message);
            } else {
              // 使用备用方法
              this.saveChatMessageToCache(message);
            }
          }
        } else {
          console.log('[DEBUG] 没有需要更新的训练计划消息');
        }
      } else {
        console.error('trainingPlanManager不可用，无法刷新训练计划');
      }
    } catch (error) {
      console.error('刷新训练计划数据失败:', error);
    }
  },

  onHide() {
    // Called when page hides (navigating away, switching apps)
    this.setData({ isPageActive: false });
  },

  onUnload() {
    // Called when page is destroyed
    this.setData({ isPageActive: false }); // Ensure flag is false

    // Clean up WebSocket connection when leaving the page permanently
    if (this.data.socketTask) {
      console.log("Page unloading, closing WebSocket definitively.");
      // Use the specific close function from chatAI
      chatApi.closeChatWebSocket(this.data.currentConversationId);
      this.setData({ socketTask: null, isConnected: false });
    }

    // 清除任何活跃的轮询
    this.messageManager.clearPolling();
  },

  // Get user openId if needed
  async getOpenId() {
    try {
      const openid = await authApi.getOpenId(); // Assumes authApi provides this
      this.setData({ openId: openid });
      return openid;
    } catch (error) {
      console.error('获取 openId 失败:', error);
      wx.showToast({title: '获取用户信息失败', icon:'none'});
      throw error; // Re-throw if critical
    }
  },

  // 以下是事件处理函数，转发到对应的管理器

  // 输入变化处理
  onInputChange(e) {
    this.uiManager.onInputChange(e);
  },

  // 发送按钮点击事件
  onSendButtonTap() {
    this.uiManager.onSendButtonTap();
  },

  // 发送消息
  sendMessage(customMessage) {
    this.messageManager.sendMessage(customMessage);
  },

  // 切换输入模式（文字/语音）
  toggleInputMode() {
    this.uiManager.toggleInputMode();
  },

  // 开始录音
  startRecording(e) {
    this.uiManager.startRecording(e);
  },

  // 结束录音
  endRecording(e) {
    this.uiManager.endRecording(e);
  },

  // 取消录音
  cancelRecording(e) {
    this.uiManager.cancelRecording(e);
  },

  // 处理上传图片
  handleUploadImage() {
    this.foodAnalysisManager.handleUploadImage();
  },

  // 导航到食物预览
  navigateToFoodPreview(e) {
    this.foodAnalysisManager.navigateToFoodPreview(e);
  },

  // 导航到训练计划
  navigateToTrainingPlan(e) {
    this.trainingPlanManager.navigateToTrainingPlan(e);
  },

  // 处理饮食建议
  handleDietAdvice() {
    this.uiManager.handleDietAdvice();
  },

  // 处理运动计划
  handleExercisePlan() {
    this.uiManager.handleExercisePlan();
  },

  // 处理训练参数选择
  handleTrainingParamSelect(e) {
    this.uiManager.handleTrainingParamSelect(e);
  },

  // 处理训练参数确认
  handleTrainingParamConfirm(e) {
    this.uiManager.handleTrainingParamConfirm(e);
  },

  // 导航到workout详情页
  navigateToWorkoutDetail(e) {
    const workoutId = e.currentTarget.dataset.workoutId;
    if (!workoutId) {
      console.error('导航到workout详情页失败: 缺少workoutId');
      return;
    }

    wx.navigateTo({
      url: `/pages/workout-detail/index?id=${workoutId}`,
      fail: (err) => {
        console.error('导航到workout详情页失败:', err);
        wx.showToast({
          title: '页面跳转失败',
          icon: 'none'
        });
      }
    });
  },

  // 滚动到底部
  scrollToBottom() {
    if (this.uiManager) {
      this.uiManager.scrollToBottom();
    } else {
      // 备用滚动方法
      setTimeout(() => {
        const query = wx.createSelectorQuery().in(this);
        query.select('.chat-list').boundingClientRect();
        query.exec((res) => {
          if (res && res[0]) {
            wx.pageScrollTo({
              scrollTop: res[0].height,
              duration: 300
            });
          }
        });
      }, 100);
    }
  },

  /**
   * 处理下拉刷新事件
   * 加载更多历史消息
   */
  async onRefresherRefresh() {
    console.log('触发下拉刷新，加载更多历史消息');

    // 如果已经在加载中，不重复触发
    if (this.data.isLoadingMore) {
      console.log('已经在加载更多消息，忽略此次刷新');
      return;
    }

    // 设置刷新状态，添加 isRefreshing 标志位
    this.setData({
      refresherTriggered: true,
      isLoadingMore: true,
      isRefreshing: true // 标志位，表示当前是下拉刷新状态
    });

    try {
      // 获取当前最早的消息ID
      let oldestMessageId = this.data.oldestMessageId;
      if (oldestMessageId === 0 && this.data.messages.length > 0) {
        // 如果没有设置最早消息ID，但有消息，则使用第一条消息的ID
        const sortedMessages = [...this.data.messages].sort((a, b) => {
          return (a.id || 0) - (b.id || 0);
        });
        oldestMessageId = sortedMessages[0].id || 0;
        console.log('计算得到最早消息ID:', oldestMessageId);
      }

      console.log('当前最早消息ID:', oldestMessageId);

      let loadSuccess = false;

      // 如果本地缓存已耗尽，直接从服务器加载
      if (this.data.localMessagesExhausted || oldestMessageId <= 0) {
        console.log('本地缓存已耗尽或无最早消息ID，从服务器加载更多消息');
        loadSuccess = await this.loadMoreMessagesFromServer();
      } else {
        // 否则先尝试从本地缓存加载
        const loadedFromCache = await this.loadMoreMessagesFromCache(oldestMessageId);

        // 如果本地没有更多消息，则从服务器加载
        if (!loadedFromCache) {
          console.log('本地缓存无更多消息，从服务器加载');
          loadSuccess = await this.loadMoreMessagesFromServer();
        } else {
          loadSuccess = true;
        }
      }

      if (!loadSuccess) {
        console.log('没有加载到更多消息');
        // 如果没有更多消息，设置hasMoreMessages为false
        this.setData({ hasMoreMessages: false });

        // 显示提示
        if (this.data.messages.length > 0) {
          wx.showToast({
            title: '没有更多历史消息',
            icon: 'none'
          });
        }
      }
    } catch (error) {
      console.error('加载更多消息失败:', error);
      wx.showToast({
        title: '加载历史消息失败',
        icon: 'none'
      });
    } finally {
      // 重置刷新状态 - 使用setTimeout延迟重置isRefreshing，确保视图渲染完成后再重置
      setTimeout(() => {
        this.setData({
          refresherTriggered: false,
          isLoadingMore: false,
          isRefreshing: false // 重置下拉刷新标志位
        });
      }, 300);
    }
  },

  /**
   * 从本地缓存加载更多消息
   * @param {number} beforeId - 加载此ID之前的消息
   * @returns {Promise<boolean>} 是否成功加载到更多消息
   */
  async loadMoreMessagesFromCache(beforeId) {
    console.log('从本地缓存加载更多消息，beforeId:', beforeId);

    try {
      // 获取所有缓存的消息
      const allCachedMessages = this.app.getCachedChatMessages();
      if (!allCachedMessages || allCachedMessages.length === 0) {
        console.log('缓存中没有消息');
        this.setData({ localMessagesExhausted: true });
        return false;
      }

      // 过滤出ID小于beforeId的消息
      let olderMessages = allCachedMessages.filter(msg =>
        msg.id && typeof msg.id === 'number' && msg.id < beforeId
      );

      // 按ID排序
      olderMessages.sort((a, b) => b.id - a.id);

      // 取出指定数量的消息
      const messagesToLoad = olderMessages.slice(0, this.data.messagesPerPage);

      if (messagesToLoad.length === 0) {
        console.log('缓存中没有更早的消息');
        this.setData({ localMessagesExhausted: true });
        return false;
      }

      console.log('从缓存中找到更早的消息:', messagesToLoad.length);

      // 先使用基本处理显示消息，避免等待时间过长
      const basicProcessedMessages = [];
      let newOldestId = beforeId;

      // 使用messageProcessor直接处理，避免异步等待
      const messageProcessor = require('../../utils/messageProcessor');
      for (const msg of messagesToLoad) {
        try {
          // 确保消息有基本字段
          const safeMsg = { ...msg };
          if (!safeMsg.meta_info) {
            safeMsg.meta_info = {};
          }

          const basicMessage = messageProcessor.processMessage(safeMsg, { processUI: true });
          if (basicMessage) {
            basicProcessedMessages.push(basicMessage);
            // 更新最早消息ID
            if (safeMsg.id && typeof safeMsg.id === 'number' && safeMsg.id < newOldestId) {
              newOldestId = safeMsg.id;
            }
          }
        } catch (processError) {
          console.error('基本处理缓存消息时出错，跳过此消息:', processError, '消息内容:', JSON.stringify(msg).substring(0, 200) + '...');
          continue;
        }
      }

      // 先显示基本处理的消息
      if (basicProcessedMessages.length > 0) {
        // 先设置标志位，防止滚动到底部
        this.setData({ isRefreshing: true });
        
        // 将新消息添加到现有消息列表的前面
        const updatedMessages = [...basicProcessedMessages, ...this.data.messages];

        this.setData({
          messages: updatedMessages,
          oldestMessageId: newOldestId,
          hasMoreMessages: olderMessages.length > messagesToLoad.length
        });

        console.log('成功加载基本处理的缓存消息，当前消息总数:', updatedMessages.length);
      } else {
        console.log('基本处理后没有有效消息');
        return false;
      }

      // 然后异步处理每条消息，获取完整的处理结果
      const processedMessages = [];
      for (const msg of messagesToLoad) {
        try {
          // 使用messageManager的异步processMessage方法
          if (this.messageManager) {
            const message = await this.messageManager.processMessage(msg);
            if (message) {
              processedMessages.push(message);
            }
          }
        } catch (processError) {
          console.error('异步处理缓存消息时出错，跳过此消息:', processError);
          continue;
        }
      }

      // 更新消息列表，使用完整处理的结果
      if (processedMessages.length > 0) {
        // 将新消息添加到现有消息列表的前面，保留当前列表中除了基本处理消息之外的所有消息
        const currentMessages = this.data.messages.slice(basicProcessedMessages.length);
        const updatedMessages = [...processedMessages, ...currentMessages];

        this.setData({
          messages: updatedMessages,
          oldestMessageId: newOldestId,
          hasMoreMessages: olderMessages.length > messagesToLoad.length
        });

        console.log('成功加载完整处理的缓存消息，当前消息总数:', updatedMessages.length);
        return true;
      }

      return basicProcessedMessages.length > 0;
    } catch (error) {
      console.error('从缓存加载更多消息失败:', error);
      return false;
    }
  },

  /**
   * 从服务器加载更多消息
   * @returns {Promise<boolean>} 是否成功加载到更多消息
   */
  async loadMoreMessagesFromServer() {
    console.log('从服务器加载更多消息');

    try {
      // 计算要跳过的消息数量
      const skip = this.data.messages.length;
      const limit = this.data.messagesPerPage;

      console.log(`请求服务器消息，skip=${skip}, limit=${limit}`);

      // 调用API获取更多消息
      const chatApi = require('../../api/chatAI');
      const result = await chatApi.getRecentConversations(skip, limit, false);

      if (!result || !result.messages || result.messages.length === 0) {
        console.log('服务器没有更多消息');
        this.setData({ hasMoreMessages: false });
        return false;
      }

      console.log('从服务器获取到更多消息:', result.messages.length);

      // 先使用基本处理显示消息，避免等待时间过长
      const basicProcessedMessages = [];
      let oldestId = Number.MAX_SAFE_INTEGER;

      // 使用messageProcessor直接处理，避免异步等待
      const messageProcessor = require('../../utils/messageProcessor');
      for (const msg of result.messages) {
        try {
          // 确保消息有基本字段
          const safeMsg = { ...msg };
          if (!safeMsg.meta_info) {
            safeMsg.meta_info = {};
          }

          const basicMessage = messageProcessor.processMessage(safeMsg, { processUI: true });
          if (basicMessage) {
            basicProcessedMessages.push(basicMessage);
            // 更新最早消息ID
            if (safeMsg.id && typeof safeMsg.id === 'number' && safeMsg.id < oldestId) {
              oldestId = safeMsg.id;
            }
          }
        } catch (processError) {
          console.error('基本处理服务器消息时出错，跳过此消息:', processError, '消息内容:', JSON.stringify(msg).substring(0, 200) + '...');
          continue;
        }
      }

      // 先显示基本处理的消息
      if (basicProcessedMessages.length > 0) {
        // 先设置标志位，防止滚动到底部
        this.setData({ isRefreshing: true });
        
        // 将新消息添加到现有消息列表的前面
        const updatedMessages = [...basicProcessedMessages, ...this.data.messages];

        this.setData({
          messages: updatedMessages,
          oldestMessageId: oldestId,
          hasMoreMessages: result.messages.length >= limit
        });

        // 保存到缓存
        this.saveChatMessagesToCache(basicProcessedMessages);

        console.log('成功加载基本处理的服务器消息，当前消息总数:', updatedMessages.length);
      } else {
        console.log('基本处理后没有有效消息');
        return false;
      }

      // 然后异步处理每条消息，获取完整的处理结果
      const processedMessages = [];
      for (const msg of result.messages) {
        try {
          // 使用messageManager的异步processMessage方法
          if (this.messageManager) {
            const message = await this.messageManager.processMessage(msg);
            if (message) {
              processedMessages.push(message);
            }
          }
        } catch (processError) {
          console.error('异步处理服务器消息时出错，跳过此消息:', processError);
          continue;
        }
      }

      // 更新消息列表，使用完整处理的结果
      if (processedMessages.length > 0) {
        // 将新消息添加到现有消息列表的前面，保留当前列表中除了基本处理消息之外的所有消息
        const currentMessages = this.data.messages.slice(basicProcessedMessages.length);
        const updatedMessages = [...processedMessages, ...currentMessages];

        this.setData({
          messages: updatedMessages,
          oldestMessageId: oldestId,
          hasMoreMessages: result.messages.length >= limit
        });

        // 保存到缓存
        this.saveChatMessagesToCache(processedMessages);

        console.log('成功加载完整处理的服务器消息，当前消息总数:', updatedMessages.length);
        return true;
      }

      return basicProcessedMessages.length > 0;
    } catch (error) {
      console.error('从服务器加载更多消息失败:', error);
      wx.showToast({
        title: '加载历史消息失败',
        icon: 'none'
      });
      return false;
    }
  },

  /**
   * 添加消息到聊天，统一处理缓存和数据库存储
   * @param {string|object} content - 消息内容
   * @param {string} role - 消息角色 'user' 或 'assistant'
   * @param {string} type - 消息类型，如 'text', 'image', 'food_analysis'
   * @param {object} [metaInfo={}] - 附加元数据
   * @param {Function} [callback] - 成功添加后的回调
   * @param {boolean} [saveToDatabase=true] - 是否保存到数据库
   * @return {Object} 添加的消息对象和Promise
   */
  addMessage: function(content, role, type = 'text', metaInfo = {}, callback, saveToDatabase = true) {
    // 创建消息基础对象(带临时ID)
    const tempMessageId = `temp_${role}_${type}_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
    const messageObj = {
      id: tempMessageId,
      role: role,
      type: type,
      content: content,
      created_at: new Date().toISOString(),
      meta_info: metaInfo
    };

    // 使用processMessage处理消息
    const processedMessage = this.messageManager.processMessage(messageObj);

    // 添加到消息列表(使用临时ID)
    const updatedMessages = [...this.data.messages, processedMessage];
    this.setData({ messages: updatedMessages });

    // 滚动到底部
    this.scrollToBottom();

    // 返回消息对象和Promise
    const page = this;

    // 如果不需要保存到数据库，直接返回
    if (!saveToDatabase) {
      return {
        message: processedMessage,
        promise: Promise.resolve({ ...processedMessage, id: tempMessageId })
      };
    }

    // 保存到数据库并返回Promise
    const savePromise = new Promise((resolve, reject) => {
      if (!this.data.currentConversationId) {
        reject(new Error('没有会话ID，无法保存消息'));
        return;
      }

      chatApi.addMessageToSession(
        this.data.currentConversationId,
        content,
        role,
        { type: type, ...metaInfo }
      ).then(savedMessage => {
        if (savedMessage?.id) {
          // 使用数据库ID更新消息
          const dbMessageId = savedMessage.id;

          // 直接更新processedMessage，确保引用的对象也被更新
          processedMessage.dbMessageId = dbMessageId;
          console.log(`直接更新processedMessage.dbMessageId = ${dbMessageId}`);

          // 更新本地消息ID和引用
          const updateResult = page.updateMessageById(tempMessageId, {
            id: dbMessageId,  // 用数据库ID替换临时ID
            dbMessageId: dbMessageId  // 保存数据库ID的引用
          });

          console.log(`${role}消息已保存到数据库, ID:`, dbMessageId);

          // 现在用真实ID保存到缓存
          if (updateResult) {
            if (page.cacheManager) {
              page.cacheManager.saveChatMessageToCache(updateResult);
            } else {
              // 使用备用方法
              page.app.saveChatMessageToCache(updateResult);
            }
          }

          // 执行回调
          if (typeof callback === 'function') {
            callback(savedMessage);
          }

          resolve(savedMessage);
        } else {
          reject(new Error('保存消息成功但没有返回ID'));
        }
      }).catch(error => {
        console.error(`保存${role}消息到数据库失败:`, error);
        reject(error);
      });
    });

    return { message: processedMessage, promise: savePromise };
  },

  /**
   * 根据ID更新消息
   * @param {string} messageId - 消息ID
   * @param {object} updates - 要更新的字段
   * @param {boolean} [updateDatabase=false] - 是否同步更新数据库
   * @return {Object} 更新后的消息对象
   */
  updateMessageById: function(messageId, updates, updateDatabase = false) {
    const messages = [...this.data.messages];
    const msgIndex = messages.findIndex(m => m.id === messageId);

    if (msgIndex !== -1) {
      // 更新本地消息
      messages[msgIndex] = { ...messages[msgIndex], ...updates };

      // 如果ID变更了，需要处理消息数组中的引用
      if (updates.id && updates.id !== messageId) {
        // 修改页面数据中的引用
        this.setData({
          messages: messages.map(msg =>
            msg.id === messageId ? messages[msgIndex] : msg
          )
        });
      } else {
        // 正常更新
        this.setData({ messages });
      }

      // 如果需要，同步更新数据库
      if (updateDatabase && (messages[msgIndex].dbMessageId || updates.dbMessageId)) {
        const dbId = messages[msgIndex].dbMessageId || updates.dbMessageId;

        // 确保dbId是数字
        const numericDbId = parseInt(dbId, 10);

        if (isNaN(numericDbId)) {
          console.error('无法更新数据库消息：无效的ID类型', dbId);
          return messages[msgIndex];
        }

        // 获取现有元数据（如果存在）
        const existingMetaInfo = messages[msgIndex].meta_info || {};

        // 准备更完整的元数据，保留现有字段并合并新字段
        const metaInfo = {
          ...existingMetaInfo,  // 保留所有现有元数据字段
          type: messages[msgIndex].type,  // 确保type字段存在
          // 从消息或更新中获取其他需要的元数据字段
          imageUrl: messages[msgIndex].imageUrl || updates.imageUrl || existingMetaInfo.imageUrl,
          status: messages[msgIndex].status || updates.status || existingMetaInfo.status
        };

        // 如果updates包含isCompleted，则在meta_info中设置confirmed状态
        if (updates.isCompleted !== undefined) {
          metaInfo.confirmed = updates.isCompleted;
        }

        // 合并updates中的其他元数据字段
        if (updates.metaInfo) {
          Object.assign(metaInfo, updates.metaInfo);
        }

        console.log('准备更新数据库消息:', {
          messageId,
          dbId: numericDbId,
          hasDbMessageId: Boolean(messages[msgIndex].dbMessageId),
          updatesHasDbMessageId: Boolean(updates.dbMessageId),
          content: messages[msgIndex].content,
          metaInfo: metaInfo
        });

        chatApi.updateMessage(
          numericDbId,
          messages[msgIndex].content,
          messages[msgIndex].role,
          metaInfo
        ).then(updatedMessage => {
          console.log('数据库消息更新成功:', updatedMessage);
        }).catch(error => {
          console.error('更新数据库消息失败:', error);
        });
      }

      return messages[msgIndex];
    }

    return null;
  },

  /**
   * 将多个消息保存到缓存
   * @param {Array} messages - 要保存的消息数组
   */
  saveChatMessagesToCache: function(messages) {
    if (!messages || !Array.isArray(messages) || messages.length === 0) return;

    if (this.cacheManager) {
      this.cacheManager.saveChatMessagesToCache(messages);
    } else {
      console.log('cacheManager不可用，使用备用方法保存消息');
      // 使用app全局方法保存消息
      messages.forEach(message => {
        if (message) {
          this.app.saveChatMessageToCache(message);
        }
      });
    }
  },

  /**
   * 加载历史聊天记录
   * 为了兼容性而保留的包装函数
   */
  loadChatHistory: async function () {
    console.log('调用loadChatHistory已过时，但为了兼容性而保留');

    // 检查cacheManager是否可用
    if (this.cacheManager) {
      this.cacheManager.loadChatHistoryFromCache();
    } else {
      // 使用备用方法
      this.loadChatHistoryFromCache();
    }
  },

  /**
   * 从缓存加载聊天历史记录
   * 这是一个备用方法，当cacheManager不可用时使用
   * @returns {Promise<Array>} 加载的消息数组
   */
  loadChatHistoryFromCache: function () {
    return new Promise((resolve, reject) => {
      try {
        console.log('使用备用方法加载聊天记录');
        this.setData({
          loadingHistory: true
        });

      // 从app全局数据或本地存储获取缓存的聊天记录
      const cachedMessages = this.app.getCachedChatMessages();
      console.log('从缓存加载聊天记录，条数:', cachedMessages.length);

      if (!cachedMessages || cachedMessages.length === 0) {
        console.log('缓存中无聊天记录');
        this.setData({
          loadingHistory: false
        });
        return;
      }

      // 处理缓存的消息
      const processedMessages = [];
      let lastMessageId = 0;

      for (const msg of cachedMessages) {
        // 使用messageProcessor直接处理消息
        const message = messageProcessor.processMessage(msg, { processUI: true });
        if (message) {
          processedMessages.push(message);
          // 跟踪最大消息 ID 用于记录
          if (msg.id && typeof msg.id === 'number' && msg.id > lastMessageId) {
            lastMessageId = msg.id;
          }
        }
      }

      // 更新消息列表、最后消息ID
      if (processedMessages.length > 0) {
        console.log('处理后的缓存消息:', processedMessages);

        this.setData({
          messages: processedMessages,
          lastMessageId: lastMessageId || 0
        });

        // 初始加载时滚动到底部，但下拉刷新时不滚动
        if (this.data.isFirstLoad && !this.data.isRefreshing) {
          setTimeout(() => {
            this.scrollToBottom();
          }, 100);
        }
      }

      console.log('聊天记录加载完成');
        resolve(processedMessages);
      } catch (err) {
        console.error('加载聊天记录失败:', err);
        wx.showToast({
          title: '加载历史消息失败',
          icon: 'none'
        });
        reject(err);
      } finally {
        this.setData({
          loadingHistory: false
        });
      }
    });
  },

  /**
   * 保存消息到缓存
   * @param {Object} message - 要保存的消息对象
   */
  saveChatMessageToCache: function(message) {
    if (!message) return;

    if (this.cacheManager) {
      this.cacheManager.saveChatMessageToCache(message);
    } else {
      // 使用app全局方法保存消息
      this.app.saveChatMessageToCache(message);
    }
  },

  /**
   * 更新训练计划消息
   * 当用户在训练计划详情页点击"加入计划"或"开始训练"按钮时调用
   * @param {string|number} messageId - 消息ID
   * @param {string|number} planId - 计划ID
   * @param {Object} statusInfo - 状态信息，如 {status: 'active'}
   * @returns {Promise<boolean>} 更新是否成功
   */
  updateTrainingPlanMessage: async function(messageId, planId, statusInfo = {}) {
    console.log('chatbot/index.js: 接收到训练计划更新请求', { messageId, planId, statusInfo });

    // 确保管理器已初始化
    if (!this.trainingPlanManager) {
      console.log('trainingPlanManager不可用，正在初始化...');
      this.initManagers();
    }

    if (this.trainingPlanManager) {
      return await this.trainingPlanManager.updateTrainingPlanMessage(messageId, planId, statusInfo);
    } else {
      console.error('trainingPlanManager不可用，无法更新训练计划消息');
      wx.showToast({
        title: '更新训练计划失败',
        icon: 'none'
      });
      return false;
    }
  }
});
