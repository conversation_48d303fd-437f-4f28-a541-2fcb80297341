.calendar-container {
  background-color: #ffffff;
  padding: 20rpx;
}

.calendar-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.arrow {
  font-size: 40rpx;
  color: #333;
  cursor: pointer;
}

.current-date {
  font-size: 36rpx;
  font-weight: bold;
}

.weekdays {
  display: flex;
  justify-content: space-around;
  margin-bottom: 10rpx;
}

.weekdays view {
  width: 14.28%;
  text-align: center;
  font-size: 28rpx;
  color: #888;
}

.days {
  display: flex;
  flex-wrap: wrap;
}

.day {
  width: 14.28%;
  height: 120rpx;
  text-align: center;
  padding: 5rpx;
  box-sizing: border-box;
}

.date {
  font-size: 28rpx;
  margin-bottom: 5rpx;
}

.today .date {
  background-color: #1890ff;
  color: #fff;
  width: 60rpx;
  height: 60rpx;
  line-height: 60rpx;
  border-radius: 50%;
  margin: 0 auto 5rpx;
}

.activities {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.activity {
  font-size: 20rpx;
  padding: 2rpx 6rpx;
  border-radius: 4rpx;
  margin-bottom: 2rpx;
  max-width: 90%;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.activity.green {
  background-color: #52c41a;
  color: #fff;
}

.activity.gray {
  background-color: #d9d9d9;
  color: #fff;
}

.activity.ellipsis {
  background-color: #f0f0f0;
  color: #888;
}

.back-to-today {
  margin-top: 20rpx;
  background-color: #333;
  color: #fff;
  padding: 20rpx;
  border-radius: 10rpx;
  text-align: center;
  font-size: 28rpx;
  cursor: pointer;
}

.back-to-today .iconfont {
  margin-right: 10rpx;
}


.arrow-icon, .return-icon {
  width: 20px;  /* Adjust this value to match your text size */
  height: 20px; /* Adjust this value to match your text size */
  vertical-align: middle;
}

.back-to-today {
  display: flex;
  align-items: center;
  justify-content: center;
}

.back-to-today text {
  margin-left: 5px; /* Adds some space between the icon and text */
}