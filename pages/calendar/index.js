Page({
  data: {
    // 当前年份和月份
    currentYear: new Date().getFullYear(),
    currentMonth: new Date().getMonth() + 1,
    // 星期显示
    weekdays: ['日', '一', '二', '三', '四', '五', '六'],
    // 日历数据
    days: [],
    // 训练活动数据
    trainingActivities: [
      { date: '2024-10-10', activities: ['初级全身力', '量燃脂'] },
      { date: '2024-10-12', activities: ['胸肩初级', '初级全身力', '量燃脂'] },
      { date: '2024-10-13', activities: ['胸肩初级', '初级全身力量燃脂'] },
      { date: '2024-10-15', activities: ['背部核心初级'] },
      { date: '2024-10-16', activities: ['臂腿初级'] },
      { date: '2024-10-20', activities: ['胸肩初级'] },
      { date: '2024-10-22', activities: ['背部核心初级'] },
      { date: '2024-10-23', activities: ['臂腿初级'] },
      { date: '2024-10-26', activities: ['胸肩初级'] },
      { date: '2024-10-29', activities: ['背部核心初级'] },
      { date: '2024-10-30', activities: ['臂腿初级'] },
    ]
  },

  /**
   * 页面加载时生成日历
   */
  onLoad: function () {
    this.generateCalendar()
  },

  /**
   * 生成日历数据
   */
  generateCalendar: function () {
    const year = this.data.currentYear
    const month = this.data.currentMonth
    const firstDay = new Date(year, month - 1, 1).getDay()
    const daysInMonth = new Date(year, month, 0).getDate()
    const today = new Date()

    let days = []

    // 填充上月剩余日期
    for (let i = 0; i < firstDay; i++) {
      days.push({ day: '', isCurrentMonth: false })
    }

    // 生成当月日期
    for (let i = 1; i <= daysInMonth; i++) {
      const date = new Date(year, month - 1, i)
      const dateString = this.formatDate(date)
      const activities = this.data.trainingActivities.find(a => a.date === dateString)?.activities || []

      days.push({
        day: i,
        isCurrentMonth: true,
        isToday: date.toDateString() === today.toDateString(),
        activities: activities,
        fullDate: dateString
      })
    }

    this.setData({ days: days })
  },

  /**
   * 格式化日期为 YYYY-MM-DD 格式
   * @param {Date} date - 日期对象
   * @return {string} 格式化后的日期字符串
   */
  formatDate: function(date) {
    const year = date.getFullYear()
    const month = (date.getMonth() + 1).toString().padStart(2, '0')
    const day = date.getDate().toString().padStart(2, '0')
    return `${year}-${month}-${day}`
  },

  /**
   * 切换到上个月
   */
  prevMonth: function () {
    let newYear = this.data.currentYear
    let newMonth = this.data.currentMonth - 1
    if (newMonth === 0) {
      newYear--
      newMonth = 12
    }
    this.setData({
      currentYear: newYear,
      currentMonth: newMonth
    }, () => {
      this.generateCalendar()
    })
  },

  /**
   * 切换到下个月
   */
  nextMonth: function () {
    let newYear = this.data.currentYear
    let newMonth = this.data.currentMonth + 1
    if (newMonth === 13) {
      newYear++
      newMonth = 1
    }
    this.setData({
      currentYear: newYear,
      currentMonth: newMonth
    }, () => {
      this.generateCalendar()
    })
  },

  /**
   * 跳转到今天
   */
  goToToday: function () {
    const today = new Date()
    const todayString = this.formatDate(today)
    const app = getApp();
    app.globalData.selectedDate = todayString;

    // 使用 wx.navigateBack 返回首页
    wx.navigateBack();
  },

  /**
   * 选择日期
   * @param {Object} e - 事件对象
   */
  selectDate: function(e) {
    const { fullDate } = e.currentTarget.dataset
    if (!fullDate) {
      console.error("Invalid date received:", fullDate)
      return
    }
    const app = getApp();
    app.globalData.selectedDate = fullDate;

    // 使用 wx.navigateBack 返回首页
    wx.navigateBack();
  }
})