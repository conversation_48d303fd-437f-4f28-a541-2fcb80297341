<view class="calendar-container">
  <!-- Calendar Header -->
  <view class="calendar-header">
    <view class="arrow" bindtap="prevMonth">
      <image src="/images/icons/left arrow.png" mode="aspectFit" class="arrow-icon" />
    </view>
    <view class="current-date">{{currentYear}}年{{currentMonth}}月</view>
    <view class="arrow" bindtap="nextMonth">
      <image src="/images/icons/right arrow.png" mode="aspectFit" class="arrow-icon" />
    </view>
  </view>
  
  <!-- Weekdays row -->
  <view class="weekdays">
    <view wx:for="{{weekdays}}" wx:key="*this">{{item}}</view>
  </view>
  
  <!-- Days Grid -->
  <view class="days">
    <view class="day {{item.isCurrentMonth ? '' : 'other-month'}} {{item.isToday ? 'today' : ''}} {{item.isSelected ? 'selected' : ''}}" 
          wx:for="{{days}}" wx:key="fullDate"
          bindtap="selectDate" data-full-date="{{item.fullDate}}">
      <view class="date">{{item.isToday ? '今' : item.day}}</view>
      <!-- Activity tags under the day -->
      <view class="activities">
        <view class="activity {{index === 0 ? 'green' : 'gray'}}" 
              wx:for="{{item.activities}}" wx:for-item="activity" 
              wx:for-index="index" wx:key="*this" wx:if="{{index < 2}}">
          {{activity}}
        </view>
        <view class="activity ellipsis" wx:if="{{item.activities.length > 2}}">...</view>
      </view>
    </view>
  </view>
  
  <!-- Button to go back to today -->
  <view class="back-to-today" bindtap="goToToday">
    <image src="/images/icons/return.png" mode="aspectFit" class="return-icon" />
    <text>回今天</text>
  </view>
</view>