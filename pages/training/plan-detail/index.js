const app = getApp()
// 导入API模块
const trainingPlanApi = require('../../../api/training-plan')
// 导入工具类
const imageHelper = require('../../../utils/image-helper')
const TrainingPlanHelper = require('../../../utils/training-plan-helper')
const ExerciseDataHelper = require('../../../utils/exercise-data-helper')

Page({
  data: {
    plan: {
      id: '',
      name: '',
      estimated_duration: 0,
      description: '',
    },
    exercises: [],
    isLoading: true,
    needRefresh: false,
    needSave: false, // 标记是否需要保存
    planDetail: null, // 存储完整的计划详情数据
    datePickerVisible: false,
    minDate: new Date().getTime(),
    maxDate: new Date(new Date().setFullYear(new Date().getFullYear() + 1)).getTime(), // 最大日期为一年后
    messageId: null, // 存储聊天消息ID，用于更新meta_info
    openerPagePath: null, // 存储打开页面的路径

    // 模板创建相关字段
    isCreateTemplateMode: false,
    templateData: null, // 存储模板数据

    // 模板查看和编辑相关字段
    isViewTemplateMode: false,
    isEditTemplateMode: false,
    templateId: null // 存储模板ID用于更新
  },

  onLoad: function(options) {
    console.log('plan-detail onLoad options:', options);

    // 检查是否为创建模板模式
    if (options.mode === 'createTemplate') {
      this.setData({
        isCreateTemplateMode: true,
        isLoading: false
      });

      // 监听模板数据
      const eventChannel = this.getOpenerEventChannel();
      if (eventChannel) {
        eventChannel.on('templateData', (templateData) => {
          console.log('接收到模板数据:', templateData);
          this.setupTemplateMode(templateData);
        });
      }
      return;
    }

    // 检查是否为查看模板模式 - 直接设置为编辑模式，避免数据丢失
    if (options.mode === 'viewTemplate' && options.templateId) {
      this.setData({
        isEditTemplateMode: true, // 直接设置为编辑模式
        templateId: options.templateId,
        isLoading: false
      });

      // 监听模板数据
      const eventChannel = this.getOpenerEventChannel();
      if (eventChannel) {
        eventChannel.on('templateData', (templateData) => {
          console.log('接收到查看模板数据，直接设置为编辑模式:', templateData);
          this.setupEditTemplateMode(templateData); // 使用统一的编辑模式设置
        });
      }
      return;
    }

    if (options.planId) {
      // 保存计划ID
      this.setData({
        plan: {
          id: options.planId
        },
        isLoading: true
      });

      // 获取打开页面的路径
      const pages = getCurrentPages();
      const prevPage = pages.length > 1 ? pages[pages.length - 2] : null;
      const openerPagePath = prevPage ? prevPage.route : null;

      console.log('打开页面的路径:', openerPagePath);

      // 保存打开页面的路径
      this.setData({
        openerPagePath: openerPagePath
      });

      // 尝试从页面参数中获取计划数据
      const eventChannel = this.getOpenerEventChannel();
      if (eventChannel) {
        eventChannel.on('acceptDataFromOpenerPage', (data) => {
          if (data && data.plan) {
            console.log('接收到的计划数据:', data.plan);

            // 保存消息ID（如果存在）
            if (data.messageId) {
              console.log('接收到的消息ID:', data.messageId);
              this.setData({
                messageId: data.messageId
              });
            }

            this.processPlanData(data.plan);
          } else {
            this.fetchPlanDetail();
          }
        });
      } else {
        // 如果没有eventChannel，直接获取数据
        this.fetchPlanDetail();
      }
    } else {
      wx.showToast({
        title: '缺少计划ID',
        icon: 'none'
      });
    }
  },

  onShow() {
    console.log('[DEBUG] onShow 触发，needRefresh:', this.data.needRefresh);

    // 检查是否需要刷新
    if (this.data.needRefresh) {
      console.log('[DEBUG] 页面显示，需要刷新数据');
      this.fetchPlanDetail();
      this.setData({
        needRefresh: false
      });
    }

    // 检查是否有待处理的选择动作数据
    const pages = getCurrentPages();
    const currentPage = pages[pages.length - 1];

    console.log('[DEBUG] 当前页面路径:', currentPage.route);
    console.log('[DEBUG] 检查是否有待处理的选择动作数据');

    // 尝试从页面数据中获取选择的动作
    if (currentPage.data && currentPage.data.__selectedExercisesData) {
      console.log('[DEBUG] 发现待处理的选择动作数据:',
                  JSON.stringify(currentPage.data.__selectedExercisesData));

      // 处理选择的动作数据
      this.handleSelectedExercises(currentPage.data.__selectedExercisesData);

      // 清除临时数据
      currentPage.setData({
        __selectedExercisesData: null
      });
    }
  },

  // 处理传入的计划数据
  processPlanData(plan) {
    try {
      // 使用工具类处理计划数据
      const planDetail = TrainingPlanHelper.processPlanData(plan);

      // 获取显示信息
      const planDisplayInfo = TrainingPlanHelper.getPlanDisplayInfo(planDetail, plan);

      this.setData({
        planDetail: planDetail,
        plan: planDisplayInfo
      });

      // 使用现有方法处理exercises
      if (planDetail.workouts && planDetail.workouts.length > 0) {
        this.processExercisesFromWorkouts(planDetail.workouts);
      } else {
        this.setData({ isLoading: false });
      }
    } catch (error) {
      console.error('处理计划数据失败:', error);
      wx.showToast({
        title: '数据处理失败',
        icon: 'none'
      });
      this.setData({ isLoading: false });
    }
  },

  /**
   * 准备更新数据，将UI数据结构转换为后端所需的格式
   * @param {Object} planDetail - 计划详情数据
   * @param {Object} options - 额外选项，如状态更新、日期等
   * @returns {Object} 符合后端要求的更新数据结构
   */
  prepareUpdateData(planDetail, options = {}) {
    const planId = this.data.plan.id;
    return TrainingPlanHelper.prepareUpdateData(planDetail, options, planId);
  },

  // 解析display_sets字符串为sets数组
  parseSets(displaySets) {
    return TrainingPlanHelper.parseSets(displaySets);
  },

  // 获取计划详情
  async fetchPlanDetail() {
    try {
      const planId = this.data.plan.id;

      if (!planId) {
        console.error('无法获取计划ID');
        return;
      }

      console.log('获取训练计划详情，planId:', planId);

      // 尝试从缓存获取数据
      const cachedData = imageHelper.getCachedPlanData(planId);
      if (cachedData) {
        console.log('使用缓存的训练计划数据');

        // 确保使用正确的planId (related_plan_id)
        const correctPlanId = cachedData.training_params?.related_plan_id || planId;
        console.log('正确的planId:', correctPlanId);

        // 保存完整的计划详情数据
        this.setData({
          planDetail: cachedData,
          plan: {
            id: correctPlanId, // 使用正确的planId
            title: cachedData.name,
            duration: cachedData.estimated_duration || 60,
            description: cachedData.description || '训练计划详情'
          }
        });

        // 处理exercises
        if (cachedData.workouts && Array.isArray(cachedData.workouts)) {
          this.processExercisesFromWorkouts(cachedData.workouts);
        }

        return cachedData;
      }

      wx.showLoading({ title: '加载计划数据...' });

      // 使用实际的API调用
      const planData = await trainingPlanApi.getPlanDetail(planId);

      // 检查返回的数据
      if (!planData || !planData.id) {
        throw new Error('获取计划详情失败: 数据不完整');
      }

      // 确保使用正确的planId (related_plan_id)
      const correctPlanId = planData.training_params?.related_plan_id || planId;
      console.log('API返回的planData:', planData);
      console.log('正确的planId:', correctPlanId);

      // 保存完整的计划详情数据，用于后续获取训练日ID等
      this.setData({
        planDetail: planData,
        plan: {
          id: correctPlanId, // 使用正确的planId
          title: planData.name,
          duration: planData.estimated_duration || 60,
          description: planData.description || '训练计划详情'
        }
      });

      // 如果有workouts数据，处理exercises
      if (planData.workouts && Array.isArray(planData.workouts)) {
        this.processExercisesFromWorkouts(planData.workouts);
      }

      wx.hideLoading();
      return planData;
    } catch (error) {
      console.error('获取训练计划详情失败:', error);
      wx.hideLoading();
      wx.showToast({
        title: '获取计划详情失败',
        icon: 'none'
      });

      // 返回默认数据
      return null;
    }
  },

  // 从workouts数据中处理exercises
  processExercisesFromWorkouts(workouts) {
    try {
      // 使用工具类处理exercises
      const result = TrainingPlanHelper.processExercisesFromWorkouts(workouts);
      console.log('处理后的运动数据:', result);

      if (result.isMultiDay) {
        // 多日计划
        const planDetail = this.data.planDetail;
        planDetail.workouts = result.planDetail.workouts;

        this.setData({
          planDetail: planDetail,
          isLoading: false
        });
      } else {
        // 单日计划
        this.setData({
          exercises: result.exercises,
          isLoading: false
        });
      }

      // 缓存处理后的数据
      if (this.data.planDetail && this.data.plan && this.data.plan.id) {
        const planId = this.data.plan.id;
        console.log('缓存训练计划数据，使用planId:', planId);
        imageHelper.cachePlanData(planId, this.data.planDetail);
      }
    } catch (error) {
      console.error('处理运动数据失败:', error);
      this.setData({
        exercises: [],
        isLoading: false
      });
    }
  },

  // 计算单个运动的总容量
  calculateTotalVolume(sets) {
    return TrainingPlanHelper.calculateTotalVolume(sets);
  },

  // 切换运动详情的展开状态
  toggleExercise(e) {
    const { index } = e.currentTarget.dataset
    const { exercises } = this.data

    exercises[index].isExpanded = !exercises[index].isExpanded

    this.setData({
      exercises
    })
  },

  // 切换训练日的展开状态
  toggleWorkout(e) {
    const { workoutId } = e.currentTarget.dataset
    const { planDetail } = this.data

    if (!planDetail || !planDetail.workouts) return;

    // 找到对应的workout
    const workoutIndex = planDetail.workouts.findIndex(w => w.id === workoutId);
    if (workoutIndex === -1) return;

    // 切换展开状态
    planDetail.workouts[workoutIndex].isExpanded = !planDetail.workouts[workoutIndex].isExpanded;

    this.setData({
      planDetail
    });
  },

  // 切换训练日中运动的展开状态
  toggleExerciseInWorkout(e) {
    const { workoutId, exerciseIndex } = e.currentTarget.dataset
    const { planDetail } = this.data

    if (!planDetail || !planDetail.workouts) return;

    // 找到对应的workout
    const workoutIndex = planDetail.workouts.findIndex(w => w.id === workoutId);
    if (workoutIndex === -1) return;

    // 找到对应的exercise
    const workout = planDetail.workouts[workoutIndex];
    if (!workout.processedExercises || !workout.processedExercises[exerciseIndex]) return;

    // 切换展开状态
    workout.processedExercises[exerciseIndex].isExpanded = !workout.processedExercises[exerciseIndex].isExpanded;

    this.setData({
      planDetail
    });
  },

  // 开始训练
  startTraining() {
    wx.showLoading({ title: '准备训练中...' });

    const planId = this.data.plan.id;
    const planDetail = this.data.planDetail;

    if (!planDetail) {
      wx.hideLoading();
      wx.showToast({ title: '计划数据不完整，请重试', icon: 'none' });
      return;
    }

    // 使用prepareUpdateData准备更新数据
    const updateData = this.prepareUpdateData(planDetail, {
      status: 'active',
      is_active: true,
      workout_status: 'in_progress',
      add_training_record: true,
      training_record_status: 'in_progress'
    });

    if (!updateData) {
      wx.hideLoading();
      wx.showToast({ title: '准备数据失败，请重试', icon: 'none' });
      return;
    }

    // 获取workout ID
    let workoutId = null;
    if (planDetail.workouts && planDetail.workouts.length > 0) {
      workoutId = planDetail.workouts[0].id; // 使用正确的workoutId
      console.log('【startTraining】获取到workoutId:', workoutId, '类型:', typeof workoutId);
    }

    console.log('【startTraining】准备更新训练计划:', updateData);

    trainingPlanApi.updatePlan(planId, updateData)
      .then(() => {
        // 更新本地planDetail
        if (planDetail.workouts && planDetail.workouts.length > 0) {
          planDetail.workouts[0].status = 'in_progress';
        }

        this.setData({
          planDetail: planDetail,
          needSave: false
        });

        // 如果是从聊天页面打开的，并且有消息ID，则向聊天页面回传数据
        if (this.data.openerPagePath === 'pages/chatbot/index' && this.data.messageId) {
          console.log('准备向聊天页面回传数据，消息ID:', this.data.messageId);

          // 获取打开页面的实例
          const pages = getCurrentPages();
          const prevPage = pages.length > 1 ? pages[pages.length - 2] : null;

          if (prevPage && prevPage.route === 'pages/chatbot/index') {
            console.log('找到聊天页面实例，准备回传数据');

            // 只传递必要的信息
            const statusInfo = {
              status: 'active',
              is_active: true,
              workout_status: 'in_progress'
            };

            // 调用聊天页面的方法更新消息，传递planId而不是整个计划数据
            prevPage.updateTrainingPlanMessage(this.data.messageId, planId, statusInfo);
            console.log('已向聊天页面回传数据');
          }
        }

        wx.hideLoading();

        // 如果没有workoutId，提示用户
        if (!workoutId) {
          wx.showToast({ title: '请先创建训练日', icon: 'none' });
          return;
        }

        // 跳转到训练执行页面
        // 确保workoutId是字符串类型
        const workoutIdStr = String(workoutId);
        const url = `/pages/training/training-session/index?planId=${planId}&workoutId=${workoutIdStr}`;
        console.log('准备跳转到:', url, '传递的workoutId:', workoutIdStr, '类型:', typeof workoutIdStr);

        wx.navigateTo({
          url: url,
          success: () => console.log('页面跳转成功'),
          fail: (error) => {
            console.error('页面跳转失败:', error);
            wx.showToast({ title: '页面跳转失败', icon: 'none' });
          }
        });
      })
      .catch(error => {
        console.error('训练计划更新失败:', error);
        wx.hideLoading();
        wx.showToast({ title: '开始训练失败', icon: 'none' });
      });
  },

  // 处理组类型变化
  handleSetTypeChange(e) {
    const { exerciseIndex, setIndex } = e.currentTarget.dataset;
    const typeValue = parseInt(e.detail.value);
    const typeMap = ['warmup', 'normal', 'decrease'];

    const exercises = this.data.exercises;
    exercises[exerciseIndex].sets[setIndex].type = typeMap[typeValue];

    this.setData({
      exercises,
      needSave: true // 标记需要保存
    });
  },

  // 处理重量变化
  handleWeightChange(e) {
    const { exerciseIndex, setIndex } = e.currentTarget.dataset;
    const weight = parseFloat(e.detail.value) || 0;

    const exercises = this.data.exercises;
    exercises[exerciseIndex].sets[setIndex].weight = weight;
    exercises[exerciseIndex].totalVolume = this.calculateTotalVolume(exercises[exerciseIndex].sets);

    this.setData({
      exercises,
      needSave: true // 标记需要保存
    });
  },

  // 处理次数变化
  handleRepsChange(e) {
    const { exerciseIndex, setIndex } = e.currentTarget.dataset;
    const reps = parseInt(e.detail.value) || 0;

    const exercises = this.data.exercises;
    exercises[exerciseIndex].sets[setIndex].reps = reps;
    exercises[exerciseIndex].totalVolume = this.calculateTotalVolume(exercises[exerciseIndex].sets);

    this.setData({
      exercises,
      needSave: true // 标记需要保存
    });
  },

  // 处理训练日中组类型变更
  handleSetTypeChangeInWorkout(e) {
    const { workoutId, exerciseIndex, setIndex } = e.currentTarget.dataset;
    const typeValue = parseInt(e.detail.value);
    const typeMap = ['warmup', 'normal', 'decrease'];
    const { planDetail } = this.data;

    if (!planDetail || !planDetail.workouts) return;

    // 找到对应的workout
    const workoutIndex = planDetail.workouts.findIndex(w => w.id === workoutId);
    if (workoutIndex === -1) return;

    // 找到对应的exercise
    const workout = planDetail.workouts[workoutIndex];
    if (!workout.processedExercises || !workout.processedExercises[exerciseIndex]) return;

    // 更新组类型
    workout.processedExercises[exerciseIndex].sets[setIndex].type = typeMap[typeValue];

    // 更新总容量
    workout.processedExercises[exerciseIndex].totalVolume = this.calculateTotalVolume(workout.processedExercises[exerciseIndex].sets);

    this.setData({
      planDetail,
      needSave: true // 标记需要保存
    });
  },

  // 处理训练日中重量变更
  handleWeightChangeInWorkout(e) {
    const { workoutId, exerciseIndex, setIndex } = e.currentTarget.dataset;
    const weight = parseFloat(e.detail.value) || 0;
    const { planDetail } = this.data;

    if (!planDetail || !planDetail.workouts) return;

    // 找到对应的workout
    const workoutIndex = planDetail.workouts.findIndex(w => w.id === workoutId);
    if (workoutIndex === -1) return;

    // 找到对应的exercise
    const workout = planDetail.workouts[workoutIndex];
    if (!workout.processedExercises || !workout.processedExercises[exerciseIndex]) return;

    // 更新重量
    workout.processedExercises[exerciseIndex].sets[setIndex].weight = weight;

    // 更新总容量
    workout.processedExercises[exerciseIndex].totalVolume = this.calculateTotalVolume(workout.processedExercises[exerciseIndex].sets);

    this.setData({
      planDetail,
      needSave: true // 标记需要保存
    });
  },

  // 处理训练日中次数变更
  handleRepsChangeInWorkout(e) {
    const { workoutId, exerciseIndex, setIndex } = e.currentTarget.dataset;
    const reps = parseInt(e.detail.value) || 0;
    const { planDetail } = this.data;

    if (!planDetail || !planDetail.workouts) return;

    // 找到对应的workout
    const workoutIndex = planDetail.workouts.findIndex(w => w.id === workoutId);
    if (workoutIndex === -1) return;

    // 找到对应的exercise
    const workout = planDetail.workouts[workoutIndex];
    if (!workout.processedExercises || !workout.processedExercises[exerciseIndex]) return;

    // 更新次数
    workout.processedExercises[exerciseIndex].sets[setIndex].reps = reps;

    // 更新总容量
    workout.processedExercises[exerciseIndex].totalVolume = this.calculateTotalVolume(workout.processedExercises[exerciseIndex].sets);

    this.setData({
      planDetail,
      needSave: true // 标记需要保存
    });
  },

  // 处理加入计划
  handleJoinPlan() {
    // 显示日期选择器
    this.setData({
      datePickerVisible: true
    });
  },

  // 日期选择器确认
  onDatePickerConfirm(e) {
    const { value } = e.detail;
    const selectedDate = new Date(value);
    const formattedDate = `${selectedDate.getFullYear()}-${String(selectedDate.getMonth() + 1).padStart(2, '0')}-${String(selectedDate.getDate()).padStart(2, '0')}`;

    // 隐藏日期选择器
    this.setData({ datePickerVisible: false });
    wx.showLoading({ title: '加入计划中...' });

    const planId = this.data.plan.id;
    const planDetail = this.data.planDetail;

    if (!planDetail) {
      wx.hideLoading();
      wx.showToast({ title: '计划数据不完整，请重试', icon: 'none' });
      return;
    }

    // 计算结束日期 - 基于选择的开始日期和计划的持续周数
    const durationWeeks = planDetail.duration_weeks || 4; // 默认为4周
    const endDate = new Date(selectedDate);
    endDate.setDate(endDate.getDate() + (durationWeeks * 7));
    const formattedEndDate = `${endDate.getFullYear()}-${String(endDate.getMonth() + 1).padStart(2, '0')}-${String(endDate.getDate()).padStart(2, '0')}`;

    // 使用prepareUpdateData准备更新数据
    const updateData = this.prepareUpdateData(planDetail, {
      status: 'active',
      is_active: true,
      start_date: formattedDate,
      end_date: formattedEndDate,
      workout_status: 'not_started',
      scheduled_date: formattedDate
    });

    if (!updateData) {
      wx.hideLoading();
      wx.showToast({ title: '准备数据失败，请重试', icon: 'none' });
      return;
    }

    console.log('【onDatePickerConfirm】准备更新训练计划:', updateData);

    trainingPlanApi.updatePlan(planId, updateData)
      .then(() => {
        // 更新本地planDetail
        if (planDetail.workouts && planDetail.workouts.length > 0) {
          planDetail.workouts[0].scheduled_date = formattedDate;
          planDetail.workouts[0].status = 'not_started';
        }

        // 更新计划的开始日期和结束日期
        planDetail.start_date = formattedDate;
        planDetail.end_date = formattedEndDate;
        planDetail.status = 'active';
        planDetail.is_active = true;

        this.setData({
          planDetail: planDetail,
          needSave: false,
          needRefresh: false // 已经更新了本地数据，不需要刷新
        });

        // 如果是从聊天页面打开的，并且有消息ID，则向聊天页面回传数据
        if (this.data.openerPagePath === 'pages/chatbot/index' && this.data.messageId) {
          console.log('准备向聊天页面回传数据，消息ID:', this.data.messageId);

          // 获取打开页面的实例
          const pages = getCurrentPages();
          const prevPage = pages.length > 1 ? pages[pages.length - 2] : null;

          if (prevPage && prevPage.route === 'pages/chatbot/index') {
            console.log('找到聊天页面实例，准备回传数据');

            // 只传递必要的信息
            const statusInfo = {
              status: 'active',
              is_active: true,
              start_date: formattedDate,
              end_date: formattedEndDate
            };

            // 确保messageId是有效的
            const messageId = this.data.messageId;
            console.log('准备更新训练计划消息，消息ID:', messageId, '类型:', typeof messageId);

            // 调用聊天页面的方法更新消息，传递planId而不是整个计划数据
            prevPage.updateTrainingPlanMessage(messageId, planId, statusInfo);
            console.log('已向聊天页面回传数据，messageId:', messageId, 'planId:', planId);
          }
        }

        wx.hideLoading();
        wx.showToast({
          title: '已加入计划',
          icon: 'success',
          complete: () => {
            // 如果是从聊天页面打开的，则在提示后自动返回聊天页面
            if (this.data.openerPagePath === 'pages/chatbot/index') {
              console.log('准备返回聊天页面');
              setTimeout(() => {
                wx.navigateBack({
                  delta: 1,
                  success: () => console.log('成功返回聊天页面'),
                  fail: (err) => console.error('返回聊天页面失败:', err)
                });
              }, 1000); // 延迟1秒，让用户看到提示
            }
          }
        });
      })
      .catch(error => {
        console.error('训练计划更新失败:', error);
        wx.hideLoading();
        wx.showToast({ title: '加入计划失败', icon: 'none' });
      });
  },

  // 日期选择器取消
  onDatePickerCancel() {
    this.setData({
      datePickerVisible: false
    });

    wx.showToast({
      title: '已取消选择',
      icon: 'none'
    });
  },

  // 导航到选择动作页面
  navigateToSelectExercise() {
    const planId = this.data.plan.id;
    console.log('[DEBUG] 导航到选择动作页面，传递planId:', planId);

    wx.navigateTo({
      url: `/pages/select-training-exercises/index?planId=${planId}`,
      success: (res) => {
        // 监听选择结果 - 修复事件名称
        res.eventChannel.on('addExercises', (data) => {
          console.log('[DEBUG] 接收到选择的动作数据:', JSON.stringify(data));
          console.log('[DEBUG] 当前exercises列表:', JSON.stringify(this.data.exercises));

          if (data && data.exercises && data.exercises.length > 0) {
            console.log('[DEBUG] 准备处理选择的动作数据，数量:', data.exercises.length);
            // 处理选择的动作数据
            this.handleSelectedExercises({
              exercises: data.exercises,
              setType: '正式', // 默认组类型
              planId: planId
            });
          } else {
            console.error('[DEBUG] 接收到的动作数据无效或为空');
          }
        });
      },
      fail: (err) => {
        console.error('页面跳转失败:', err);
        wx.showToast({
          title: '页面跳转失败',
          icon: 'none'
        });
      }
    });
  },

  // 处理选择的动作数据
  handleSelectedExercises(data) {
    console.log('[DEBUG] handleSelectedExercises 开始处理数据:', JSON.stringify(data));

    if (!data) {
      console.error('[DEBUG] 传入的数据为空');
      wx.showToast({
        title: '数据不完整，请重试',
        icon: 'none'
      });
      return;
    }

    const { exercises, setType, planId: receivedPlanId } = data;

    // 检查planId一致性
    const currentPlanId = this.data.plan.id;
    if (receivedPlanId && receivedPlanId !== currentPlanId) {
      console.warn('接收到的planId与当前planId不一致，使用当前planId');
    }

    // 使用工具类处理选择的动作
    const result = TrainingPlanHelper.handleSelectedExercises({
      exercises: exercises,
      setType: setType,
      planDetail: this.data.planDetail,
      currentExercises: this.data.exercises,
      planId: currentPlanId
    });

    if (result.success) {
      // 更新状态
      this.setData({
        exercises: result.newExercises,
        planDetail: result.updatedPlanDetail,
        needRefresh: false,
        needSave: true
      }, () => {
        console.log('[DEBUG] 数据更新完成，exercises数量:', this.data.exercises.length);
      });

      // 缓存更新后的计划数据
      imageHelper.cachePlanData(currentPlanId, this.data.planDetail);

      wx.showToast({
        title: '已添加动作',
        icon: 'success'
      });
    } else {
      console.error('[DEBUG] 处理选择的动作失败:', result.error);
      wx.showToast({
        title: '添加动作失败',
        icon: 'none'
      });
    }
  },

  /**
   * 保存训练计划变更
   * 将UI中的exercises数据同步到planDetail，并发送到后端
   */
  async saveTrainingPlanChanges() {
    console.log('[DEBUG] 保存训练计划变更');

    if (!this.data.planDetail || !this.data.planDetail.workouts) {
      console.log('[DEBUG] 没有计划详情或workout数据，无法保存');
      return;
    }

    try {
      wx.showLoading({
        title: '保存中...',
        mask: true
      });

      // 构建更新数据
      const updateData = this.prepareUpdateData(this.data.planDetail);
      const planId = this.data.plan.id;

      console.log('[DEBUG] 准备更新的数据:', updateData);

      // 调用更新API
      const result = await trainingPlanApi.updateTrainingPlan(planId, updateData);

      console.log('[DEBUG] 更新结果:', result);

      wx.hideLoading();

      // 标记为已保存
      this.setData({
        needSave: false
      });

      wx.showToast({
        title: '保存成功',
        icon: 'success'
      });

    } catch (error) {
      console.error('[DEBUG] 保存训练计划失败:', error);
      wx.hideLoading();
      wx.showToast({
        title: '保存失败',
        icon: 'none'
      });
    }
  },

  /**
   * 设置模板创建模式
   */
  setupTemplateMode(templateData) {
    console.log('设置模板创建模式:', templateData);

    // 设置导航栏标题
    wx.setNavigationBarTitle({
      title: '创建训练模板'
    });

    // 生成临时ID
    const templateId = 'template_' + Date.now();
    const workoutId = 'workout_' + Date.now();

    // 创建默认的workout结构，用于模板创建模式
    const defaultWorkout = {
      id: workoutId,
      training_plan_id: templateId,
      name: templateData.name || '训练模板',
      day_of_week: 1,
      day_number: 1,
      description: templateData.description || '',
      scheduled_date: new Date().toISOString().split('T')[0],
      status: "not_started",
      exercises: [] // 空的exercises数组，用于存放添加的动作
    };

    // 初始化模板数据
    this.setData({
      templateData: templateData,
      plan: {
        id: templateId, // 临时ID
        name: templateData.name,
        estimated_duration: templateData.estimated_duration,
        description: templateData.description
      },
      exercises: [],
      planDetail: {
        id: templateId,
        name: templateData.name,
        estimated_duration: templateData.estimated_duration,
        description: templateData.description,
        workouts: [defaultWorkout] // 包含一个默认workout
      }
    });

    console.log('模板创建模式初始化完成，planDetail:', this.data.planDetail);
  },

  /**
   * 设置编辑模板模式（统一的模板编辑模式，避免数据丢失）
   */
  setupEditTemplateMode(templateData) {
    console.log('设置编辑模板模式:', templateData);

    // 设置导航栏标题
    wx.setNavigationBarTitle({
      title: `编辑模板 - ${templateData.name || '训练模板'}`
    });

    // 如果模板有exercises数据，需要转换为plan-detail页面的数据格式
    const processedExercises = [];

    if (templateData.exercises && templateData.exercises.length > 0) {
      templateData.exercises.forEach((exercise, index) => {
        // 使用工具类创建标准运动对象
        const standardExercise = ExerciseDataHelper.createStandardExercise({
          ...exercise,
          id: exercise.exercise_id || exercise.id || `template_exercise_${index}`,
          name: exercise.exercise_name || exercise.name,
          image_url: exercise.image_url || exercise.imageUrl,
          exercise_description: exercise.exercise_description || exercise.category,
          sets: exercise.sets || 3,
          reps: exercise.reps || 12,
          weight: exercise.weight || 20
        }, {
          calculateTotalVolume: this.calculateTotalVolume
        });

        processedExercises.push(standardExercise);
      });
    }

    // 创建模拟的workout结构用于显示
    const templateId = templateData.templateId || templateData.id;
    const workoutId = 'template_workout_' + templateId;

    const workout = {
      id: workoutId,
      training_plan_id: templateId,
      name: templateData.name || '模板训练',
      day_of_week: 1,
      day_number: 1,
      description: templateData.description || '',
      scheduled_date: new Date().toISOString().split('T')[0],
      status: "template",
      exercises: processedExercises.map((exercise, index) => {
        const setCount = exercise.sets.length;
        const defaultReps = 12;
        const defaultWeight = 20;

        // 从模板数据中正确提取ID
        // exercise.id 是 workout_exercise ID (如 221)
        // exercise.exercise_id 是实际的 exercise ID (如 8)
        const workoutExerciseId = exercise.id; // 这是 workout exercise 的 ID
        const actualExerciseId = exercise.exercise_id || exercise.id; // 这是实际的 exercise ID

        return {
          id: workoutExerciseId, // 使用 workout exercise ID
          exercise_id: actualExerciseId, // 使用实际的 exercise ID
          workout_id: workoutId,
          sets: setCount,
          reps: defaultReps,
          rest_seconds: 60,
          order: index + 1,
          notes: "",
          exercise_type: "weight_reps",
          weight: defaultWeight,
          exercise_name: exercise.name,
          image_url: exercise.imageUrl,
          exercise_description: exercise.category,
          set_records: exercise.sets.map((set, setIndex) => ({
            id: `${workoutExerciseId}_set_${setIndex + 1}`,
            workout_exercise_id: workoutExerciseId, // 使用正确的 workout exercise ID
            set_number: setIndex + 1,
            set_type: set.type,
            weight: set.weight,
            reps: set.reps,
            completed: set.completed,
            notes: ""
          }))
        };
      })
    };

    // 确保所有必要的数据都被保留，特别是estimated_duration
    const estimatedDuration = templateData.estimated_duration || 60;

    // 设置数据，确保所有字段都有值
    this.setData({
      templateData: {
        ...templateData,
        estimated_duration: estimatedDuration // 确保estimated_duration有值
      },
      templateId: templateId,
      plan: {
        id: templateId,
        name: templateData.name || '训练模板',
        estimated_duration: estimatedDuration, // 确保plan中也有estimated_duration
        description: templateData.description || ''
      },
      exercises: processedExercises,
      planDetail: {
        id: templateId,
        name: templateData.name || '训练模板',
        estimated_duration: estimatedDuration, // 确保planDetail中也有estimated_duration
        description: templateData.description || '',
        workouts: [workout]
      }
    });

    console.log('编辑模板模式初始化完成，exercises数量:', processedExercises.length);
    console.log('模板数据estimated_duration:', estimatedDuration);
  },

  /**
   * 设置模板查看模式
   */
  setupViewTemplateMode(templateData) {
    console.log('设置模板查看模式:', templateData);

    // 设置导航栏标题
    wx.setNavigationBarTitle({
      title: templateData.name || '模板详情'
    });

    // 如果模板有exercises数据，需要转换为plan-detail页面的数据格式
    const processedExercises = [];

    if (templateData.exercises && templateData.exercises.length > 0) {
      templateData.exercises.forEach((exercise, index) => {
        // 使用工具类创建标准运动对象
        const standardExercise = ExerciseDataHelper.createStandardExercise({
          ...exercise,
          id: exercise.exercise_id || exercise.id || `template_exercise_${index}`,
          name: exercise.exercise_name || exercise.name,
          image_url: exercise.image_url || exercise.imageUrl,
          exercise_description: exercise.exercise_description || exercise.category,
          sets: exercise.sets || 3,
          reps: exercise.reps || 12,
          weight: exercise.weight || 20
        }, {
          calculateTotalVolume: this.calculateTotalVolume
        });

        processedExercises.push(standardExercise);
      });
    }

    // 创建模拟的workout结构用于显示
    const templateId = templateData.templateId || templateData.id;
    const workoutId = 'template_workout_' + templateId;

    const workout = {
      id: workoutId,
      training_plan_id: templateId,
      name: templateData.name || '模板训练',
      day_of_week: 1,
      day_number: 1,
      description: templateData.description || '',
      scheduled_date: new Date().toISOString().split('T')[0],
      status: "template",
      exercises: processedExercises.map((exercise, index) => {
        const setCount = exercise.sets.length;
        const defaultReps = 12;
        const defaultWeight = 20;

        // 从模板数据中正确提取ID
        // exercise.id 是 workout_exercise ID (如 221)
        // exercise.exercise_id 是实际的 exercise ID (如 8)
        const workoutExerciseId = exercise.id; // 这是 workout exercise 的 ID
        const actualExerciseId = exercise.exercise_id || exercise.id; // 这是实际的 exercise ID

        return {
          id: workoutExerciseId, // 使用 workout exercise ID
          exercise_id: actualExerciseId, // 使用实际的 exercise ID
          workout_id: workoutId,
          sets: setCount,
          reps: defaultReps,
          rest_seconds: 60,
          order: index + 1,
          notes: "",
          exercise_type: "weight_reps",
          weight: defaultWeight,
          exercise_name: exercise.name,
          image_url: exercise.imageUrl,
          exercise_description: exercise.category,
          set_records: exercise.sets.map((set, setIndex) => ({
            id: `${workoutExerciseId}_set_${setIndex + 1}`,
            workout_exercise_id: workoutExerciseId, // 使用正确的 workout exercise ID
            set_number: setIndex + 1,
            set_type: set.type,
            weight: set.weight,
            reps: set.reps,
            completed: set.completed,
            notes: ""
          }))
        };
      })
    };

    // 设置数据
    this.setData({
      templateData: templateData,
      templateId: templateId,
      plan: {
        id: templateId,
        name: templateData.name,
        estimated_duration: templateData.estimated_duration,
        description: templateData.description
      },
      exercises: processedExercises,
      planDetail: {
        id: templateId,
        name: templateData.name,
        estimated_duration: templateData.estimated_duration,
        description: templateData.description,
        workouts: [workout]
      }
    });

    console.log('模板查看模式初始化完成，exercises数量:', processedExercises.length);
  },

  /**
   * 保存为模板
   */
  async saveAsTemplate() {
    // 检查是否有训练动作
    if (!this.data.exercises || this.data.exercises.length === 0) {
      wx.showToast({
        title: '请先添加训练动作',
        icon: 'none'
      });
      return;
    }

    try {
      wx.showLoading({
        title: this.data.templateId ? '更新模板中...' : '保存模板中...',
        mask: true
      });

      // 准备模板数据
      const workoutTemplatesApi = require('../../../api/workout-templates');

      const templateData = {
        name: this.data.templateData?.name || this.data.plan?.name || '训练模板',
        description: this.data.templateData?.description || this.data.plan?.description || '',
        estimated_duration: this.data.templateData?.estimated_duration || this.data.plan?.estimated_duration || 60,
        visibility: 'private',
        exercises: this.prepareTemplateExercisesData()
      };

      console.log('保存模板数据:', templateData);

      if (this.data.templateId) {
        // 更新现有模板
        console.log('更新模板ID:', this.data.templateId);
        await workoutTemplatesApi.updateWorkoutTemplate(this.data.templateId, templateData);

        wx.showToast({
          title: '模板更新成功',
          icon: 'success'
        });
      } else {
        // 创建新模板
        await workoutTemplatesApi.createWorkoutTemplate(templateData);

        wx.showToast({
          title: '模板保存成功',
          icon: 'success'
        });
      }

      wx.hideLoading();

      // 延迟返回上一页
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);

    } catch (error) {
      console.error('保存模板失败:', error);
      wx.hideLoading();
      wx.showToast({
        title: this.data.templateId ? '更新失败' : '保存失败',
        icon: 'none'
      });
    }
  },

  /**
   * 开始训练（在模板模式下也保存模板）
   */
  async startTrainingInTemplateMode() {
    // 先保存模板
    await this.saveAsTemplate();

    // 如果保存成功，不需要额外操作，因为已经在saveAsTemplate中返回了
  },

  /**
   * 编辑模板
   */
  editTemplate() {
    console.log('切换到编辑模式');

    // 设置导航栏标题
    wx.setNavigationBarTitle({
      title: '编辑模板'
    });

    // 切换到编辑模式
    this.setData({
      isViewTemplateMode: false,
      isEditTemplateMode: true
    });

    wx.showToast({
      title: '已进入编辑模式',
      icon: 'success'
    });
  },

  // 删除训练动作
  deleteExercise(e) {
    const { index } = e.currentTarget.dataset;
    const exercise = this.data.exercises[index];

    wx.showModal({
      title: '删除动作',
      content: `确定要删除"${exercise.name}"及其所有组数据吗？`,
      confirmText: '删除',
      confirmColor: '#F44336',
      success: (res) => {
        if (res.confirm) {
          const exercises = [...this.data.exercises];

          // 从列表中移除该运动
          exercises.splice(index, 1);

          // 同时从planDetail中移除
          const planDetail = this.data.planDetail;
          if (planDetail && planDetail.workouts && planDetail.workouts[0] && planDetail.workouts[0].exercises) {
            planDetail.workouts[0].exercises.splice(index, 1);
          }

          // 更新状态
          this.setData({
            exercises,
            planDetail,
            needSave: true
          });

          // 显示提示
          wx.showToast({
            title: '已删除动作',
            icon: 'success'
          });
        }
      }
    });
  },

  // 添加新组
  addNewSet(e) {
    const { exerciseIndex } = e.currentTarget.dataset;
    const exercises = [...this.data.exercises];
    const exercise = exercises[exerciseIndex];

    let newSet;

    // 检查是否有现有组数据
    if (exercise.sets.length === 0) {
      // 如果没有现有组，创建默认组数据
      newSet = {
        id: Date.now() + '_' + Math.floor(Math.random() * 1000),
        weight: 20, // 默认重量 20kg
        reps: 12,   // 默认次数 12次
        type: 'normal', // 默认为正式组
        completed: false
      };
    } else {
      // 获取最后一组的数据作为参考
      const lastSet = exercise.sets[exercise.sets.length - 1];

      // 创建新组，从最后一组复制数据，但状态为未完成
      newSet = {
        id: Date.now() + '_' + Math.floor(Math.random() * 1000),
        weight: lastSet.weight,
        reps: lastSet.reps,
        type: lastSet.type,
        completed: false
      };
    }

    // 添加新组
    exercise.sets.push(newSet);

    // 重新计算总容量
    exercise.totalVolume = this.calculateTotalVolume(exercise.sets);

    // 同时更新planDetail中的数据
    const planDetail = this.data.planDetail;
    if (planDetail && planDetail.workouts && planDetail.workouts[0] && planDetail.workouts[0].exercises[exerciseIndex]) {
      const workoutExercise = planDetail.workouts[0].exercises[exerciseIndex];
      workoutExercise.sets = exercise.sets.length;

      // 添加set_record
      if (!workoutExercise.set_records) {
        workoutExercise.set_records = [];
      }
      workoutExercise.set_records.push({
        id: newSet.id,
        workout_exercise_id: workoutExercise.id,
        set_number: exercise.sets.length,
        set_type: newSet.type,
        weight: newSet.weight,
        reps: newSet.reps,
        completed: newSet.completed,
        notes: ""
      });
    }

    this.setData({
      exercises,
      planDetail,
      needSave: true
    });

    wx.showToast({
      title: '已添加新组',
      icon: 'success'
    });
  },

  // 删除组
  deleteSet(e) {
    const { exerciseIndex, setIndex } = e.currentTarget.dataset;
    const exercises = [...this.data.exercises];
    const exercise = exercises[exerciseIndex];

    // 检查是否至少保留一组
    if (exercise.sets.length <= 1) {
      wx.showToast({
        title: '至少需要保留一组',
        icon: 'none'
      });
      return;
    }

    // 从运动中移除该组
    exercise.sets.splice(setIndex, 1);

    // 重新计算总容量
    exercise.totalVolume = this.calculateTotalVolume(exercise.sets);

    // 同时更新planDetail中的数据
    const planDetail = this.data.planDetail;
    if (planDetail && planDetail.workouts && planDetail.workouts[0] && planDetail.workouts[0].exercises[exerciseIndex]) {
      const workoutExercise = planDetail.workouts[0].exercises[exerciseIndex];
      workoutExercise.sets = exercise.sets.length;

      // 更新set_records
      if (workoutExercise.set_records && workoutExercise.set_records[setIndex]) {
        workoutExercise.set_records.splice(setIndex, 1);

        // 重新编号剩余的set_records
        workoutExercise.set_records.forEach((record, index) => {
          record.set_number = index + 1;
        });
      }
    }

    // 更新状态
    this.setData({
      exercises,
      planDetail,
      needSave: true
    });

    wx.showToast({
      title: '已删除组',
      icon: 'success'
    });
  },

  /**
   * 准备模板运动数据，正确处理组记录ID
   */
  prepareTemplateExercisesData() {
    return this.data.exercises.map((exercise, exerciseIndex) => {
      // 处理组记录数据，确保ID的正确性
      console.log(exercise)
      const setRecords = this.prepareSetRecordsForTemplate(exercise, exerciseIndex);

      // 计算基础数据（使用第一组的数据作为默认值）
      const firstSet = exercise.sets && exercise.sets.length > 0 ? exercise.sets[0] : {};
      const defaultReps = firstSet.reps || 12;
      const defaultWeight = firstSet.weight || 0;

      // 正确区分 workout exercise ID 和 actual exercise ID
      const actualExerciseId = exercise.exercise_id || exercise.id;

      console.log(`[prepareTemplateExercisesData] 运动 ${exerciseIndex}: actualExerciseId=${actualExerciseId}`);

      return {
        exercise_id: actualExerciseId, // 使用实际的 exercise ID
        order: exerciseIndex + 1,
        sets: exercise.sets?.length || 3,
        reps: defaultReps,
        weight: defaultWeight,
        rest_seconds: exercise.rest_seconds || 60,
        notes: exercise.notes || '',
        exercise_type: exercise.exercise_type || 'weight_reps',
        set_records: setRecords // 包含完整的组记录数据
      };
    });
  },

  /**
   * 为模板准备组记录数据，处理ID生成逻辑
   */
  prepareSetRecordsForTemplate(exercise, exerciseIndex) {
    if (!exercise.sets || exercise.sets.length === 0) {
      return [];
    }

    // 正确区分 workout exercise ID 和 actual exercise ID
    const workoutExerciseId = exercise.id; // 这是 workout exercise 的 ID (如 221)
    const actualExerciseId = exercise.exercise_id || exercise.id; // 这是实际的 exercise ID (如 8)
    const timestamp = Date.now();

    console.log(`[prepareSetRecordsForTemplate] 运动 ${exerciseIndex}: workoutExerciseId=${workoutExerciseId}, actualExerciseId=${actualExerciseId}`);

    return exercise.sets.map((set, setIndex) => {
      // 检查组记录是否已有有效的ID
      let setRecordId = set.id;

      // 如果没有ID或者是临时ID，生成新的唯一ID
      if (!setRecordId || this.isTemporaryId(setRecordId)) {
        // 生成唯一的组记录ID，使用 workout exercise ID
        const randomSuffix = Math.floor(Math.random() * 10000);
        setRecordId = `${workoutExerciseId}_set_${timestamp}_${setIndex}_${randomSuffix}`;

        console.log(`为运动 ${exerciseIndex} 的第 ${setIndex + 1} 组生成新ID:`, setRecordId);
      } else {
        console.log(`运动 ${exerciseIndex} 的第 ${setIndex + 1} 组使用现有ID:`, setRecordId);
      }

      return {
        id: setRecordId,
        workout_exercise_id: workoutExerciseId, // 使用正确的 workout exercise ID
        set_number: setIndex + 1,
        set_type: set.type || 'normal',
        weight: set.weight || 0,
        reps: set.reps || 0,
        completed: set.completed || false,
        notes: set.notes || ''
      };
    });
  },

  /**
   * 检查ID是否为临时ID
   */
  isTemporaryId(id) {
    if (!id) return true;

    const idStr = String(id);

    // 检查是否为临时ID的模式
    return (
      idStr.startsWith('temp_') ||           // 临时ID前缀
      idStr.includes('_temp_') ||            // 包含临时标识
      idStr.match(/^\d+_\d+$/) ||           // 简单的时间戳_随机数格式
      idStr.startsWith('template_exercise_') // 模板运动临时ID
    );
  },
})