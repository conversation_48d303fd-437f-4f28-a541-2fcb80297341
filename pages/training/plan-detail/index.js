const app = getApp()
// 导入API模块
const trainingPlanApi = require('../../../api/training-plan')
// 导入图片处理工具
const imageHelper = require('../../../utils/image-helper')

Page({
  data: {
    plan: {
      id: '',
      name: '',
      estimated_duration: 0,
      description: '',
    },
    exercises: [],
    isLoading: true,
    needRefresh: false,
    needSave: false, // 标记是否需要保存
    planDetail: null, // 存储完整的计划详情数据
    datePickerVisible: false,
    minDate: new Date().getTime(),
    maxDate: new Date(new Date().setFullYear(new Date().getFullYear() + 1)).getTime(), // 最大日期为一年后
    messageId: null, // 存储聊天消息ID，用于更新meta_info
    openerPagePath: null, // 存储打开页面的路径
    
    // 模板创建相关字段
    isCreateTemplateMode: false,
    templateData: null, // 存储模板数据
    
    // 模板查看和编辑相关字段
    isViewTemplateMode: false,
    isEditTemplateMode: false,
    templateId: null // 存储模板ID用于更新
  },

  onLoad: function(options) {
    console.log('plan-detail onLoad options:', options);
    
    // 检查是否为创建模板模式
    if (options.mode === 'createTemplate') {
      this.setData({
        isCreateTemplateMode: true,
        isLoading: false
      });
      
      // 监听模板数据
      const eventChannel = this.getOpenerEventChannel();
      if (eventChannel) {
        eventChannel.on('templateData', (templateData) => {
          console.log('接收到模板数据:', templateData);
          this.setupTemplateMode(templateData);
        });
      }
      return;
    }
    
    // 检查是否为查看模板模式
    if (options.mode === 'viewTemplate' && options.templateId) {
      this.setData({
        isViewTemplateMode: true,
        templateId: options.templateId,
        isLoading: false
      });
      
      // 监听模板数据
      const eventChannel = this.getOpenerEventChannel();
      if (eventChannel) {
        eventChannel.on('templateData', (templateData) => {
          console.log('接收到查看模板数据:', templateData);
          this.setupViewTemplateMode(templateData);
        });
      }
      return;
    }
    
    if (options.planId) {
      // 保存计划ID
      this.setData({
        plan: {
          id: options.planId
        },
        isLoading: true
      });

      // 获取打开页面的路径
      const pages = getCurrentPages();
      const prevPage = pages.length > 1 ? pages[pages.length - 2] : null;
      const openerPagePath = prevPage ? prevPage.route : null;

      console.log('打开页面的路径:', openerPagePath);

      // 保存打开页面的路径
      this.setData({
        openerPagePath: openerPagePath
      });

      // 尝试从页面参数中获取计划数据
      const eventChannel = this.getOpenerEventChannel();
      if (eventChannel) {
        eventChannel.on('acceptDataFromOpenerPage', (data) => {
          if (data && data.plan) {
            console.log('接收到的计划数据:', data.plan);

            // 保存消息ID（如果存在）
            if (data.messageId) {
              console.log('接收到的消息ID:', data.messageId);
              this.setData({
                messageId: data.messageId
              });
            }

            this.processPlanData(data.plan);
          } else {
            this.fetchPlanDetail();
          }
        });
      } else {
        // 如果没有eventChannel，直接获取数据
        this.fetchPlanDetail();
      }
    } else {
      wx.showToast({
        title: '缺少计划ID',
        icon: 'none'
      });
    }
  },

  onShow() {
    console.log('[DEBUG] onShow 触发，needRefresh:', this.data.needRefresh);

    // 检查是否需要刷新
    if (this.data.needRefresh) {
      console.log('[DEBUG] 页面显示，需要刷新数据');
      this.fetchPlanDetail();
      this.setData({
        needRefresh: false
      });
    }

    // 检查是否有待处理的选择动作数据
    const pages = getCurrentPages();
    const currentPage = pages[pages.length - 1];

    console.log('[DEBUG] 当前页面路径:', currentPage.route);
    console.log('[DEBUG] 检查是否有待处理的选择动作数据');

    // 尝试从页面数据中获取选择的动作
    if (currentPage.data && currentPage.data.__selectedExercisesData) {
      console.log('[DEBUG] 发现待处理的选择动作数据:',
                  JSON.stringify(currentPage.data.__selectedExercisesData));

      // 处理选择的动作数据
      this.handleSelectedExercises(currentPage.data.__selectedExercisesData);

      // 清除临时数据
      currentPage.setData({
        __selectedExercisesData: null
      });
    }
  },

  // 处理传入的计划数据
  processPlanData(plan) {
    try {
      console.log('处理计划数据:', plan);

      // 创建一个包含workouts的planDetail
      const planDetail = {
        ...plan
      };

      // 确保workouts字段存在且是数组
      if (!planDetail.workouts || !Array.isArray(planDetail.workouts)) {
        planDetail.workouts = [];
      }

      // 如果存在training_params，确保related_plan_id是plan的id
      if (plan.training_params) {
        planDetail.training_params = {
          ...plan.training_params
        };
      }

      // 如果workouts为空但有exercises，创建一个模拟的workout包含这些exercises
      if (planDetail.workouts.length === 0 && plan.exercises && plan.exercises.length > 0) {
        console.log('没有workouts但有exercises，创建模拟workout');

        // 创建一个默认workout
        const workout = {
          id: plan.id, // 生成一个临时ID
          training_plan_id: plan.training_params?.related_plan_id || plan.id,
          name: plan.name,
          day_of_week: 1,
          day_number: 1,
          description: plan.description,
          scheduled_date: new Date().toISOString().split('T')[0],
          status: "not_started",
          exercises: []
        };

        // 转换exercises为workout格式
        plan.exercises.forEach((exercise, index) => {
          // 解析组数和次数
          const setsReps = this.parseSets(exercise.display_sets);
          const setCount = setsReps.setCount || 3;
          const reps = setsReps.reps || 12;

          // 创建workout exercise
          const workoutExercise = {
            id: exercise.id, // 保留exercise原始ID
            exercise_id: exercise.exercise_id || exercise.id,
            workout_id: workout.id,
            sets: setCount,
            reps: reps,
            rest_seconds: 60,
            order: index + 1,
            notes: exercise.notes || "",
            exercise_type: "weight_reps",
            weight: exercise.weight || 20,
            exercise_name: exercise.name,
            image_url: exercise.image_url || '/images/exercises/default.png',
            exercise_description: exercise.description || "",
            set_records: []
          };

          // 创建set_records
          for (let i = 0; i < setCount; i++) {
            workoutExercise.set_records.push({
              id: `${exercise.id}_set_${i+1}`,
              workout_exercise_id: exercise.id,
              set_number: i + 1,
              set_type: i === 0 ? 'warmup' : 'normal',
              weight: i === 0 ? (exercise.weight || 20) * 0.8 : (exercise.weight || 20),
              reps: i === 0 ? reps + 2 : reps,
              completed: false,
              notes: ""
            });
          }

          // 添加到workout
          workout.exercises.push(workoutExercise);
        });

        // 添加workout到planDetail
        planDetail.workouts.push(workout);
      }

      // 确保每个workout中的exercises都有正确的数据
      if (planDetail.workouts && planDetail.workouts.length > 0) {
        console.log('处理workouts中的exercises数据');

        planDetail.workouts.forEach(workout => {
          // 确保exercises字段存在且是数组
          if (!workout.exercises || !Array.isArray(workout.exercises)) {
            console.log(`Workout ${workout.id} 没有exercises数组，创建空数组`);
            workout.exercises = [];
          } else {
            console.log(`Workout ${workout.id} 有 ${workout.exercises.length} 个exercises`);
          }
        });
      }
      console.log('处理后的计划详情:', planDetail);
      // 保存完整计划详情
      // 根据workouts长度决定显示内容
      let title, description, duration;

      // 根据workouts长度决定显示内容
      if (planDetail.workouts && planDetail.workouts.length === 1) {
        // 单日计划：显示workout的name作为标题
        const workout = planDetail.workouts[0];
        title = workout.name || plan.name || '训练计划';
        description = workout.description || plan.description || '';
        duration = workout.estimated_duration || plan.estimated_duration || 60;
        console.log('单日计划，使用workout的name作为标题:', title);
      } else {
        // 多日计划：显示计划的name作为标题
        title = plan.plan_name || plan.name || '训练计划';
        description = plan.description || '';
        duration = plan.estimated_duration || 60;
        console.log('多日计划，使用plan的name作为标题:', title);
      }

      this.setData({
        planDetail: planDetail,
        plan: {
          id: plan.training_params?.related_plan_id || plan.id,
          title: title,
          duration: duration,
          description: description
        }
      });

      // 使用现有方法处理exercises
      if (planDetail.workouts && planDetail.workouts.length > 0) {
        this.processExercisesFromWorkouts(planDetail.workouts);
      } else {
        this.setData({ isLoading: false });
      }
    } catch (error) {
      console.error('处理计划数据失败:', error);
      wx.showToast({
        title: '数据处理失败',
        icon: 'none'
      });
      this.setData({ isLoading: false });
    }
  },

  /**
   * 准备更新数据，将UI数据结构转换为后端所需的格式
   * @param {Object} planDetail - 计划详情数据
   * @param {Object} options - 额外选项，如状态更新、日期等
   * @returns {Object} 符合后端要求的更新数据结构
   */
  prepareUpdateData(planDetail, options = {}) {
    try {
      if (!planDetail || !planDetail.id) {
        console.error('计划数据不完整');
        return null;
      }
      console.log('【prepareUpdateData】处理计划数据:', planDetail);

      // 获取正确的planId
      const planId = this.data.plan.id;
      console.log('准备更新数据，使用planId:', planId);

      // 初始化更新数据结构
      const updateData = {
        plan_data: {
          id: planId // 使用正确的planId
        },
        workout_data: [],
        workout_exercise_data: [],
        training_record_data: [],
        set_record_data: []
      };

      // 更新计划基本信息
      if (options.status) {
        updateData.plan_data.status = options.status;
      }

      if (options.is_active !== undefined) {
        updateData.plan_data.is_active = options.is_active;
      }

      if (options.start_date) {
        updateData.plan_data.start_date = options.start_date;
      }

      if (options.end_date) {
        updateData.plan_data.end_date = options.end_date;
      }

      // 如果有workouts数据，处理workout相关数据
      if (planDetail.workouts && planDetail.workouts.length > 0) {
        planDetail.workouts.forEach(workout => {
          // 添加workout数据
          const workoutData = {
            id: workout.id
          };

          // 更新workout状态
          if (options.workout_status) {
            workoutData.status = options.workout_status;
          }

          // 更新workout日期
          if (options.scheduled_date) {
            workoutData.scheduled_date = options.scheduled_date;
          }

          updateData.workout_data.push(workoutData);

          // 处理workout exercises
          if (workout.exercises && workout.exercises.length > 0) {
            workout.exercises.forEach(exercise => {
              // 添加workout exercise数据
              const exerciseData = {
                id: exercise.id,
                workout_id: workout.id
              };

              // 检查是否是新添加的动作（ID以added_exercise开头）
              const isNewExercise = exercise.id && String(exercise.id).startsWith('added_exercise');
              console.log('[DEBUG] 处理workout_exercise:', exercise.id, '是否为新添加动作:', isNewExercise);

              // 对于新添加的动作，必须包含exercise_id字段
              if (isNewExercise) {
                exerciseData.exercise_id = exercise.exercise_id;
                console.log('[DEBUG] 添加exercise_id字段:', exercise.exercise_id);
              } else if (exercise.exercise_id) {
                // 对于非新添加的动作，如果有exercise_id也添加
                exerciseData.exercise_id = exercise.exercise_id;
              }

              // 如果有更新，添加相应字段
              if (exercise.sets) exerciseData.sets = exercise.sets;
              if (exercise.reps) exerciseData.reps = exercise.reps;
              if (exercise.rest_seconds) exerciseData.rest_seconds = exercise.rest_seconds;
              if (exercise.notes) exerciseData.notes = exercise.notes;
              if (exercise.weight) exerciseData.weight = exercise.weight;

              updateData.workout_exercise_data.push(exerciseData);

              // 处理set records
              if (exercise.set_records && exercise.set_records.length > 0) {
                exercise.set_records.forEach(record => {
                  // 添加set record数据
                  const recordData = {
                    id: record.id,
                    workout_exercise_id: exercise.id
                  };

                  // 如果record中有自己的workout_exercise_id字段，优先使用它
                  if (record.workout_exercise_id) {
                    recordData.workout_exercise_id = record.workout_exercise_id;
                    console.log('[DEBUG] 使用record中的workout_exercise_id:', record.workout_exercise_id);
                  }

                  // 如果有更新，添加相应字段
                  if (record.set_type) recordData.set_type = record.set_type;
                  if (record.weight !== undefined) recordData.weight = record.weight;
                  if (record.reps !== undefined) recordData.reps = record.reps;
                  if (record.completed !== undefined) recordData.completed = record.completed;
                  if (record.notes) recordData.notes = record.notes;

                  updateData.set_record_data.push(recordData);
                });
              }
            });
          }

          // 如果需要添加训练记录
          if (options.add_training_record) {
            updateData.training_record_data.push({
              workout_id: workout.id,
              status: options.training_record_status || 'not_started'
            });
          }
        });
      }

      // 移除空数组，减少数据传输量
      if (updateData.workout_data.length === 0) delete updateData.workout_data;
      if (updateData.workout_exercise_data.length === 0) delete updateData.workout_exercise_data;
      if (updateData.training_record_data.length === 0) delete updateData.training_record_data;
      if (updateData.set_record_data.length === 0) delete updateData.set_record_data;

      console.log('准备的更新数据:', updateData);
      return updateData;
    } catch (error) {
      console.error('准备更新数据失败:', error);
      return null;
    }
  },

  // 解析display_sets字符串为sets数组
  parseSets(displaySets) {
    try {
      // 解析格式如 "3组x12次" 的字符串
      const match = displaySets ? displaySets.match(/(\d+)组x(\d+)次/) : null;
      if (!match) return { setCount: 3, reps: 12 };

      return {
        setCount: parseInt(match[1]),
        reps: parseInt(match[2])
      };
    } catch (error) {
      console.error('解析组数据失败:', error);
      return { setCount: 3, reps: 12 };
    }
  },

  // 获取计划详情
  async fetchPlanDetail() {
    try {
      const planId = this.data.plan.id;

      if (!planId) {
        console.error('无法获取计划ID');
        return;
      }

      console.log('获取训练计划详情，planId:', planId);

      // 尝试从缓存获取数据
      const cachedData = imageHelper.getCachedPlanData(planId);
      if (cachedData) {
        console.log('使用缓存的训练计划数据');

        // 确保使用正确的planId (related_plan_id)
        const correctPlanId = cachedData.training_params?.related_plan_id || planId;
        console.log('正确的planId:', correctPlanId);

        // 保存完整的计划详情数据
        this.setData({
          planDetail: cachedData,
          plan: {
            id: correctPlanId, // 使用正确的planId
            title: cachedData.name,
            duration: cachedData.estimated_duration || 60,
            description: cachedData.description || '训练计划详情'
          }
        });

        // 处理exercises
        if (cachedData.workouts && Array.isArray(cachedData.workouts)) {
          this.processExercisesFromWorkouts(cachedData.workouts);
        }

        return cachedData;
      }

      wx.showLoading({ title: '加载计划数据...' });

      // 使用实际的API调用
      const planData = await trainingPlanApi.getPlanDetail(planId);

      // 检查返回的数据
      if (!planData || !planData.id) {
        throw new Error('获取计划详情失败: 数据不完整');
      }

      // 确保使用正确的planId (related_plan_id)
      const correctPlanId = planData.training_params?.related_plan_id || planId;
      console.log('API返回的planData:', planData);
      console.log('正确的planId:', correctPlanId);

      // 保存完整的计划详情数据，用于后续获取训练日ID等
      this.setData({
        planDetail: planData,
        plan: {
          id: correctPlanId, // 使用正确的planId
          title: planData.name,
          duration: planData.estimated_duration || 60,
          description: planData.description || '训练计划详情'
        }
      });

      // 如果有workouts数据，处理exercises
      if (planData.workouts && Array.isArray(planData.workouts)) {
        this.processExercisesFromWorkouts(planData.workouts);
      }

      wx.hideLoading();
      return planData;
    } catch (error) {
      console.error('获取训练计划详情失败:', error);
      wx.hideLoading();
      wx.showToast({
        title: '获取计划详情失败',
        icon: 'none'
      });

      // 返回默认数据
      return null;
    }
  },

  // 从workouts数据中处理exercises
  processExercisesFromWorkouts(workouts) {
    try {
      console.log('处理workouts中的exercises，workouts数量:', workouts?.length || 0);

      if (!workouts || !Array.isArray(workouts) || workouts.length === 0) {
        console.log('没有workouts数据，设置exercises为空数组');
        this.setData({
          exercises: [],
          isLoading: false
        });
        return;
      }

      // 提取所有运动
      let allExercises = [];

      // 处理多日计划的情况
      if (workouts.length > 1) {
        console.log('多日计划，处理每个训练日');

        // 为每个workout添加isExpanded属性和processedExercises数组
        const processedWorkouts = workouts.map(workout => {
          // 确保有exercises数据
          if (!workout.exercises || !Array.isArray(workout.exercises)) {
            workout.exercises = [];
          }

          // 处理每个运动
          const processedExercises = workout.exercises.map(exercise => {
            // 处理图片URL，确保使用完整路径
            // 优先处理exercise_image字段
            let processedImageUrl = '/images/exercises/default.png';

            if (exercise.exercise_image) {
              processedImageUrl = imageHelper.getFullImageUrl(exercise.exercise_image);
            } else if (exercise.gif_url) {
              processedImageUrl = imageHelper.getFullImageUrl(exercise.gif_url);
            } else if (exercise.image_url) {
              processedImageUrl = imageHelper.getFullImageUrl(exercise.image_url);
            } else if (exercise.image_name) {
              processedImageUrl = imageHelper.getFullImageUrl(exercise.image_name);
            }

            console.log('处理后的图片URL:', processedImageUrl);

            // 处理组数据
            const sets = [];
            const setCount = exercise.sets || 3;
            const reps = exercise.reps || 12;
            const weight = exercise.weight || 20;

            // 如果有set_records，使用set_records数据
            if (exercise.set_records && Array.isArray(exercise.set_records) && exercise.set_records.length > 0) {
              exercise.set_records.forEach(record => {
                sets.push({
                  type: record.set_type || 'normal',
                  weight: record.weight || weight,
                  reps: record.reps || reps,
                  completed: record.completed || false
                });
              });
            } else {
              // 创建默认组数据
              for (let i = 0; i < setCount; i++) {
                sets.push({
                  type: i === 0 ? 'warmup' : 'normal',
                  weight: i === 0 ? weight * 0.8 : weight,
                  reps: i === 0 ? reps + 2 : reps,
                  completed: false
                });
              }
            }

            // 返回处理后的运动数据
            return {
              id: exercise.id,
              name: exercise.exercise_name || exercise.name,
              imageUrl: processedImageUrl,
              category: exercise.exercise_description || '',
              sets: sets,
              isExpanded: false,
              totalVolume: this.calculateTotalVolume(sets)
            };
          });

          // 添加处理后的exercises到workout
          return {
            ...workout,
            isExpanded: false, // 默认折叠
            processedExercises: processedExercises
          };
        });

        // 更新planDetail中的workouts
        const planDetail = this.data.planDetail;
        planDetail.workouts = processedWorkouts;

        this.setData({
          planDetail: planDetail,
          isLoading: false
        });
      }
      // 单日计划的情况
      else if (workouts.length === 1) {
        console.log('单日计划，直接处理exercises');
        const workout = workouts[0];

        // 确保有exercises数据
        if (workout.exercises && Array.isArray(workout.exercises)) {
          // 处理每个运动
          const processedExercises = workout.exercises.map(exercise => {
            // 处理图片URL，确保使用完整路径
            // 优先处理exercise_image字段
            let processedImageUrl = '/images/exercises/default.png';

            if (exercise.exercise_image) {
              processedImageUrl = imageHelper.getFullImageUrl(exercise.exercise_image);
            } else if (exercise.gif_url) {
              processedImageUrl = imageHelper.getFullImageUrl(exercise.gif_url);
            } else if (exercise.image_url) {
              processedImageUrl = imageHelper.getFullImageUrl(exercise.image_url);
            } else if (exercise.image_name) {
              processedImageUrl = imageHelper.getFullImageUrl(exercise.image_name);
            }

            console.log('处理后的图片URL:', processedImageUrl);

            // 处理组数据
            const sets = [];
            const setCount = exercise.sets || 3;
            const reps = exercise.reps || 12;
            const weight = exercise.weight || 20;

            // 如果有set_records，使用set_records数据
            if (exercise.set_records && Array.isArray(exercise.set_records) && exercise.set_records.length > 0) {
              exercise.set_records.forEach(record => {
                sets.push({
                  type: record.set_type || 'normal',
                  weight: record.weight || weight,
                  reps: record.reps || reps,
                  completed: record.completed || false
                });
              });
            } else {
              // 创建默认组数据
              for (let i = 0; i < setCount; i++) {
                sets.push({
                  type: i === 0 ? 'warmup' : 'normal',
                  weight: i === 0 ? weight * 0.8 : weight,
                  reps: i === 0 ? reps + 2 : reps,
                  completed: false
                });
              }
            }

            // 返回处理后的运动数据
            return {
              id: exercise.id,
              name: exercise.exercise_name || exercise.name,
              imageUrl: processedImageUrl,
              category: exercise.exercise_description || '',
              sets: sets,
              isExpanded: false
            };
          });

          // 添加到总列表
          allExercises = [...allExercises, ...processedExercises];
        }

        // 计算每个运动的总容量并更新状态
        const exercisesWithVolume = allExercises.map(exercise => ({
          ...exercise,
          totalVolume: this.calculateTotalVolume(exercise.sets)
        }));

        this.setData({
          exercises: exercisesWithVolume,
          isLoading: false
        });
      }

      // 缓存处理后的数据
      if (this.data.planDetail && this.data.plan && this.data.plan.id) {
        // 确保使用正确的planId作为缓存键
        const planId = this.data.plan.id;
        console.log('缓存训练计划数据，使用planId:', planId);
        imageHelper.cachePlanData(planId, this.data.planDetail);
      }
    } catch (error) {
      console.error('处理运动数据失败:', error);
      // 设置默认空数组
      this.setData({
        exercises: [],
        isLoading: false
      });
    }
  },

  // 计算单个运动的总容量
  calculateTotalVolume(sets) {
    return sets.reduce((total, set) => {
      return total + (set.weight * set.reps)
    }, 0)
  },

  // 切换运动详情的展开状态
  toggleExercise(e) {
    const { index } = e.currentTarget.dataset
    const { exercises } = this.data

    exercises[index].isExpanded = !exercises[index].isExpanded

    this.setData({
      exercises
    })
  },

  // 切换训练日的展开状态
  toggleWorkout(e) {
    const { workoutId } = e.currentTarget.dataset
    const { planDetail } = this.data

    if (!planDetail || !planDetail.workouts) return;

    // 找到对应的workout
    const workoutIndex = planDetail.workouts.findIndex(w => w.id === workoutId);
    if (workoutIndex === -1) return;

    // 切换展开状态
    planDetail.workouts[workoutIndex].isExpanded = !planDetail.workouts[workoutIndex].isExpanded;

    this.setData({
      planDetail
    });
  },

  // 切换训练日中运动的展开状态
  toggleExerciseInWorkout(e) {
    const { workoutId, exerciseIndex } = e.currentTarget.dataset
    const { planDetail } = this.data

    if (!planDetail || !planDetail.workouts) return;

    // 找到对应的workout
    const workoutIndex = planDetail.workouts.findIndex(w => w.id === workoutId);
    if (workoutIndex === -1) return;

    // 找到对应的exercise
    const workout = planDetail.workouts[workoutIndex];
    if (!workout.processedExercises || !workout.processedExercises[exerciseIndex]) return;

    // 切换展开状态
    workout.processedExercises[exerciseIndex].isExpanded = !workout.processedExercises[exerciseIndex].isExpanded;

    this.setData({
      planDetail
    });
  },

  // 开始训练
  startTraining() {
    wx.showLoading({ title: '准备训练中...' });

    const planId = this.data.plan.id;
    const planDetail = this.data.planDetail;

    if (!planDetail) {
      wx.hideLoading();
      wx.showToast({ title: '计划数据不完整，请重试', icon: 'none' });
      return;
    }

    // 使用prepareUpdateData准备更新数据
    const updateData = this.prepareUpdateData(planDetail, {
      status: 'active',
      is_active: true,
      workout_status: 'in_progress',
      add_training_record: true,
      training_record_status: 'in_progress'
    });

    if (!updateData) {
      wx.hideLoading();
      wx.showToast({ title: '准备数据失败，请重试', icon: 'none' });
      return;
    }

    // 获取workout ID
    let workoutId = null;
    if (planDetail.workouts && planDetail.workouts.length > 0) {
      workoutId = planDetail.workouts[0].id; // 使用正确的workoutId
      console.log('【startTraining】获取到workoutId:', workoutId, '类型:', typeof workoutId);
    }

    console.log('【startTraining】准备更新训练计划:', updateData);

    trainingPlanApi.updatePlan(planId, updateData)
      .then(() => {
        // 更新本地planDetail
        if (planDetail.workouts && planDetail.workouts.length > 0) {
          planDetail.workouts[0].status = 'in_progress';
        }

        this.setData({
          planDetail: planDetail,
          needSave: false
        });

        // 如果是从聊天页面打开的，并且有消息ID，则向聊天页面回传数据
        if (this.data.openerPagePath === 'pages/chatbot/index' && this.data.messageId) {
          console.log('准备向聊天页面回传数据，消息ID:', this.data.messageId);

          // 获取打开页面的实例
          const pages = getCurrentPages();
          const prevPage = pages.length > 1 ? pages[pages.length - 2] : null;

          if (prevPage && prevPage.route === 'pages/chatbot/index') {
            console.log('找到聊天页面实例，准备回传数据');

            // 只传递必要的信息
            const statusInfo = {
              status: 'active',
              is_active: true,
              workout_status: 'in_progress'
            };

            // 调用聊天页面的方法更新消息，传递planId而不是整个计划数据
            prevPage.updateTrainingPlanMessage(this.data.messageId, planId, statusInfo);
            console.log('已向聊天页面回传数据');
          }
        }

        wx.hideLoading();

        // 如果没有workoutId，提示用户
        if (!workoutId) {
          wx.showToast({ title: '请先创建训练日', icon: 'none' });
          return;
        }

        // 跳转到训练执行页面
        // 确保workoutId是字符串类型
        const workoutIdStr = String(workoutId);
        const url = `/pages/training/training-session/index?planId=${planId}&workoutId=${workoutIdStr}`;
        console.log('准备跳转到:', url, '传递的workoutId:', workoutIdStr, '类型:', typeof workoutIdStr);

        wx.navigateTo({
          url: url,
          success: () => console.log('页面跳转成功'),
          fail: (error) => {
            console.error('页面跳转失败:', error);
            wx.showToast({ title: '页面跳转失败', icon: 'none' });
          }
        });
      })
      .catch(error => {
        console.error('训练计划更新失败:', error);
        wx.hideLoading();
        wx.showToast({ title: '开始训练失败', icon: 'none' });
      });
  },

  // 处理组类型变化
  handleSetTypeChange(e) {
    const { exerciseIndex, setIndex } = e.currentTarget.dataset;
    const typeValue = parseInt(e.detail.value);
    const typeMap = ['warmup', 'normal', 'decrease'];

    const exercises = this.data.exercises;
    exercises[exerciseIndex].sets[setIndex].type = typeMap[typeValue];

    this.setData({
      exercises,
      needSave: true // 标记需要保存
    });
  },

  // 处理重量变化
  handleWeightChange(e) {
    const { exerciseIndex, setIndex } = e.currentTarget.dataset;
    const weight = parseFloat(e.detail.value) || 0;

    const exercises = this.data.exercises;
    exercises[exerciseIndex].sets[setIndex].weight = weight;
    exercises[exerciseIndex].totalVolume = this.calculateTotalVolume(exercises[exerciseIndex].sets);

    this.setData({
      exercises,
      needSave: true // 标记需要保存
    });
  },

  // 处理次数变化
  handleRepsChange(e) {
    const { exerciseIndex, setIndex } = e.currentTarget.dataset;
    const reps = parseInt(e.detail.value) || 0;

    const exercises = this.data.exercises;
    exercises[exerciseIndex].sets[setIndex].reps = reps;
    exercises[exerciseIndex].totalVolume = this.calculateTotalVolume(exercises[exerciseIndex].sets);

    this.setData({
      exercises,
      needSave: true // 标记需要保存
    });
  },

  // 处理训练日中组类型变更
  handleSetTypeChangeInWorkout(e) {
    const { workoutId, exerciseIndex, setIndex } = e.currentTarget.dataset;
    const typeValue = parseInt(e.detail.value);
    const typeMap = ['warmup', 'normal', 'decrease'];
    const { planDetail } = this.data;

    if (!planDetail || !planDetail.workouts) return;

    // 找到对应的workout
    const workoutIndex = planDetail.workouts.findIndex(w => w.id === workoutId);
    if (workoutIndex === -1) return;

    // 找到对应的exercise
    const workout = planDetail.workouts[workoutIndex];
    if (!workout.processedExercises || !workout.processedExercises[exerciseIndex]) return;

    // 更新组类型
    workout.processedExercises[exerciseIndex].sets[setIndex].type = typeMap[typeValue];

    // 更新总容量
    workout.processedExercises[exerciseIndex].totalVolume = this.calculateTotalVolume(workout.processedExercises[exerciseIndex].sets);

    this.setData({
      planDetail,
      needSave: true // 标记需要保存
    });
  },

  // 处理训练日中重量变更
  handleWeightChangeInWorkout(e) {
    const { workoutId, exerciseIndex, setIndex } = e.currentTarget.dataset;
    const weight = parseFloat(e.detail.value) || 0;
    const { planDetail } = this.data;

    if (!planDetail || !planDetail.workouts) return;

    // 找到对应的workout
    const workoutIndex = planDetail.workouts.findIndex(w => w.id === workoutId);
    if (workoutIndex === -1) return;

    // 找到对应的exercise
    const workout = planDetail.workouts[workoutIndex];
    if (!workout.processedExercises || !workout.processedExercises[exerciseIndex]) return;

    // 更新重量
    workout.processedExercises[exerciseIndex].sets[setIndex].weight = weight;

    // 更新总容量
    workout.processedExercises[exerciseIndex].totalVolume = this.calculateTotalVolume(workout.processedExercises[exerciseIndex].sets);

    this.setData({
      planDetail,
      needSave: true // 标记需要保存
    });
  },

  // 处理训练日中次数变更
  handleRepsChangeInWorkout(e) {
    const { workoutId, exerciseIndex, setIndex } = e.currentTarget.dataset;
    const reps = parseInt(e.detail.value) || 0;
    const { planDetail } = this.data;

    if (!planDetail || !planDetail.workouts) return;

    // 找到对应的workout
    const workoutIndex = planDetail.workouts.findIndex(w => w.id === workoutId);
    if (workoutIndex === -1) return;

    // 找到对应的exercise
    const workout = planDetail.workouts[workoutIndex];
    if (!workout.processedExercises || !workout.processedExercises[exerciseIndex]) return;

    // 更新次数
    workout.processedExercises[exerciseIndex].sets[setIndex].reps = reps;

    // 更新总容量
    workout.processedExercises[exerciseIndex].totalVolume = this.calculateTotalVolume(workout.processedExercises[exerciseIndex].sets);

    this.setData({
      planDetail,
      needSave: true // 标记需要保存
    });
  },

  // 处理加入计划
  handleJoinPlan() {
    // 显示日期选择器
    this.setData({
      datePickerVisible: true
    });
  },

  // 日期选择器确认
  onDatePickerConfirm(e) {
    const { value } = e.detail;
    const selectedDate = new Date(value);
    const formattedDate = `${selectedDate.getFullYear()}-${String(selectedDate.getMonth() + 1).padStart(2, '0')}-${String(selectedDate.getDate()).padStart(2, '0')}`;

    // 隐藏日期选择器
    this.setData({ datePickerVisible: false });
    wx.showLoading({ title: '加入计划中...' });

    const planId = this.data.plan.id;
    const planDetail = this.data.planDetail;

    if (!planDetail) {
      wx.hideLoading();
      wx.showToast({ title: '计划数据不完整，请重试', icon: 'none' });
      return;
    }

    // 计算结束日期 - 基于选择的开始日期和计划的持续周数
    const durationWeeks = planDetail.duration_weeks || 4; // 默认为4周
    const endDate = new Date(selectedDate);
    endDate.setDate(endDate.getDate() + (durationWeeks * 7));
    const formattedEndDate = `${endDate.getFullYear()}-${String(endDate.getMonth() + 1).padStart(2, '0')}-${String(endDate.getDate()).padStart(2, '0')}`;

    // 使用prepareUpdateData准备更新数据
    const updateData = this.prepareUpdateData(planDetail, {
      status: 'active',
      is_active: true,
      start_date: formattedDate,
      end_date: formattedEndDate,
      workout_status: 'not_started',
      scheduled_date: formattedDate
    });

    if (!updateData) {
      wx.hideLoading();
      wx.showToast({ title: '准备数据失败，请重试', icon: 'none' });
      return;
    }

    console.log('【onDatePickerConfirm】准备更新训练计划:', updateData);

    trainingPlanApi.updatePlan(planId, updateData)
      .then(() => {
        // 更新本地planDetail
        if (planDetail.workouts && planDetail.workouts.length > 0) {
          planDetail.workouts[0].scheduled_date = formattedDate;
          planDetail.workouts[0].status = 'not_started';
        }

        // 更新计划的开始日期和结束日期
        planDetail.start_date = formattedDate;
        planDetail.end_date = formattedEndDate;
        planDetail.status = 'active';
        planDetail.is_active = true;

        this.setData({
          planDetail: planDetail,
          needSave: false,
          needRefresh: false // 已经更新了本地数据，不需要刷新
        });

        // 如果是从聊天页面打开的，并且有消息ID，则向聊天页面回传数据
        if (this.data.openerPagePath === 'pages/chatbot/index' && this.data.messageId) {
          console.log('准备向聊天页面回传数据，消息ID:', this.data.messageId);

          // 获取打开页面的实例
          const pages = getCurrentPages();
          const prevPage = pages.length > 1 ? pages[pages.length - 2] : null;

          if (prevPage && prevPage.route === 'pages/chatbot/index') {
            console.log('找到聊天页面实例，准备回传数据');

            // 只传递必要的信息
            const statusInfo = {
              status: 'active',
              is_active: true,
              start_date: formattedDate,
              end_date: formattedEndDate
            };

            // 确保messageId是有效的
            const messageId = this.data.messageId;
            console.log('准备更新训练计划消息，消息ID:', messageId, '类型:', typeof messageId);

            // 调用聊天页面的方法更新消息，传递planId而不是整个计划数据
            prevPage.updateTrainingPlanMessage(messageId, planId, statusInfo);
            console.log('已向聊天页面回传数据，messageId:', messageId, 'planId:', planId);
          }
        }

        wx.hideLoading();
        wx.showToast({
          title: '已加入计划',
          icon: 'success',
          complete: () => {
            // 如果是从聊天页面打开的，则在提示后自动返回聊天页面
            if (this.data.openerPagePath === 'pages/chatbot/index') {
              console.log('准备返回聊天页面');
              setTimeout(() => {
                wx.navigateBack({
                  delta: 1,
                  success: () => console.log('成功返回聊天页面'),
                  fail: (err) => console.error('返回聊天页面失败:', err)
                });
              }, 1000); // 延迟1秒，让用户看到提示
            }
          }
        });
      })
      .catch(error => {
        console.error('训练计划更新失败:', error);
        wx.hideLoading();
        wx.showToast({ title: '加入计划失败', icon: 'none' });
      });
  },

  // 日期选择器取消
  onDatePickerCancel() {
    this.setData({
      datePickerVisible: false
    });

    wx.showToast({
      title: '已取消选择',
      icon: 'none'
    });
  },

  // 导航到选择动作页面
  navigateToSelectExercise() {
    const planId = this.data.plan.id;
    console.log('[DEBUG] 导航到选择动作页面，传递planId:', planId);

    wx.navigateTo({
      url: `/pages/select-training-exercises/index?planId=${planId}`,
      success: (res) => {
        // 监听选择结果 - 修复事件名称
        res.eventChannel.on('addExercises', (data) => {
          console.log('[DEBUG] 接收到选择的动作数据:', JSON.stringify(data));
          console.log('[DEBUG] 当前exercises列表:', JSON.stringify(this.data.exercises));

          if (data && data.exercises && data.exercises.length > 0) {
            console.log('[DEBUG] 准备处理选择的动作数据，数量:', data.exercises.length);
            // 处理选择的动作数据
            this.handleSelectedExercises({
              exercises: data.exercises,
              setType: '正式', // 默认组类型
              planId: planId
            });
          } else {
            console.error('[DEBUG] 接收到的动作数据无效或为空');
          }
        });
      },
      fail: (err) => {
        console.error('页面跳转失败:', err);
        wx.showToast({
          title: '页面跳转失败',
          icon: 'none'
        });
      }
    });
  },

  // 处理选择的动作数据
  handleSelectedExercises(data) {
    console.log('[DEBUG] handleSelectedExercises 开始处理数据:', JSON.stringify(data));

    if (!data) {
      console.error('[DEBUG] 传入的数据为空');
      wx.showToast({
        title: '数据不完整，请重试',
        icon: 'none'
      });
      return;
    }

    const { exercises, setType, planId: receivedPlanId } = data;
    console.log('[DEBUG] 解构后的数据 - exercises:', JSON.stringify(exercises),
                '组类型:', setType, '接收到的planId:', receivedPlanId);

    // 确保planDetail和workouts存在
    if (!this.data.planDetail || !this.data.planDetail.workouts || this.data.planDetail.workouts.length === 0) {
      console.error('[DEBUG] planDetail数据不完整，无法添加动作');
      console.log('[DEBUG] planDetail:', JSON.stringify(this.data.planDetail));
      wx.showToast({
        title: '数据不完整，请重试',
        icon: 'none'
      });
      return;
    }

    // 获取当前workout
    const workout = this.data.planDetail.workouts[0];
    const workoutId = workout.id;

    // 检查planId一致性
    const currentPlanId = this.data.plan.id;
    if (receivedPlanId && receivedPlanId !== currentPlanId) {
      console.warn('接收到的planId与当前planId不一致，使用当前planId');
      console.warn('接收到的planId:', receivedPlanId, '当前planId:', currentPlanId);
    }

    // 使用正确的planId
    const planId = currentPlanId;
    console.log('处理选择的动作数据，使用planId:', planId);

    // 将选择的动作添加到当前计划中
    const newExercises = [...this.data.exercises];

    // 为每个选择的动作创建默认的组数据
    exercises.forEach((exercise, index) => {
      // 处理图片URL，确保使用完整路径
      const processedImageUrl = imageHelper.getFullImageUrl(exercise.imageUrl);
      console.log('处理后的图片URL:', processedImageUrl);

      // 根据组类型设置默认值
      let defaultSets = [];
      const defaultReps = 12;
      const defaultWeight = 20;

      // 根据组类型创建不同的默认组
      switch(setType) {
        case '热身':
          defaultSets = [
            { type: 'warmup', weight: defaultWeight * 0.6, reps: defaultReps + 3 },
            { type: 'normal', weight: defaultWeight * 0.8, reps: defaultReps },
            { type: 'normal', weight: defaultWeight, reps: defaultReps }
          ];
          break;
        case '递减':
          defaultSets = [
            { type: 'normal', weight: defaultWeight, reps: defaultReps },
            { type: 'normal', weight: defaultWeight * 0.9, reps: defaultReps },
            { type: 'decrease', weight: defaultWeight * 0.8, reps: defaultReps + 2 }
          ];
          break;
        default: // 正式
          defaultSets = [
            { type: 'normal', weight: defaultWeight, reps: defaultReps },
            { type: 'normal', weight: defaultWeight, reps: defaultReps },
            { type: 'normal', weight: defaultWeight, reps: defaultReps }
          ];
      }

      // 创建新的UI运动对象
      const newExercise = {
        id: exercise.id,
        name: exercise.name,
        imageUrl: processedImageUrl,
        category: exercise.category || '',
        isExpanded: false,
        sets: defaultSets,
        totalVolume: this.calculateTotalVolume(defaultSets)
      };

      // 添加到UI运动列表
      newExercises.push(newExercise);

      // 创建workout exercise对象，添加到planDetail中
      const setCount = defaultSets.length;

      // 生成唯一的ID，使用added_exercise前缀加时间戳和随机数
      const uniqueId = `added_exercise_${Date.now()}_${exercise.id}_${Math.floor(Math.random() * 10000)}`;
      console.log('[DEBUG] 生成的唯一ID:', uniqueId);

      const workoutExercise = {
        id: uniqueId, // 使用生成的唯一ID
        exercise_id: exercise.exercise_id || exercise.id, // 优先使用exercise_id，回退到id
        workout_id: workoutId,
        sets: setCount,
        reps: defaultReps,
        rest_seconds: 60,
        order: (workout.exercises ? workout.exercises.length : 0) + index + 1,
        notes: "",
        exercise_type: "weight_reps",
        weight: defaultWeight,
        exercise_name: exercise.name,
        image_url: processedImageUrl,
        exercise_description: exercise.category || "",
        set_records: []
      };

      // 创建set_records
      for (let i = 0; i < setCount; i++) {
        const set = defaultSets[i];
        // 为set_record生成唯一ID，使用workout_exercise的ID作为前缀
        const setRecordId = `${uniqueId}_set_${i+1}`;

        workoutExercise.set_records.push({
          id: setRecordId, // 使用生成的唯一ID
          workout_exercise_id: uniqueId, // 使用workout_exercise的唯一ID
          set_number: i + 1,
          set_type: set.type,
          weight: set.weight,
          reps: set.reps,
          completed: false,
          notes: ""
        });
      }

      // 更新UI中的exercise ID，确保与后端数据一致
      newExercise.id = uniqueId;

      // 添加到planDetail的workouts中
      if (!workout.exercises) {
        workout.exercises = [];
      }
      workout.exercises.push(workoutExercise);
    });

    // 打印更新前的状态
    console.log('[DEBUG] 更新前的exercises:', JSON.stringify(this.data.exercises));
    console.log('[DEBUG] 新的exercises列表:', JSON.stringify(newExercises));
    console.log('[DEBUG] 新增exercises数量:', newExercises.length - this.data.exercises.length);

    // 更新状态
    this.setData({
      exercises: newExercises,
      planDetail: this.data.planDetail, // 更新planDetail
      needRefresh: false, // 已经更新了本地数据，不需要刷新
      needSave: true // 标记需要保存
    }, () => {
      // 在回调中确认数据已更新
      console.log('[DEBUG] 数据更新完成后的状态:');
      console.log('[DEBUG] - exercises数量:', this.data.exercises.length);
      console.log('[DEBUG] - 最后一个exercise:', this.data.exercises.length > 0 ?
                 JSON.stringify(this.data.exercises[this.data.exercises.length - 1]) : 'none');
    });

    // 缓存更新后的计划数据
    console.log('[DEBUG] 缓存更新后的计划数据，使用planId:', planId);
    imageHelper.cachePlanData(planId, this.data.planDetail);

    console.log('[DEBUG] 已更新planDetail, workouts数量:',
                this.data.planDetail.workouts ? this.data.planDetail.workouts.length : 0,
                '第一个workout的exercises数量:',
                this.data.planDetail.workouts && this.data.planDetail.workouts[0] ?
                this.data.planDetail.workouts[0].exercises.length : 0);

    wx.showToast({
      title: '已添加动作',
      icon: 'success'
    });
  },

  /**
   * 保存训练计划变更
   * 将UI中的exercises数据同步到planDetail，并发送到后端
   */
  async saveTrainingPlanChanges() {
    console.log('[DEBUG] 保存训练计划变更');

    if (!this.data.planDetail || !this.data.planDetail.workouts) {
      console.log('[DEBUG] 没有计划详情或workout数据，无法保存');
      return;
    }

    try {
      wx.showLoading({
        title: '保存中...',
        mask: true
      });

      // 构建更新数据
      const updateData = this.prepareUpdateData(this.data.planDetail);
      const planId = this.data.plan.id;

      console.log('[DEBUG] 准备更新的数据:', updateData);

      // 调用更新API
      const result = await trainingPlanApi.updateTrainingPlan(planId, updateData);

      console.log('[DEBUG] 更新结果:', result);

      wx.hideLoading();

      // 标记为已保存
      this.setData({
        needSave: false
      });

      wx.showToast({
        title: '保存成功',
        icon: 'success'
      });

    } catch (error) {
      console.error('[DEBUG] 保存训练计划失败:', error);
      wx.hideLoading();
      wx.showToast({
        title: '保存失败',
        icon: 'none'
      });
    }
  },

  /**
   * 设置模板创建模式
   */
  setupTemplateMode(templateData) {
    console.log('设置模板创建模式:', templateData);
    
    // 设置导航栏标题
    wx.setNavigationBarTitle({
      title: '创建训练模板'
    });
    
    // 生成临时ID
    const templateId = 'template_' + Date.now();
    const workoutId = 'workout_' + Date.now();
    
    // 创建默认的workout结构，用于模板创建模式
    const defaultWorkout = {
      id: workoutId,
      training_plan_id: templateId,
      name: templateData.name || '训练模板',
      day_of_week: 1,
      day_number: 1,
      description: templateData.description || '',
      scheduled_date: new Date().toISOString().split('T')[0],
      status: "not_started",
      exercises: [] // 空的exercises数组，用于存放添加的动作
    };
    
    // 初始化模板数据
    this.setData({
      templateData: templateData,
      plan: {
        id: templateId, // 临时ID
        name: templateData.name,
        estimated_duration: templateData.estimated_duration,
        description: templateData.description
      },
      exercises: [],
      planDetail: {
        id: templateId,
        name: templateData.name,
        estimated_duration: templateData.estimated_duration,
        description: templateData.description,
        workouts: [defaultWorkout] // 包含一个默认workout
      }
    });
    
    console.log('模板创建模式初始化完成，planDetail:', this.data.planDetail);
  },

  /**
   * 设置模板查看模式
   */
  setupViewTemplateMode(templateData) {
    console.log('设置模板查看模式:', templateData);
    
    // 设置导航栏标题
    wx.setNavigationBarTitle({
      title: templateData.name || '模板详情'
    });
    
    // 如果模板有exercises数据，需要转换为plan-detail页面的数据格式
    const processedExercises = [];
    
    if (templateData.exercises && templateData.exercises.length > 0) {
      templateData.exercises.forEach((exercise, index) => {
        // 处理图片URL
        const imageHelper = require('../../../utils/image-helper');
        const processedImageUrl = imageHelper.getFullImageUrl(exercise.image_url || exercise.imageUrl || '/images/exercises/default.png');
        
        // 创建默认组数据
        const sets = [];
        const setCount = exercise.sets || 3;
        const reps = exercise.reps || 12;
        const weight = exercise.weight || 20;
        
        for (let i = 0; i < setCount; i++) {
          sets.push({
            type: i === 0 ? 'warmup' : 'normal',
            weight: i === 0 ? weight * 0.8 : weight,
            reps: i === 0 ? reps + 2 : reps,
            completed: false
          });
        }
        
        processedExercises.push({
          id: exercise.exercise_id || exercise.id || `template_exercise_${index}`,
          name: exercise.exercise_name || exercise.name,
          imageUrl: processedImageUrl,
          category: exercise.exercise_description || exercise.category || '',
          sets: sets,
          isExpanded: false,
          totalVolume: this.calculateTotalVolume(sets)
        });
      });
    }
    
    // 创建模拟的workout结构用于显示
    const templateId = templateData.templateId || templateData.id;
    const workoutId = 'template_workout_' + templateId;
    
    const workout = {
      id: workoutId,
      training_plan_id: templateId,
      name: templateData.name || '模板训练',
      day_of_week: 1,
      day_number: 1,
      description: templateData.description || '',
      scheduled_date: new Date().toISOString().split('T')[0],
      status: "template",
      exercises: processedExercises.map((exercise, index) => {
        const setCount = exercise.sets.length;
        const defaultReps = 12;
        const defaultWeight = 20;
        
        return {
          id: exercise.id,
          exercise_id: exercise.id,
          workout_id: workoutId,
          sets: setCount,
          reps: defaultReps,
          rest_seconds: 60,
          order: index + 1,
          notes: "",
          exercise_type: "weight_reps",
          weight: defaultWeight,
          exercise_name: exercise.name,
          image_url: exercise.imageUrl,
          exercise_description: exercise.category,
          set_records: exercise.sets.map((set, setIndex) => ({
            id: `${exercise.id}_set_${setIndex + 1}`,
            workout_exercise_id: exercise.id,
            set_number: setIndex + 1,
            set_type: set.type,
            weight: set.weight,
            reps: set.reps,
            completed: set.completed,
            notes: ""
          }))
        };
      })
    };
    
    // 设置数据
    this.setData({
      templateData: templateData,
      templateId: templateId,
      plan: {
        id: templateId,
        name: templateData.name,
        estimated_duration: templateData.estimated_duration,
        description: templateData.description
      },
      exercises: processedExercises,
      planDetail: {
        id: templateId,
        name: templateData.name,
        estimated_duration: templateData.estimated_duration,
        description: templateData.description,
        workouts: [workout]
      }
    });
    
    console.log('模板查看模式初始化完成，exercises数量:', processedExercises.length);
  },

  /**
   * 保存为模板
   */
  async saveAsTemplate() {
    // 检查是否有训练动作
    if (!this.data.exercises || this.data.exercises.length === 0) {
      wx.showToast({
        title: '请先添加训练动作',
        icon: 'none'
      });
      return;
    }

    try {
      wx.showLoading({
        title: this.data.templateId ? '更新模板中...' : '保存模板中...',
        mask: true
      });

      // 准备模板数据
      const workoutTemplatesApi = require('../../../api/workout-templates');
      
      const templateData = {
        name: this.data.templateData?.name || this.data.plan?.name || '训练模板',
        description: this.data.templateData?.description || this.data.plan?.description || '',
        estimated_duration: this.data.templateData?.estimated_duration || this.data.plan?.estimated_duration || 60,
        visibility: 'private',
        exercises: this.data.exercises.map((exercise, index) => ({
          exercise_id: exercise.exercise_id || exercise.id,
          order: index + 1,
          sets: exercise.sets?.length || 3,
          reps: exercise.reps || 12,
          weight: exercise.weight || 0,
          rest_seconds: exercise.rest_seconds || 60,
          notes: exercise.notes || '',
          exercise_type: exercise.exercise_type || 'weight_reps'
        }))
      };

      console.log('保存模板数据:', templateData);

      let result;
      if (this.data.templateId) {
        // 更新现有模板
        console.log('更新模板ID:', this.data.templateId);
        result = await workoutTemplatesApi.updateWorkoutTemplate(this.data.templateId, templateData);
        
        wx.showToast({
          title: '模板更新成功',
          icon: 'success'
        });
      } else {
        // 创建新模板
        result = await workoutTemplatesApi.createWorkoutTemplate(templateData);
        
        wx.showToast({
          title: '模板保存成功',
          icon: 'success'
        });
      }
      
      wx.hideLoading();

      // 延迟返回上一页
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);

    } catch (error) {
      console.error('保存模板失败:', error);
      wx.hideLoading();
      wx.showToast({
        title: this.data.templateId ? '更新失败' : '保存失败',
        icon: 'none'
      });
    }
  },

  /**
   * 开始训练（在模板模式下也保存模板）
   */
  async startTrainingInTemplateMode() {
    // 先保存模板
    await this.saveAsTemplate();
    
    // 如果保存成功，不需要额外操作，因为已经在saveAsTemplate中返回了
  },

  /**
   * 编辑模板
   */
  editTemplate() {
    console.log('切换到编辑模式');
    
    // 设置导航栏标题
    wx.setNavigationBarTitle({
      title: '编辑模板'
    });
    
    // 切换到编辑模式
    this.setData({
      isViewTemplateMode: false,
      isEditTemplateMode: true
    });
    
    wx.showToast({
      title: '已进入编辑模式',
      icon: 'success'
    });
  }
})