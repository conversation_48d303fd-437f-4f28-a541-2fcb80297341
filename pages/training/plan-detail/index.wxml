<!-- 训练计划详情页 -->
<view class="container">
  <block wx:if="{{isLoading}}">
    <view class="loading-container">
      <view class="loading">加载中...</view>
    </view>
  </block>
  <block wx:elif="{{!plan.id}}">
    <view class="empty-container">
      <view class="empty-text">未找到训练计划</view>
    </view>
  </block>
  <block wx:else>
    <!-- 顶部计划信息卡片 -->
    <view class="plan-header">
      <!-- 显示不同模式下的标题 -->
      <view class="plan-title">
        <block wx:if="{{isCreateTemplateMode}}">{{plan.name}}</block>
        <block wx:elif="{{isViewTemplateMode}}">{{plan.name}}</block>
        <block wx:else>{{planDetail.workouts && planDetail.workouts.length === 1 ? planDetail.workouts[0].name : plan.title}}</block>
      </view>
      <view class="plan-meta">
        <block wx:if="{{isCreateTemplateMode || isViewTemplateMode}}">
          <!-- 模板模式 -->
          <text class="duration">预计时长：{{plan.estimated_duration}}分钟</text>
          <text class="exercise-count">{{exercises.length}}个动作</text>
        </block>
        <block wx:elif="{{planDetail.workouts && planDetail.workouts.length === 1}}">
          <!-- 单日计划显示预计时长 -->
          <text class="duration">预计时长：{{plan.duration}}分钟</text>
          <text class="exercise-count">{{exercises.length}}个动作</text>
        </block>
        <block wx:else>
          <!-- 多日计划显示周期长度 -->
          <text class="duration">周期：{{planDetail.duration_weeks || '未设置'}}周</text>
          <text class="exercise-count">{{planDetail.workouts.length || 0}}个训练日</text>
        </block>
      </view>
      <view class="plan-description">{{plan.description}}</view>
    </view>

    <!-- 多日计划显示训练日分组 -->
    <block wx:if="{{planDetail.workouts && planDetail.workouts.length > 1}}">
      <view class="workout-groups">
        <view class="workout-group" wx:for="{{planDetail.workouts}}" wx:key="id" wx:for-item="workout">
          <view class="workout-header" bindtap="toggleWorkout" data-workout-id="{{workout.id}}">
            <view class="workout-title">{{workout.name}}</view>
            <view class="workout-meta">
              <text class="workout-duration">{{workout.estimated_duration || 0}}分钟</text>
              <text class="workout-exercise-count">{{workout.exercises.length || 0}}个动作</text>
            </view>
            <view class="expand-icon {{workout.isExpanded ? 'expanded' : ''}}"></view>
          </view>

          <!-- 训练日下的动作列表 -->
          <view class="workout-exercises {{workout.isExpanded ? 'expanded' : ''}}">
            <view class="exercise-card" wx:for="{{workout.processedExercises}}" wx:key="id" wx:for-item="exercise" data-workout-id="{{workout.id}}" data-exercise-index="{{index}}">
              <view class="exercise-header" bindtap="toggleExerciseInWorkout" data-workout-id="{{workout.id}}" data-exercise-index="{{index}}">
                <view class="exercise-basic-info">
                  <image class="exercise-image" src="{{exercise.imageUrl}}" mode="aspectFill" lazy-load></image>
                  <view class="exercise-info">
                    <text class="exercise-name">{{exercise.name}}</text>
                    <text class="exercise-summary">{{exercise.sets.length}}组 · 总容量 {{exercise.totalVolume}}kg</text>
                  </view>
                </view>
                <view class="expand-icon {{exercise.isExpanded ? 'expanded' : ''}}"></view>
              </view>

              <!-- 展开的详细信息 -->
              <view class="exercise-details {{exercise.isExpanded ? 'expanded' : ''}}">
                <view class="sets-list">
                  <view class="set-item" wx:for="{{exercise.sets}}" wx:for-item="set" wx:for-index="setIndex" wx:key="setIndex">
                    <picker bindchange="handleSetTypeChangeInWorkout" data-workout-id="{{workout.id}}" data-exercise-index="{{index}}" data-set-index="{{setIndex}}" value="{{set.type === 'warmup' ? 0 : (set.type === 'decrease' ? 2 : 1)}}" range="{{['热身', '正式', '递减']}}">
                      <view class="set-type {{set.type}}">{{set.type === 'warmup' ? '热身' : (set.type === 'decrease' ? '递减' : '正式')}}</view>
                    </picker>
                    <view class="set-info">
                      <input class="weight-input" type="digit" value="{{set.weight}}" bindinput="handleWeightChangeInWorkout" data-workout-id="{{workout.id}}" data-exercise-index="{{index}}" data-set-index="{{setIndex}}" />
                      <text class="multiply">×</text>
                      <input class="reps-input" type="number" value="{{set.reps}}" bindinput="handleRepsChangeInWorkout" data-workout-id="{{workout.id}}" data-exercise-index="{{index}}" data-set-index="{{setIndex}}" />
                      <text class="unit">次</text>
                    </view>
                    <view class="set-volume">容量: {{set.weight * set.reps}}kg</view>
                  </view>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </block>

    <!-- 单日计划直接显示动作列表 -->
    <block wx:else>
      <view class="exercise-list">
        <view class="exercise-card" wx:for="{{exercises}}" wx:key="id" data-index="{{index}}">
          <view class="exercise-header" bindtap="toggleExercise" data-index="{{index}}">
            <view class="exercise-basic-info">
              <image class="exercise-image" src="{{item.imageUrl}}" mode="aspectFill" lazy-load></image>
              <view class="exercise-info">
                <text class="exercise-name">{{item.name}}</text>
                <text class="exercise-summary">{{item.sets.length}}组 · 总容量 {{item.totalVolume}}kg</text>
              </view>
            </view>
            <view class="expand-icon {{item.isExpanded ? 'expanded' : ''}}"></view>
          </view>

          <!-- 展开的详细信息 -->
          <view class="exercise-details {{item.isExpanded ? 'expanded' : ''}}">
            <view class="sets-list">
              <view class="set-item" wx:for="{{item.sets}}" wx:for-item="set" wx:for-index="setIndex" wx:key="setIndex">
                <picker bindchange="handleSetTypeChange" data-exercise-index="{{index}}" data-set-index="{{setIndex}}" value="{{set.type === 'warmup' ? 0 : (set.type === 'decrease' ? 2 : 1)}}" range="{{['热身', '正式', '递减']}}">
                  <view class="set-type {{set.type}}">{{set.type === 'warmup' ? '热身' : (set.type === 'decrease' ? '递减' : '正式')}}</view>
                </picker>
                <view class="set-info">
                  <input class="weight-input" type="digit" value="{{set.weight}}" bindinput="handleWeightChange" data-exercise-index="{{index}}" data-set-index="{{setIndex}}" />
                  <text class="multiply">×</text>
                  <input class="reps-input" type="number" value="{{set.reps}}" bindinput="handleRepsChange" data-exercise-index="{{index}}" data-set-index="{{setIndex}}" />
                  <text class="unit">次</text>
                </view>
                <view class="set-volume">容量: {{set.weight * set.reps}}kg</view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </block>

    <!-- 底部操作按钮 -->
    <view class="bottom-actions">
      <block wx:if="{{isCreateTemplateMode}}">
        <!-- 创建模板模式的按钮 -->
        <button class="save-template-btn" bindtap="saveAsTemplate">保存为模板</button>
        <button class="start-button" bindtap="startTrainingInTemplateMode">开始训练</button>
      </block>
      <block wx:elif="{{isViewTemplateMode}}">
        <!-- 查看模板模式的按钮 -->
        <button class="edit-template-btn" bindtap="editTemplate">编辑模板</button>
        <button class="start-button" bindtap="startTrainingInTemplateMode">开始训练</button>
      </block>
      <block wx:elif="{{isEditTemplateMode}}">
        <!-- 编辑模板模式的按钮 -->
        <button class="save-template-btn" bindtap="saveAsTemplate">更新模板</button>
        <button class="start-button" bindtap="startTrainingInTemplateMode">开始训练</button>
      </block>
      <block wx:else>
        <!-- 普通模式的按钮 -->
        <button class="join-plan-btn" bindtap="handleJoinPlan">加入计划</button>
        <button class="start-button" bindtap="startTraining">开始训练</button>
      </block>
    </view>

    <!-- 浮动添加动作按钮 - 在模板创建和编辑模式下显示 -->
    <view class="add-exercise-btn" wx:if="{{isCreateTemplateMode || isEditTemplateMode}}" bindtap="navigateToSelectExercise">
      <text class="add-icon">+</text>
    </view>
    
    <!-- 普通模式下的浮动添加动作按钮 -->
    <view class="add-exercise-btn" wx:if="{{!isCreateTemplateMode && !isViewTemplateMode && !isEditTemplateMode}}" bindtap="navigateToSelectExercise">
      <text class="add-icon">+</text>
    </view>

    <!-- 日期选择器 -->
    <t-date-time-picker
      title="选择训练日期"
      visible="{{datePickerVisible}}"
      mode="date"
      value="{{minDate}}"
      format="YYYY-MM-DD"
      start="{{minDate}}"
      end="{{maxDate}}"
      bindconfirm="onDatePickerConfirm"
      bindcancel="onDatePickerCancel"
    />
  </block>
</view>