/* 容器样式 */
.container {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding-bottom: 120rpx;
}

/* 加载中和空状态样式 */
.loading-container,
.empty-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 60vh;
}

.loading,
.empty-text {
  font-size: 28rpx;
  color: #999999;
}

/* 计划头部样式 */
.plan-header {
  background-color: #ffffff;
  padding: 40rpx 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}

.plan-title {
  font-size: 40rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 20rpx;
}

.plan-meta {
  display: flex;
  gap: 20rpx;
  margin-bottom: 16rpx;
}

.duration, .exercise-count {
  font-size: 26rpx;
  color: #666666;
  background-color: #f0f0f0;
  padding: 6rpx 16rpx;
  border-radius: 20rpx;
}

.plan-description {
  font-size: 28rpx;
  color: #666666;
  line-height: 1.6;
}

/* 训练日分组样式 */
.workout-groups {
  margin-bottom: 20rpx;
}

.workout-group {
  background-color: #ffffff;
  margin: 20rpx;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.workout-header {
  padding: 30rpx;
  display: flex;
  flex-direction: column;
  border-bottom: 1rpx solid #f0f0f0;
  position: relative;
}

.workout-title {
  font-size: 34rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 10rpx;
}

.workout-meta {
  display: flex;
  gap: 20rpx;
}

.workout-duration, .workout-exercise-count {
  font-size: 24rpx;
  color: #666666;
  background-color: #f0f0f0;
  padding: 4rpx 12rpx;
  border-radius: 16rpx;
}

.workout-exercises {
  display: none;
  background-color: #f9f9f9;
  padding: 10rpx 0;
}

.workout-exercises.expanded {
  display: block;
}

.workout-group .expand-icon {
  position: absolute;
  right: 30rpx;
  top: 50%;
  transform: translateY(-50%);
}

/* 运动卡片样式 */
.exercise-card {
  background-color: #ffffff;
  margin: 20rpx;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

/* 多日计划中的运动卡片样式 */
.workout-exercises .exercise-card {
  margin: 10rpx 20rpx;
  box-shadow: 0 1rpx 4rpx rgba(0, 0, 0, 0.05);
}

.exercise-header {
  padding: 30rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.exercise-basic-info {
  display: flex;
  align-items: center;
  flex: 1;
}

.exercise-image {
  width: 120rpx;
  height: 120rpx;
  border-radius: 12rpx;
  margin-right: 20rpx;
  background-color: #f5f5f5;
}

.exercise-info {
  flex: 1;
}

.exercise-name {
  font-size: 32rpx;
  font-weight: 500;
  color: #333333;
  margin-bottom: 8rpx;
}

.exercise-summary {
  font-size: 26rpx;
  color: #666666;
}

.expand-icon {
  width: 32rpx;
  height: 32rpx;
  transition: transform 0.3s ease;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath fill='%23666' d='M7.41 8.59L12 13.17l4.59-4.58L18 10l-6 6-6-6 1.41-1.41z'/%3E%3C/svg%3E");
  background-size: contain;
}

.expand-icon.expanded {
  transform: rotate(180deg);
}

/* 展开的详细信息样式 */
.exercise-details {
  display: none;
  padding: 0 30rpx 30rpx;
  background-color: #f9f9f9;
}

.exercise-details.expanded {
  display: block;
}

.sets-list {
  border-top: 2rpx solid #eee;
  padding-top: 20rpx;
}

.set-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx;
  margin-bottom: 16rpx;
  background-color: #f8f9fa;
  border-radius: 12rpx;
  border: 2rpx solid #e9ecef;
}

.set-type {
  font-size: 24rpx;
  padding: 4rpx 12rpx;
  border-radius: 8rpx;
  margin-right: 20rpx;
  min-width: 80rpx;
  text-align: center;
}

.set-type.warmup {
  background-color: #e6f7ff;
  color: #1890ff;
}

.set-type.decrease {
  background-color: #fff7e6;
  color: #fa8c16;
}

.set-type.normal {
  background-color: #f6ffed;
  color: #52c41a;
}

.set-info {
  display: flex;
  align-items: center;
  gap: 12rpx;
  flex: 1;
  margin: 0 20rpx;
}

.weight-input,
.reps-input {
  width: 100rpx;
  height: 56rpx;
  background-color: #ffffff;
  border: 2rpx solid #e8e8e8;
  border-radius: 8rpx;
  text-align: center;
  font-size: 28rpx;
  color: #333333;
  padding: 0 10rpx;
}

.weight-input {
  margin-right: 8rpx;
}

.reps-input {
  margin-left: 8rpx;
}

.unit {
  color: #666666;
  font-size: 24rpx;
  margin-left: 4rpx;
}

.multiply {
  color: #999999;
  margin: 0 8rpx;
}

.set-volume {
  font-size: 24rpx;
  color: #666;
  min-width: 120rpx;
  text-align: right;
}

/* 底部操作按钮样式 */
.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 20rpx 40rpx;
  background-color: #ffffff;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  gap: 10rpx;
}

.start-button {
  background-color: #1890ff;
  color: #ffffff;
  font-size: 32rpx;
  font-weight: 500;
  border-radius: 44rpx;
  padding: 20rpx 0;
  width: 48%;
  text-align: center;
}

.join-plan-btn {
  background-color: #07c160;
  color: #ffffff;
  font-size: 32rpx;
  font-weight: 500;
  border-radius: 44rpx;
  padding: 20rpx 0;
  width: 48%;
  text-align: center;
}

.save-template-btn {
  background-color: #fa8c16;
  color: #ffffff;
  font-size: 32rpx;
  font-weight: 500;
  border-radius: 44rpx;
  padding: 20rpx 0;
  width: 48%;
  text-align: center;
}

.edit-template-btn {
  background-color: #722ed1;
  color: #ffffff;
  font-size: 32rpx;
  font-weight: 500;
  border-radius: 44rpx;
  padding: 20rpx 0;
  width: 48%;
  text-align: center;
}

.start-button:active,
.join-plan-btn:active,
.save-template-btn:active,
.edit-template-btn:active {
  opacity: 0.9;
}

/* 添加动作浮动按钮样式 */
.add-exercise-btn {
  position: fixed;
  right: 30rpx;
  bottom: 120rpx;
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
  background-color: #07c160;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 10rpx rgba(0, 0, 0, 0.2);
  z-index: 100;
}

.add-icon {
  font-size: 60rpx;
  color: #fff;
  line-height: 1;
}

/* 训练动作管理功能样式 */
.exercise-actions {
  display: flex;
  align-items: center;
  gap: 8rpx;
  margin-right: 10rpx;
}

.action-btn {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  transition: all 0.3s ease;
}

.delete-btn {
  background-color: #ffebee;
  color: #f44336;
}

.delete-btn:active {
  background-color: #ffcdd2;
  transform: scale(0.95);
}

.action-icon {
  font-size: 28rpx;
}

.set-actions {
  display: flex;
  align-items: center;
  gap: 8rpx;
  margin-left: 10rpx;
}

.delete-set-btn {
  width: 48rpx;
  height: 48rpx;
  border-radius: 50%;
  background-color: #ffebee;
  color: #f44336;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  transition: all 0.3s ease;
}

.delete-set-btn:active {
  background-color: #ffcdd2;
  transform: scale(0.9);
}

.delete-icon {
  font-size: 32rpx;
  font-weight: bold;
}

.add-set-container {
  margin-top: 20rpx;
  padding: 0 20rpx;
}

.add-set-btn {
  width: 100%;
  height: 80rpx;
  border: 2rpx dashed #1976D2;
  border-radius: 12rpx;
  background-color: #f3f9ff;
  color: #1976D2;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  transition: all 0.3s ease;
}

.add-set-btn:active {
  background-color: #e3f2fd;
  transform: scale(0.98);
}

.add-icon {
  font-size: 32rpx;
  margin-right: 8rpx;
}