<!-- 训练执行页面 -->
<view class="container">
  <block wx:if="{{isLoading}}">
    <view class="loading-container">
      <view class="loading">加载中...</view>
    </view>
  </block>
  <block wx:else>
    <!-- 顶部进度和计时器 -->
    <view class="training-header">
      <!-- 计时器和统计信息 -->
      <view class="stats-container">
        <view class="timer-display">
          <view class="timer-icon">⏱️</view>
          <view class="timer-value">{{formattedTime}}</view>
        </view>
        
        <view class="stats-divider"></view>
        
        <view class="stats-card">
          <view class="stats-item">
            <view class="stats-value">{{totalVolume || 0}}</view>
            <view class="stats-label">总重量 (kg)</view>
          </view>
          <view class="stats-item">
            <view class="stats-value">{{completedSets}}/{{totalSets}}</view>
            <view class="stats-label">已完成/总组数</view>
          </view>
          <view class="stats-item">
            <view class="stats-value">{{progressPercent}}%</view>
            <view class="stats-label">进度</view>
          </view>
        </view>
      </view>
      
      <!-- 按钮区域 -->
      <view class="control-buttons">
        <view class="pause-btn {{timerPaused ? 'resume' : 'pause'}}" bindtap="pauseResumeTimer">
          {{timerPaused ? '继续' : '暂停'}}
        </view>
        <view class="complete-btn" bindtap="completeTraining">完成</view>
      </view>
    </view>

    <!-- 添加练习和加载模板按钮 -->
    <view class="add-exercise-container">
      <button class="add-exercise-btn" bindtap="navigateToExerciseSelection">
        <text class="add-icon">+</text> 添加练习
      </button>
      <button class="load-template-btn" bindtap="loadTemplate">
        <text class="template-icon">📋</text> 加载模板
      </button>
    </view>

    <!-- 运动列表 -->
    <scroll-view scroll-y class="exercise-list">
      <view class="exercise-card" wx:for="{{exercises}}" wx:key="id" data-index="{{index}}">
        <view class="exercise-header">
          <view class="exercise-basic-info" bindtap="toggleExercise" data-index="{{index}}">
            <image class="exercise-image" src="{{item.imageUrl}}" mode="aspectFill" lazy-load></image>
            <view class="exercise-info">
              <text class="exercise-name">{{item.name}}</text>
              <text class="exercise-summary">{{item.sets.length}}组 | {{item.totalVolume || 0}}kg</text>
            </view>
          </view>
          <view class="exercise-actions">
            <view class="action-btn edit-btn" catchtap="editExercise" data-index="{{index}}">
              <text class="action-icon">✏️</text>
            </view>
            <view class="action-btn delete-btn" catchtap="deleteExercise" data-index="{{index}}">
              <text class="action-icon">🗑️</text>
            </view>
            <view class="action-btn expand-btn" catchtap="toggleExercise" data-index="{{index}}">
              <view class="expand-icon {{item.isExpanded ? 'expanded' : ''}}"></view>
            </view>
          </view>
        </view>

        <!-- 展开的详细信息 -->
        <view class="exercise-details {{item.isExpanded ? 'expanded' : ''}}">
          <view class="sets-list">
            <view class="set-header">
              <text class="set-header-text">类型</text>
              <text class="set-header-text">重量 × 次数</text>
              <text class="set-header-text">操作</text>
            </view>
            
            <view class="set-item" wx:for="{{item.sets}}" wx:for-item="set" wx:for-index="setIndex" wx:key="id">
              <view class="set-type-selector">
                <picker bindchange="handleSetTypeChange" data-exercise-index="{{index}}" data-set-index="{{setIndex}}" value="{{set.type === 'warmup' ? 0 : (set.type === 'decrease' ? 1 : 2)}}" range="{{['热身', '递减', '正式']}}">
                  <view class="set-type {{set.type}}">
                    {{set.type === 'warmup' ? '热身' : (set.type === 'decrease' ? '递减' : '正式')}}
                  </view>
                </picker>
              </view>
              <view class="set-info">
                <input class="weight-input" type="digit" value="{{set.weight}}" bindinput="handleWeightChange" data-exercise-index="{{index}}" data-set-index="{{setIndex}}" />
                <text class="multiply">×</text>
                <input class="reps-input" type="number" value="{{set.reps}}" bindinput="handleRepsChange" data-exercise-index="{{index}}" data-set-index="{{setIndex}}" />
                <text class="unit">次</text>
              </view>

              <view class="set-actions">
                <!-- 完成按钮 -->
                <view class="complete-set-btn {{set.completed ? 'completed' : ''}}"
                  bindtap="markSetCompleted"
                  data-exercise-index="{{index}}"
                  data-set-index="{{setIndex}}">
                  <text class="check-icon">✓</text>
                </view>
                
                <!-- 删除组按钮 -->
                <view class="delete-set-btn" bindtap="deleteSet" 
                  data-exercise-index="{{index}}" data-set-index="{{setIndex}}"
                  wx:if="{{item.sets.length > 1}}">
                  <text class="delete-icon">×</text>
                </view>
              </view>
            </view>

            <!-- 添加新组的按钮 -->
            <view class="add-set-container">
              <view class="add-set-btn" bindtap="addNewSet" data-exercise-index="{{index}}">
                <text class="add-icon">+</text> 添加组
              </view>
            </view>

            <!-- 已完成的组数据统计 -->
            <view class="completed-sets-summary" wx:if="{{item.hasCompletedSets}}">
              <text class="summary-title">已完成组: {{item.completedSets}}/{{item.sets.length}}</text>
              <text class="summary-volume">总重量: {{item.totalVolume || 0}}kg</text>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 底部提示 -->
      <view class="bottom-tips" wx:if="{{exercises.length > 0}}">
        <text>下拉可添加新练习</text>
      </view>
      
      <!-- 没有练习时的提示 -->
      <view class="empty-exercises-tip" wx:if="{{exercises.length === 0}}">
        <text>没有练习项目，请点击上方添加按钮</text>
      </view>
    </scroll-view>
    
    <!-- 保存模板按钮 - 固定在底部 -->
    <view class="bottom-actions">
      <button class="save-template-btn" bindtap="saveAsTemplate">
        <text class="template-icon">💾</text>
        <text class="template-text">保存为模板</text>
      </button>
    </view>
  </block>

  <!-- 保存为模板弹窗 -->
  <view class="modal-overlay" wx:if="{{showSaveTemplateModal}}" bindtap="closeSaveTemplateModal">
    <view class="modal-content" catchtap="stopPropagation">
      <view class="modal-header">
        <text class="modal-title">保存为训练模板</text>
        <view class="modal-close" bindtap="closeSaveTemplateModal">×</view>
      </view>
      
      <view class="modal-body">
        <view class="form-group">
          <text class="form-label">模板名称 *</text>
          <input class="form-input" type="text" placeholder="请输入模板名称" 
                 value="{{templateForm.name}}" bindinput="onTemplateNameInput" maxlength="50"/>
        </view>
        
        <view class="form-group">
          <text class="form-label">预计时长（分钟）*</text>
          <input class="form-input" type="number" placeholder="请输入预计时长" 
                 value="{{templateForm.estimated_duration}}" bindinput="onTemplateDurationInput" />
        </view>
        
        <view class="form-group">
          <text class="form-label">模板描述</text>
          <textarea class="form-textarea" placeholder="请输入模板描述（可选）" 
                    value="{{templateForm.description}}" bindinput="onTemplateDescInput" maxlength="200"/>
        </view>
        
        <view class="form-group">
          <text class="form-label">可见性</text>
          <picker bindchange="onVisibilityChange" value="{{templateForm.visibilityIndex}}" 
                  range="{{visibilityOptions}}" range-key="label">
            <view class="picker-display">
              {{visibilityOptions[templateForm.visibilityIndex].label}}
              <text class="picker-arrow">▼</text>
            </view>
          </picker>
        </view>
      </view>
      
      <view class="modal-footer">
        <button class="modal-btn cancel-btn" bindtap="closeSaveTemplateModal">取消</button>
        <button class="modal-btn confirm-btn" bindtap="confirmSaveTemplate">保存</button>
      </view>
    </view>
  </view>
</view>