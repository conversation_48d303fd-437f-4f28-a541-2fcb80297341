const app = getApp()
// 导入API模块
const trainingPlanApi = require('../../../api/training-plan')
const workoutTemplatesApi = require('../../../api/workout-templates')
// 导入图片处理工具
const imageHelper = require('../../../utils/image-helper')

Page({
  data: {
    planId: '',
    workoutId: '',
    isLoading: true,
    timerStarted: false,
    timerPaused: false, // 计时器是否暂停
    timerSeconds: 0,
    timerInterval: null,
    exercises: [], // 当前训练的动作列表
    completedSets: 0, // 已完成的组数
    totalSets: 0,    // 总组数
    totalVolume: 0,  // 总重量
    progressPercent: 0, // 进度百分比
    formattedTime: '00:00', // 格式化后的时间
    updateData: {}, // 用于保存到后端的数据
    needSave: false, // 是否需要保存
    
    // 模板相关数据
    showSaveTemplateModal: false,
    templateForm: {
      name: '',
      description: '',
      estimated_duration: 60, // 默认60分钟
      visibilityIndex: 2 // 默认选择私有
    },
    visibilityOptions: [
      { label: '公开可见', value: 'everyone' },
      { label: '好友可见', value: 'friends' }, 
      { label: '仅自己可见', value: 'private' }
    ]
  },

  onLoad: function(options) {
    console.log('【onLoad】页面参数:', options);
    
    // 检查是否从自定义训练页面跳转而来（自由训练模式）
    if (options.fromCustomize === 'true') {
      // 自由训练模式，不需要planId和workoutId
      this.setData({
        planId: null,
        workoutId: null,
        isLoading: false,
        exercises: [], // 初始化为空
        isFreeTraining: true // 标记为自由训练模式
      });
      
      // 开始定时器
      this.startTimer();
      return;
    }
    
    if (options.planId && options.workoutId) {
      // 保存计划ID和训练日ID
      this.setData({
        planId: options.planId,
        workoutId: options.workoutId,
        isLoading: true,
        isFreeTraining: false
      });

      // 获取训练数据
      this.fetchTrainingData();
    } else {
      wx.showToast({
        title: '缺少必要参数',
        icon: 'none'
      });
    }
    
    // 监听从模板页面返回的事件
    const eventChannel = this.getOpenerEventChannel();
    if (eventChannel) {
      eventChannel.on('applyTemplate', (data) => {
        console.log('【onLoad】接收到模板数据:', data);
        if (data && data.exercises) {
          this.applyTemplateData(data.exercises);
        }
      });
    }
  },

  onUnload: function() {
    // 页面卸载时清除定时器
    if (this.data.timerInterval) {
      clearInterval(this.data.timerInterval);
    }
  },

  // 获取训练数据
  async fetchTrainingData() {
    try {
      const planId = this.data.planId;
      const workoutId = this.data.workoutId;

      console.log('【fetchTrainingData】开始获取训练数据:', {
        planId: planId,
        workoutId: workoutId
      });

      if (!planId) {
        console.error('无法获取计划ID');
        return;
      }

      wx.showLoading({ title: '加载训练数据...' });

      // 获取训练计划详情
      console.log('【fetchTrainingData】调用getPlanDetail, planId:', planId);
      const planData = await trainingPlanApi.getPlanDetail(planId);

      console.log('【fetchTrainingData】获取到训练计划数据:', planData);

      if (!planData || !planData.workouts || planData.workouts.length === 0) {
        console.error('【fetchTrainingData】训练计划数据不完整:', planData);
        throw new Error('获取训练数据失败: 数据不完整');
      }

      console.log('【fetchTrainingData】训练计划workouts:', planData.workouts);
      console.log('【fetchTrainingData】当前workoutId:', workoutId);

      // 查找对应的训练日
      // 将workoutId转换为字符串进行比较，因为可能存在类型不匹配的问题
      const workoutIdStr = String(workoutId);
      const workout = planData.workouts.find(w => {
        const wIdStr = String(w.id);
        console.log('【fetchTrainingData】比较workout.id:', w.id, '(', typeof w.id, ')', '与workoutId:', workoutId, '(', typeof workoutId, ')', '字符串比较结果:', wIdStr === workoutIdStr);
        return wIdStr === workoutIdStr;
      }) || planData.workouts[0];

      console.log('【fetchTrainingData】找到的workout:', workout);

      if (!workout || !workout.exercises || workout.exercises.length === 0) {
        console.error('【fetchTrainingData】未找到有效的训练日数据:', workout);
        throw new Error('未找到有效的训练日数据');
      }

      // 详细打印workout中的exercises数据
      console.log('【fetchTrainingData】workout.exercises数量:', workout.exercises.length);
      workout.exercises.forEach((exercise, index) => {
        // 打印所有可能包含图片URL的字段
        const imageFields = {
          id: exercise.id,
          exercise_name: exercise.exercise_name,
          set_records_count: exercise.set_records ? exercise.set_records.length : 0
        };

        // 添加所有可能的图片字段
        if (exercise.exercise_image) imageFields.exercise_image = exercise.exercise_image;
        if (exercise.image_url) imageFields.image_url = exercise.image_url;
        if (exercise.image) imageFields.image = exercise.image;
        if (exercise.imageUrl) imageFields.imageUrl = exercise.imageUrl;

        console.log(`【fetchTrainingData】exercise[${index}]:`, imageFields);

        // 打印完整的exercise对象，查看所有字段
        console.log(`【fetchTrainingData】完整exercise[${index}]对象:`, exercise);
      });

      // 处理训练动作数据
      const processedExercises = this.processExercisesData(workout.exercises);

      // 计算总组数和已完成组数
      let totalSets = 0;
      let completedSets = 0;

      processedExercises.forEach(exercise => {
        totalSets += exercise.sets.length;
        completedSets += exercise.sets.filter(set => set.completed).length;
      });

      // 计算进度百分比
      const progressPercent = totalSets > 0 ? Math.floor((completedSets / totalSets) * 100) : 0;

      // 开始定时器
      this.startTimer();

      // 更新状态
      this.setData({
        exercises: processedExercises,
        isLoading: false,
        totalSets,
        completedSets,
        progressPercent,
        // 初始化更新数据结构
        updateData: {
          plan_data: {
            id: planId
          },
          workout_data: [{
            id: workout.id
          }],
          workout_exercise_data: [],
          set_record_data: []
        }
      });

      wx.hideLoading();
    } catch (error) {
      console.error('获取训练数据失败:', error);
      wx.hideLoading();
      wx.showToast({
        title: '加载训练数据失败',
        icon: 'none'
      });

      // 设置默认空数据
      this.setData({
        exercises: [],
        isLoading: false
      });
    }
  },

  // 处理训练动作数据
  processExercisesData(exercises) {
    console.log('【processExercisesData】处理训练动作数据，原始数据:', exercises);

    return exercises.map(exercise => {
      // 处理图片URL，尝试所有可能的图片字段
      let imageUrl = '';
      let sourceField = '无';

      // 检查所有可能的图片字段
      if (exercise.exercise_image) {
        imageUrl = exercise.exercise_image;
        sourceField = 'exercise_image';
      } else if (exercise.image_url) {
        imageUrl = exercise.image_url;
        sourceField = 'image_url';
      } else if (exercise.image) {
        imageUrl = exercise.image;
        sourceField = 'image';
      } else if (exercise.imageUrl) {
        imageUrl = exercise.imageUrl;
        sourceField = 'imageUrl';
      }

      console.log('【processExercisesData】处理前的图片URL:', imageUrl, '来源字段:', sourceField);

      const processedImageUrl = imageHelper.getFullImageUrl(imageUrl);
      console.log('【processExercisesData】处理后的图片URL:', processedImageUrl);

      // 处理组数据
      const sets = Array.isArray(exercise.set_records) ? exercise.set_records.map(record => ({
        id: record.id,
        workout_exercise_id: record.workout_exercise_id,
        type: record.set_type || 'normal',
        weight: record.weight || 0,
        reps: record.reps || 0,
        completed: record.completed || false,
        notes: record.notes || ''
      })) : [];

      // 检查是否有已完成的组
      const hasCompletedSets = sets.some(set => set.completed);

      // 返回处理后的动作数据
      const processedExercise = {
        id: exercise.id,
        name: exercise.exercise_name,
        imageUrl: processedImageUrl,
        category: exercise.exercise_description || '',
        sets: sets,
        hasCompletedSets: hasCompletedSets,
        isExpanded: false // 默认展开
      };

      console.log('【processExercisesData】处理后的动作数据:', processedExercise);
      return processedExercise;
    });
  },

  // 开始定时器
  startTimer() {
    if (this.data.timerInterval) {
      // 如果已经有定时器，先清除
      clearInterval(this.data.timerInterval);
    }

    // 立即格式化并显示时间
    const formattedTime = this.formatTime(this.data.timerSeconds);
    
    // 立即设置timerStarted为true，确保计时器立即显示
    this.setData({
      timerStarted: true,
      timerPaused: false,
      formattedTime: formattedTime
    });

    const interval = setInterval(() => {
      // 只有在计时器未暂停时才增加秒数
      if (!this.data.timerPaused) {
        const newSeconds = this.data.timerSeconds + 1;
        const newFormattedTime = this.formatTime(newSeconds);
        
        this.setData({
          timerSeconds: newSeconds,
          formattedTime: newFormattedTime
        });
      }
    }, 1000);

    this.setData({
      timerInterval: interval
    });
  },

  // 暂停或继续计时器
  pauseResumeTimer() {
    const { timerPaused, workoutId } = this.data;

    if (timerPaused) {
      // 继续计时器
      this.resumeTimer();
      // 更新训练状态为进行中 (如果需要)
      // this.updateWorkoutStatus(workoutId, 'in_progress'); 
    } else {
      // 暂停计时器
      this.pauseTimer();
      // 更新训练状态为暂停 (如果需要)
      // this.updateWorkoutStatus(workoutId, 'paused');
    }
  },

  // 暂停计时器
  pauseTimer() {
    if (this.data.timerInterval) {
      // clearInterval(this.data.timerInterval); // 清除interval的逻辑由startTimer和resumeTimer管理，这里只设置状态
      this.setData({
        // timerInterval: null, // 不设置为null，以便resume时可以判断
        timerPaused: true
      });
      wx.showToast({ title: '计时已暂停', icon: 'none', duration: 1000 });
    }
  },

  // 继续计时器
  resumeTimer() {
    // const interval = setInterval(() => { // interval 的创建和管理应统一在 startTimer
    //   this.setData({
    //     timerSeconds: this.data.timerSeconds + 1
    //   });
    // }, 1000);

    this.setData({
      // timerInterval: interval, // 不重新创建 interval，而是依赖全局的 interval
      timerPaused: false,
      timerStarted: true // 确保计时器在继续时是可见的
    });
    wx.showToast({ title: '计时已继续', icon: 'none', duration: 1000 });
  },

  // 更新训练状态
  async updateWorkoutStatus(workoutId, status) {
    if (!workoutId) {
      console.error('无法更新训练状态：缺少workoutId');
      return;
    }

    try {
      console.log(`更新训练状态: workoutId=${workoutId}, status=${status}`);

      // 调用API更新训练状态
      await trainingPlanApi.updateWorkoutStatus(workoutId, { status });

      console.log('训练状态更新成功');

      // 更新本地数据
      this.setData({
        'updateData.workout_data[0].status': status
      });
    } catch (error) {
      console.error('更新训练状态失败:', error);
      wx.showToast({
        title: '状态更新失败',
        icon: 'none'
      });
    }
  },

  // 格式化时间为 MM:SS 格式
  formatTime(seconds) {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${String(minutes).padStart(2, '0')}:${String(remainingSeconds).padStart(2, '0')}`;
  },

  // 切换动作详情的展开状态
  toggleExercise(e) {
    const { index } = e.currentTarget.dataset;
    const { exercises } = this.data;

    exercises[index].isExpanded = !exercises[index].isExpanded;

    this.setData({
      exercises
    });
  },

  // 标记组已完成
  markSetCompleted(e) {
    const { exerciseIndex, setIndex } = e.currentTarget.dataset;
    const { exercises } = this.data;

    // 获取当前组
    const set = exercises[exerciseIndex].sets[setIndex];

    // 切换已完成状态
    set.completed = !set.completed;

    // 重新计算已完成组数
    let completedSets = 0;
    exercises.forEach(exercise => {
      // 计算每个运动的已完成组数
      const exerciseCompletedSets = exercise.sets.filter(s => s.completed).length;
      
      // 更新运动的hasCompletedSets属性
      exercise.hasCompletedSets = exerciseCompletedSets > 0;
      exercise.completedSets = exerciseCompletedSets;
      
      // 累加总的已完成组数
      completedSets += exerciseCompletedSets;
    });

    // 计算总重量
    const totalVolume = this.calculateTotalVolume();

    // 计算进度百分比
    const progressPercent = this.data.totalSets > 0 ?
      Math.floor((completedSets / this.data.totalSets) * 100) : 0;

    // 添加到待更新数据
    const setRecord = {
      id: set.id,
      workout_exercise_id: set.workout_exercise_id,
      completed: set.completed
    };

    // 检查是否已存在相同ID的记录
    const setRecordData = this.data.updateData.set_record_data || [];
    const existingIndex = setRecordData.findIndex(item => item.id === set.id);

    if (existingIndex >= 0) {
      // 更新现有记录
      setRecordData[existingIndex] = { ...setRecordData[existingIndex], ...setRecord };
    } else {
      // 添加新记录
      setRecordData.push(setRecord);
    }

    // 更新状态
    this.setData({
      exercises,
      completedSets,
      totalVolume,
      progressPercent,
      'updateData.set_record_data': setRecordData,
      needSave: true
    });
  },

  // 添加新组
  addNewSet(e) {
    const { exerciseIndex } = e.currentTarget.dataset;
    const exercises = this.data.exercises;
    const exercise = exercises[exerciseIndex];
    
    let newSet;
    
    // 检查是否有现有组数据
    if (exercise.sets.length === 0) {
      // 如果没有现有组，创建默认组数据
      newSet = {
        id: Date.now() + '_' + Math.floor(Math.random() * 1000),
        weight: 5, // 默认重量 5kg
        reps: 12,  // 默认次数 12次
        type: 'normal', // 默认为正式组
        completed: false,
        notes: ''
      };
    } else {
      // 获取最后一组的数据作为参考
      const lastSet = exercise.sets[exercise.sets.length - 1];
      
      // 创建新组，从最后一组复制数据，但状态为未完成
      newSet = {
        id: Date.now() + '_' + Math.floor(Math.random() * 1000),
        weight: lastSet.weight,
        reps: lastSet.reps,
        type: lastSet.type,
        completed: false,
        notes: ''
      };
    }
    
    // 添加新组
    exercise.sets.push(newSet);
    
    // 更新总组数
    const totalSets = this.data.totalSets + 1;
    
    // 计算进度百分比
    const progressPercent = totalSets > 0 
      ? Math.floor((this.data.completedSets / totalSets) * 100) 
      : 0;
    
    this.setData({
      exercises,
      totalSets,
      progressPercent,
      needSave: true
    });
  },

  // 处理重量变化
  handleWeightChange(e) {
    const { exerciseIndex, setIndex } = e.currentTarget.dataset;
    const weight = parseFloat(e.detail.value) || 0;

    const { exercises } = this.data;
    const set = exercises[exerciseIndex].sets[setIndex];

    // 更新重量
    set.weight = weight;

    // 如果组已完成，重新计算总重量
    if (set.completed) {
      const totalVolume = this.calculateTotalVolume();
      this.setData({
        totalVolume
      });
    }

    // 添加到待更新数据
    const setRecord = {
      id: set.id,
      workout_exercise_id: set.workout_exercise_id,
      weight: weight
    };

    // 检查是否已存在相同ID的记录
    const setRecordData = this.data.updateData.set_record_data || [];
    const existingIndex = setRecordData.findIndex(item => item.id === set.id);

    if (existingIndex >= 0) {
      // 更新现有记录
      setRecordData[existingIndex] = { ...setRecordData[existingIndex], ...setRecord };
    } else {
      // 添加新记录
      setRecordData.push(setRecord);
    }

    this.setData({
      exercises,
      'updateData.set_record_data': setRecordData,
      needSave: true
    });
  },

  // 处理次数变化
  handleRepsChange(e) {
    const { exerciseIndex, setIndex } = e.currentTarget.dataset;
    const reps = parseInt(e.detail.value) || 0;

    const { exercises } = this.data;
    const set = exercises[exerciseIndex].sets[setIndex];

    // 更新次数
    set.reps = reps;

    // 如果组已完成，重新计算总重量
    if (set.completed) {
      const totalVolume = this.calculateTotalVolume();
      this.setData({
        totalVolume
      });
    }

    // 添加到待更新数据
    const setRecord = {
      id: set.id,
      workout_exercise_id: set.workout_exercise_id,
      reps: reps
    };

    // 检查是否已存在相同ID的记录
    const setRecordData = this.data.updateData.set_record_data || [];
    const existingIndex = setRecordData.findIndex(item => item.id === set.id);

    if (existingIndex >= 0) {
      // 更新现有记录
      setRecordData[existingIndex] = { ...setRecordData[existingIndex], ...setRecord };
    } else {
      // 添加新记录
      setRecordData.push(setRecord);
    }

    this.setData({
      exercises,
      'updateData.set_record_data': setRecordData,
      needSave: true
    });
  },

  // 完成训练
  completeTraining() {
    wx.showModal({
      title: '完成训练',
      content: '确定要结束本次训练吗？',
      confirmText: '确定',
      cancelText: '取消',
      success: (res) => {
        if (res.confirm) {
          // 停止计时器
          if (this.data.timerInterval) {
            clearInterval(this.data.timerInterval);
          }
          
          // 准备训练数据
          const trainingData = {
            planId: this.data.planId,
            workoutId: this.data.workoutId,
            exercises: this.data.exercises,
            timerSeconds: this.data.timerSeconds,
            completedSets: this.data.completedSets,
            totalSets: this.data.totalSets,
            totalVolume: this.data.totalVolume
          };
          
          // 将训练数据传递给保存页面
          wx.navigateTo({
            url: `/pages/training/save-workout/index?trainingData=${encodeURIComponent(JSON.stringify(trainingData))}`
          });
        }
      }
    });
  },

  // 自动保存
  autoSave() {
    // 如果不需要保存，直接返回
    if (!this.data.needSave) {
      return;
    }

    console.log('自动保存训练数据');

    // 保存到后端
    trainingPlanApi.updatePlan(this.data.planId, this.data.updateData)
      .then(() => {
        console.log('自动保存成功');
        this.setData({
          needSave: false
        });
      })
      .catch(error => {
        console.error('自动保存失败:', error);
      });
  },

  // 计算总重量和各项运动的重量
  calculateTotalVolume() {
    const { exercises } = this.data;
    let totalVolume = 0;
    
    // 遍历所有运动
    exercises.forEach(exercise => {
      let exerciseVolume = 0;
      
      // 计算该运动的已完成组的总重量
      exercise.sets.forEach(set => {
        if (set.completed) {
          exerciseVolume += (set.weight || 0) * (set.reps || 0);
        }
      });
      
      // 更新该运动的总重量
      exercise.totalVolume = exerciseVolume;
      // 更新已完成组数
      exercise.completedSets = exercise.sets.filter(set => set.completed).length;
      
      // 累加到总重量
      totalVolume += exerciseVolume;
    });
    
    // 返回计算结果
    return totalVolume;
  },

  // 导航到运动选择页面
  navigateToExerciseSelection() {
    const { planId, workoutId } = this.data;
    
    // 保存当前训练数据到全局变量，以便从选择页面返回时可以恢复
    const app = getApp();
    app.globalData.currentTrainingData = {
      exercises: this.data.exercises,
      timerSeconds: this.data.timerSeconds,
      timerPaused: this.data.timerPaused
    };
    
    // 导航到运动选择页面
    wx.navigateTo({
      url: `/pages/select-training-exercises/index?planId=${planId}&workoutId=${workoutId}&mode=add`,
      events: {
        // 监听从选择页面返回的事件
        'addExercises': (result) => {
          const selectedExercises = result.exercises || [];
          
          if (selectedExercises.length > 0) {
            console.log('从选择页面返回，选择了运动:', selectedExercises);
            
            // 将新选择的运动添加到当前运动列表
            this.addSelectedExercises(selectedExercises);
          }
        }
      }
    });
  },

  // 添加从选择页面返回的运动
  addSelectedExercises(selectedExercises) {
    const { exercises } = this.data;
    
    // 处理每个新选择的运动
    const newExercises = selectedExercises.map(exercise => {
      // 创建默认的组设置
      const defaultSets = [];
      for (let i = 0; i < 4; i++) { // 默认创建4组
        defaultSets.push({
          id: Date.now() + '_' + Math.floor(Math.random() * 1000) + '_' + i,
          type: 'normal',
          weight: 20, // 默认重量
          reps: 12,   // 默认次数
          completed: false
        });
      }
      
      // 返回处理后的运动数据，确保所需的字段都存在
      return {
        id: exercise.id,
        name: exercise.name || exercise.exercise_name,
        imageUrl: exercise.imageUrl || imageHelper.getFullImageUrl(exercise.image),
        category: exercise.description || exercise.exercise_description || '',
        sets: defaultSets,
        hasCompletedSets: false,
        isExpanded: true, // 默认展开
        completedSets: 0,
        totalVolume: 0
      };
    });
    
    // 更新总组数
    const addedSetsCount = newExercises.reduce((total, exercise) => total + exercise.sets.length, 0);
    const totalSets = this.data.totalSets + addedSetsCount;
    
    // 计算新的进度百分比
    const progressPercent = totalSets > 0 
      ? Math.floor((this.data.completedSets / totalSets) * 100)
      : 0;
    
    // 更新状态
    this.setData({
      exercises: [...exercises, ...newExercises],
      totalSets,
      progressPercent,
      needSave: true
    });
    
    // 显示成功提示
    wx.showToast({
      title: `已添加${newExercises.length}个运动`,
      icon: 'success'
    });
  },
  
  // 编辑运动
  editExercise(e) {
    const { index } = e.currentTarget.dataset;
    const exercise = this.data.exercises[index];
    
    wx.showActionSheet({
      itemList: ['替换动作', '向上移动', '向下移动'],
      success: (res) => {
        if (res.tapIndex === 0) {
          // 替换动作
          this.replaceExercise(index);
        } else if (res.tapIndex === 1) {
          // 向上移动
          this.moveExercise(index, -1);
        } else if (res.tapIndex === 2) {
          // 向下移动
          this.moveExercise(index, 1);
        }
      }
    });
  },
  
  // 替换运动
  replaceExercise(index) {
    const { planId, workoutId } = this.data;
    const currentExercise = this.data.exercises[index];
    
    // 保存当前训练数据到全局变量
    const app = getApp();
    app.globalData.currentTrainingData = {
      exercises: this.data.exercises,
      timerSeconds: this.data.timerSeconds,
      timerPaused: this.data.timerPaused,
      replaceExerciseIndex: index
    };
    
    // 导航到运动选择页面，请求替换单个运动
    wx.navigateTo({
      url: `/pages/select-training-exercises/index?planId=${planId}&workoutId=${workoutId}&mode=replace`,
      events: {
        'replaceExercise': (result) => {
          const selectedExercise = result.exercise;
          
          if (selectedExercise) {
            console.log('从选择页面返回，选择了替换运动:', selectedExercise);
            
            // 保留原运动的组设置，只替换运动信息
            const { exercises } = this.data;
            exercises[index].id = selectedExercise.id;
            exercises[index].name = selectedExercise.name || selectedExercise.exercise_name;
            exercises[index].imageUrl = selectedExercise.imageUrl || imageHelper.getFullImageUrl(selectedExercise.image);
            exercises[index].category = selectedExercise.description || selectedExercise.exercise_description || '';
            
            // 更新状态
            this.setData({
              exercises,
              needSave: true
            });
            
            // 显示成功提示
            wx.showToast({
              title: '已替换动作',
              icon: 'success'
            });
          }
        }
      }
    });
  },
  
  // 移动运动位置
  moveExercise(index, direction) {
    const { exercises } = this.data;
    const newIndex = index + direction;
    
    // 检查新位置是否有效
    if (newIndex < 0 || newIndex >= exercises.length) {
      wx.showToast({
        title: '无法移动',
        icon: 'none'
      });
      return;
    }
    
    // 交换位置
    const temp = exercises[index];
    exercises[index] = exercises[newIndex];
    exercises[newIndex] = temp;
    
    // 更新状态
    this.setData({
      exercises,
      needSave: true
    });
  },
  
  // 删除运动
  deleteExercise(e) {
    const { index } = e.currentTarget.dataset;
    const exercise = this.data.exercises[index];
    
    wx.showModal({
      title: '删除动作',
      content: `确定要删除"${exercise.name}"及其所有组数据吗？`,
      confirmText: '删除',
      confirmColor: '#F44336',
      success: (res) => {
        if (res.confirm) {
          const { exercises, totalSets, completedSets } = this.data;
          
          // 计算要删除的组数和已完成组数
          const exerciseSetsCount = exercise.sets.length;
          const exerciseCompletedSets = exercise.sets.filter(set => set.completed).length;
          
          // 从列表中移除该运动
          exercises.splice(index, 1);
          
          // 更新组数和进度
          const newTotalSets = totalSets - exerciseSetsCount;
          const newCompletedSets = completedSets - exerciseCompletedSets;
          const progressPercent = newTotalSets > 0 
            ? Math.floor((newCompletedSets / newTotalSets) * 100)
            : 0;
          
          // 重新计算总重量
          const totalVolume = this.calculateTotalVolume();
          
          // 更新状态
          this.setData({
            exercises,
            totalSets: newTotalSets,
            completedSets: newCompletedSets,
            progressPercent,
            totalVolume,
            needSave: true
          });
          
          // 显示提示
          wx.showToast({
            title: '已删除动作',
            icon: 'success'
          });
        }
      }
    });
  },
  
  // 删除组
  deleteSet(e) {
    const { exerciseIndex, setIndex } = e.currentTarget.dataset;
    const { exercises, totalSets, completedSets } = this.data;
    const exercise = exercises[exerciseIndex];
    const set = exercise.sets[setIndex];
    
    // 检查组是否已完成，如果完成需要更新已完成组数
    const isCompleted = set.completed;
    
    // 从运动中移除该组
    exercise.sets.splice(setIndex, 1);
    
    // 更新总组数和已完成组数
    const newTotalSets = totalSets - 1;
    const newCompletedSets = isCompleted ? completedSets - 1 : completedSets;
    
    // 更新该运动的统计信息
    exercise.completedSets = exercise.sets.filter(s => s.completed).length;
    exercise.hasCompletedSets = exercise.completedSets > 0;
    
    // 计算进度百分比
    const progressPercent = newTotalSets > 0 
      ? Math.floor((newCompletedSets / newTotalSets) * 100)
      : 0;
    
    // 如果组已完成，重新计算总重量
    let totalVolume = this.data.totalVolume;
    if (isCompleted) {
      totalVolume = this.calculateTotalVolume();
    }
    
    // 更新状态
    this.setData({
      exercises,
      totalSets: newTotalSets,
      completedSets: newCompletedSets,
      progressPercent,
      totalVolume,
      needSave: true
    });
  },
  
  // 处理组类型变更
  handleSetTypeChange(e) {
    const { exerciseIndex, setIndex } = e.currentTarget.dataset;
    const typeIndex = parseInt(e.detail.value);
    
    // 类型映射
    const typeMapping = ['warmup', 'decrease', 'normal'];
    const type = typeMapping[typeIndex];
    
    const { exercises } = this.data;
    const set = exercises[exerciseIndex].sets[setIndex];
    
    // 更新类型
    set.type = type;
    
    // 添加到待更新数据
    const setRecord = {
      id: set.id,
      workout_exercise_id: set.workout_exercise_id,
      set_type: type
    };
    
    // 检查是否已存在相同ID的记录
    const setRecordData = this.data.updateData.set_record_data || [];
    const existingIndex = setRecordData.findIndex(item => item.id === set.id);
    
    if (existingIndex >= 0) {
      // 更新现有记录
      setRecordData[existingIndex] = { ...setRecordData[existingIndex], ...setRecord };
    } else {
      // 添加新记录
      setRecordData.push(setRecord);
    }
    
    this.setData({
      exercises,
      'updateData.set_record_data': setRecordData,
      needSave: true
    });
  },

  /**
   * 模板功能相关方法
   */

  // 加载模板
  loadTemplate() {
    console.log('【loadTemplate】准备跳转到模板页面');
    wx.navigateTo({
      url: '/pages/training-templates/index?fromTrainingSession=true',
      success: (res) => {
        // 监听模板选择事件
        res.eventChannel.on('templateSelected', (data) => {
          console.log('【loadTemplate】接收到选中的模板:', data);
          this.applyTemplateData(data.template);
        });
      },
      fail: (err) => {
        console.error('跳转模板页面失败:', err);
        wx.showToast({
          title: '页面跳转失败',
          icon: 'none'
        });
      }
    });
  },

  // 应用模板数据
  async applyTemplateData(template) {
    try {
      wx.showLoading({ title: '加载模板中...' });
      
      console.log('【applyTemplateData】应用模板数据:', template);
      
      // 检查模板数据结构
      if (!template || !template.exercises || template.exercises.length === 0) {
        throw new Error('模板数据不完整');
      }
      
      // 调用应用模板API
      const templateExercises = await workoutTemplatesApi.applyWorkoutTemplate(template.id);
      
      console.log('【applyTemplateData】API返回的训练动作:', templateExercises);
      
      // 处理模板数据并转换为当前页面的数据格式
      const processedExercises = this.processTemplateExercises(templateExercises);
      
      // 计算总组数
      let totalSets = 0;
      processedExercises.forEach(exercise => {
        totalSets += exercise.sets.length;
      });
      
      // 替换当前所有动作
      this.setData({
        exercises: processedExercises,
        totalSets,
        completedSets: 0,
        progressPercent: 0,
        totalVolume: 0,
        needSave: true
      });
      
      wx.hideLoading();
      wx.showToast({
        title: '模板加载成功',
        icon: 'success'
      });
      
    } catch (error) {
      console.error('应用模板失败:', error);
      wx.hideLoading();
      wx.showToast({
        title: '加载模板失败',
        icon: 'none'
      });
    }
  },

  // 处理模板训练动作数据
  processTemplateExercises(templateExercises) {
    return templateExercises.map((exercise, exerciseIndex) => {
      // 生成组数据
      const sets = [];
      for (let i = 0; i < exercise.sets; i++) {
        sets.push({
          id: `temp_${Date.now()}_${exerciseIndex}_${i}`,
          setNumber: i + 1,
          weight: exercise.weight || '',
          reps: exercise.reps || '',
          completed: false,
          type: 'normal'
        });
      }
      
      return {
        id: exercise.exercise_id,
        name: exercise.exercise_name || `动作 ${exerciseIndex + 1}`,
        imageUrl: imageHelper.getFullImageUrl(exercise.exercise_image || ''),
        category: exercise.muscle_group || '',
        sets: sets,
        isExpanded: false,
        completedSets: 0,
        hasCompletedSets: false,
        totalVolume: 0,
        notes: exercise.notes || ''
      };
    });
  },

  // 保存为模板
  saveAsTemplate() {
    console.log('【saveAsTemplate】准备保存为模板');
    
    // 检查是否有训练动作
    if (!this.data.exercises || this.data.exercises.length === 0) {
      wx.showToast({
        title: '请先添加训练动作',
        icon: 'none'
      });
      return;
    }
    
    // 重置表单并显示模态框
    this.setData({
      showSaveTemplateModal: true,
      templateForm: {
        name: '',
        description: '',
        estimated_duration: 60, // 默认60分钟
        visibilityIndex: 2
      }
    });
  },

  // 关闭保存模板模态框
  closeSaveTemplateModal() {
    this.setData({
      showSaveTemplateModal: false
    });
  },

  // 阻止事件冒泡（防止点击弹窗内容时关闭弹窗）
  stopPropagation() {
    // 空方法，仅用于阻止事件冒泡
  },

  // 处理模板名称输入
  onTemplateNameInput(e) {
    this.setData({
      'templateForm.name': e.detail.value
    });
  },

  // 处理模板描述输入
  onTemplateDescInput(e) {
    this.setData({
      'templateForm.description': e.detail.value
    });
  },

  // 处理可见性选择
  onVisibilityChange(e) {
    this.setData({
      'templateForm.visibilityIndex': parseInt(e.detail.value)
    });
  },

  // 确认保存模板
  async confirmSaveTemplate() {
    const { templateForm, visibilityOptions, exercises } = this.data;
    
    // 验证必填字段
    if (!templateForm.name.trim()) {
      wx.showToast({
        title: '请输入模板名称',
        icon: 'none'
      });
      return;
    }
    
    try {
      wx.showLoading({ title: '保存中...' });
      
      // 准备模板数据
      const templateData = {
        name: templateForm.name.trim(),
        description: templateForm.description.trim(),
        estimated_duration: templateForm.estimated_duration || 60,
        visibility: visibilityOptions[templateForm.visibilityIndex].value,
        exercises: this.convertExercisesToTemplateFormat(exercises)
      };
      
      console.log('【confirmSaveTemplate】准备创建模板:', templateData);
      
      // 调用创建模板API
      const result = await workoutTemplatesApi.createWorkoutTemplate(templateData);
      
      console.log('【confirmSaveTemplate】模板创建成功:', result);
      
      wx.hideLoading();
      this.setData({
        showSaveTemplateModal: false
      });
      
      wx.showToast({
        title: '模板保存成功',
        icon: 'success'
      });
      
    } catch (error) {
      console.error('保存模板失败:', error);
      wx.hideLoading();
      wx.showToast({
        title: '保存失败',
        icon: 'none'
      });
    }
  },

  // 将当前训练动作转换为模板格式
  convertExercisesToTemplateFormat(exercises) {
    return exercises.map((exercise, index) => ({
      exercise_id: exercise.id,
      sets: exercise.sets.length,
      reps: exercise.sets.length > 0 ? exercise.sets[0].reps : '',
      weight: exercise.sets.length > 0 ? exercise.sets[0].weight : '',
      rest_seconds: 90, // 默认休息时间
      notes: exercise.notes || '',
      exercise_type: 'weight_reps',
      superset_group: null
    }));
  },

  // 处理预计时长输入
  onTemplateDurationInput(e) {
    this.setData({
      'templateForm.estimated_duration': parseInt(e.detail.value) || 60
    });
  }
})