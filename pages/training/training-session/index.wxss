/* 训练执行页面样式 */
.container {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f5f5f5;
  padding-bottom: 30rpx;
}

/* 加载状态 */
.loading-container {
  height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
}

.loading {
  color: #888;
  font-size: 32rpx;
}

/* 顶部区域 */
.training-header {
  background-color: #fff;
  padding: 24rpx;
  border-bottom: 1rpx solid #e0e0e0;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  position: sticky;
  top: 0;
  z-index: 10;
}

/* 统计信息容器 */
.stats-container {
  display: flex;
  padding: 20rpx 0;
  margin-bottom: 20rpx;
  border-radius: 10rpx;
  background-color: #f8f8f8;
  overflow: hidden;
}

/* 计时器样式 */
.timer-display {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 0 30rpx;
  width: 160rpx;
}

.timer-icon {
  font-size: 40rpx;
  margin-bottom: 8rpx;
}

.timer-value {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

/* 分隔线 */
.stats-divider {
  width: 2rpx;
  background-color: #ddd;
  height: 80rpx;
  margin: auto 0;
}

/* 统计卡片 */
.stats-card {
  flex: 1;
  display: flex;
  justify-content: space-around;
  padding: 0 10rpx;
}

.stats-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 0 10rpx;
}

.stats-value {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 4rpx;
}

.stats-label {
  font-size: 24rpx;
  color: #888;
}

/* 控制按钮 */
.control-buttons {
  display: flex;
  justify-content: space-between;
  margin-top: 10rpx;
}

.pause-btn, .complete-btn {
  padding: 16rpx 0;
  border-radius: 8rpx;
  text-align: center;
  font-size: 30rpx;
  font-weight: 500;
}

.pause-btn {
  flex: 1;
  margin-right: 10rpx;
  background-color: #f5f5f5;
  color: #333;
  border: 1rpx solid #ddd;
}

.pause-btn.resume {
  background-color: #4CAF50;
  color: white;
  border: 1rpx solid #4CAF50;
}

.complete-btn {
  flex: 1;
  margin-left: 10rpx;
  background-color: #2196F3;
  color: white;
  border: 1rpx solid #2196F3;
}

/* 添加练习按钮 */
.add-exercise-container {
  display: flex;
  gap: 20rpx;
  padding: 20rpx 24rpx;
}

.add-exercise-btn {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 80rpx;
  background-color: #f8f8f8;
  border: 1rpx dashed #ccc;
  border-radius: 8rpx;
  color: #666;
  font-size: 28rpx;
}

.add-exercise-btn .add-icon {
  font-size: 32rpx;
  margin-right: 8rpx;
}

.load-template-btn {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 80rpx;
  background-color: #E3F2FD;
  color: #1976D2;
  border: none;
  border-radius: 8rpx;
  font-size: 28rpx;
  font-weight: 500;
}

.load-template-btn .template-icon {
  margin-right: 8rpx;
  font-size: 32rpx;
}

.load-template-btn:active {
  background-color: #BBDEFB;
}

/* 底部操作区域 */
.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: white;
  border-top: 2rpx solid #f0f0f0;
  padding: 20rpx 40rpx;
  padding-bottom: calc(20rpx + env(safe-area-inset-bottom));
  box-shadow: 0 -4rpx 16rpx rgba(0, 0, 0, 0.1);
  z-index: 100;
}

.save-template-btn {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 24rpx 40rpx;
  border-radius: 16rpx;
  border: none;
  font-size: 28rpx;
  font-weight: 500;
  background-color: #E8F5E9;
  color: #388E3C;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.save-template-btn:active {
  background-color: #C8E6C9;
  transform: scale(0.98);
}

.save-template-btn .template-icon {
  margin-right: 12rpx;
  font-size: 32rpx;
}

.save-template-btn .template-text {
  font-size: 28rpx;
}

/* 练习列表 */
.exercise-list {
  flex: 1;
  padding: 0 24rpx;
  padding-bottom: 140rpx; /* 为底部按钮留出空间 */
}

/* 练习卡片 */
.exercise-card {
  background-color: #fff;
  margin-bottom: 20rpx;
  border-radius: 10rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.exercise-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.exercise-basic-info {
  display: flex;
  align-items: center;
  flex: 1;
}

.exercise-image {
  width: 100rpx;
  height: 100rpx;
  border-radius: 8rpx;
  margin-right: 20rpx;
  background-color: #f0f0f0;
}

.exercise-info {
  flex: 1;
}

.exercise-name {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 6rpx;
}

.exercise-summary {
  font-size: 26rpx;
  color: #888;
}

/* 练习操作按钮 */
.exercise-actions {
  display: flex;
  align-items: center;
}

.action-btn {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-left: 10rpx;
  border-radius: 50%;
  background-color: #f5f5f5;
}

.edit-btn {
  color: #2196F3;
}

.delete-btn {
  color: #F44336;
}

.expand-btn {
  padding: 0;
}

.action-icon {
  font-size: 28rpx;
}

/* 展开图标 */
.expand-icon {
  width: 30rpx;
  height: 30rpx;
  position: relative;
  transition: transform 0.3s;
}

.expand-icon::before, .expand-icon::after {
  content: '';
  position: absolute;
  background-color: #999;
}

.expand-icon::before {
  width: 100%;
  height: 4rpx;
  top: 50%;
  left: 0;
  transform: translateY(-50%);
}

.expand-icon::after {
  width: 4rpx;
  height: 100%;
  left: 50%;
  top: 0;
  transform: translateX(-50%);
  transition: opacity 0.3s;
}

.expand-icon.expanded::after {
  opacity: 0;
}

/* 练习详细信息 */
.exercise-details {
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.3s ease-out;
}

.exercise-details.expanded {
  max-height: 2000rpx;
}

/* 组列表 */
.sets-list {
  padding: 20rpx;
}

/* 组表头 */
.set-header {
  display: flex;
  padding: 0 10rpx 15rpx;
  border-bottom: 1rpx solid #f0f0f0;
  margin-bottom: 15rpx;
}

.set-header-text {
  flex: 1;
  font-size: 24rpx;
  color: #888;
  text-align: center;
}

/* 组项目 */
.set-item {
  display: flex;
  align-items: center;
  padding: 15rpx 0;
  border-bottom: 1rpx solid #f5f5f5;
}

.set-number {
  width: 60rpx;
  font-size: 28rpx;
  color: #666;
  text-align: center;
}

.set-type-selector {
  flex: 1;
}

.set-type {
  padding: 8rpx 16rpx;
  border-radius: 30rpx;
  font-size: 24rpx;
  text-align: center;
  background-color: #f5f5f5;
  margin: 0 10rpx;
  max-width: 120rpx;
}

.set-type.warmup {
  background-color: #E3F2FD;
  color: #2196F3;
}

.set-type.decrease {
  background-color: #FFF8E1;
  color: #FFC107;
}

.set-type.normal {
  background-color: #E8F5E9;
  color: #4CAF50;
}

.set-info {
  flex: 2;
  display: flex;
  align-items: center;
  justify-content: center;
}

.weight-input, .reps-input {
  width: 80rpx;
  height: 60rpx;
  text-align: center;
  background-color: #f5f5f5;
  border-radius: 6rpx;
  margin: 0 8rpx;
  font-size: 28rpx;
  color: #333;
}

.multiply {
  margin: 0 6rpx;
  color: #888;
}

.unit {
  margin-left: 6rpx;
  color: #888;
  font-size: 24rpx;
}

/* 组操作按钮 */
.set-actions {
  display: flex;
  align-items: center;
  flex: 1;
  justify-content: center;
}

.complete-set-btn {
  width: 50rpx;
  height: 50rpx;
  border-radius: 50%;
  background-color: #f5f5f5;
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 0 8rpx;
}

.complete-set-btn.completed {
  background-color: #4CAF50;
  color: white;
}

.delete-set-btn {
  width: 50rpx;
  height: 50rpx;
  border-radius: 50%;
  background-color: #f5f5f5;
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 0 8rpx;
  color: #F44336;
}

.check-icon, .delete-icon {
  font-size: 24rpx;
}

/* 添加组按钮 */
.add-set-container {
  padding: 20rpx 0 10rpx;
  display: flex;
  justify-content: center;
}

.add-set-btn {
  padding: 10rpx 30rpx;
  background-color: #f5f5f5;
  border-radius: 30rpx;
  color: #666;
  font-size: 26rpx;
  display: inline-flex;
  align-items: center;
}

.add-set-btn .add-icon {
  margin-right: 6rpx;
}

/* 已完成组数据统计 */
.completed-sets-summary {
  margin-top: 20rpx;
  padding: 15rpx;
  background-color: #f8f8f8;
  border-radius: 8rpx;
  display: flex;
  justify-content: space-between;
}

.summary-title, .summary-volume {
  font-size: 24rpx;
  color: #666;
}

/* 底部提示 */
.bottom-tips {
  text-align: center;
  padding: 30rpx 0;
  color: #999;
  font-size: 24rpx;
}

/* 空练习提示 */
.empty-exercises-tip {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 300rpx;
  color: #999;
  font-size: 28rpx;
}

/* 模态框样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
}

.modal-content {
  background-color: white;
  border-radius: 24rpx;
  width: 640rpx;
  max-height: 85vh;
  overflow: hidden;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
  margin: 40rpx 20rpx;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32rpx 32rpx 20rpx;
  border-bottom: 2rpx solid #f0f0f0;
}

.modal-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.modal-close {
  width: 40rpx;
  height: 40rpx;
  border-radius: 50%;
  background-color: #f5f5f5;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 24rpx;
  color: #666;
}

.modal-body {
  padding: 32rpx;
  max-height: 50vh;
  overflow-y: auto;
}

.form-group {
  margin-bottom: 32rpx;
}

.form-label {
  display: block;
  margin-bottom: 12rpx;
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.form-input {
  width: 100%;
  padding: 28rpx 20rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 12rpx;
  font-size: 30rpx;
  color: #333;
  background-color: #fafafa;
  box-sizing: border-box;
  min-height: 88rpx;
}

.form-input:focus {
  border-color: #1976D2;
  background-color: white;
}

.form-textarea {
  width: 100%;
  min-height: 140rpx;
  padding: 28rpx 20rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 12rpx;
  font-size: 30rpx;
  color: #333;
  background-color: #fafafa;
  box-sizing: border-box;
  resize: none;
}

.form-textarea:focus {
  border-color: #1976D2;
  background-color: white;
}

.picker-display {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 28rpx 20rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 12rpx;
  background-color: #fafafa;
  font-size: 30rpx;
  color: #333;
  min-height: 88rpx;
  box-sizing: border-box;
}

.picker-arrow {
  color: #666;
  font-size: 24rpx;
}

.modal-footer {
  display: flex;
  padding: 20rpx 32rpx 32rpx;
  gap: 16rpx;
}

.modal-btn {
  flex: 1;
  padding: 20rpx;
  border-radius: 12rpx;
  font-size: 28rpx;
  font-weight: 500;
  border: none;
  min-height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.cancel-btn {
  background-color: #f5f5f5;
  color: #666;
}

.confirm-btn {
  background-color: #1976D2;
  color: white;
}