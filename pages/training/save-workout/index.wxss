/* 保存训练页面样式 */
/* 移除Taro UI样式引用，完全使用自定义样式 */

.container {
  padding: 0;
  background-color: #f8f8f8;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* 头部导航栏 */
.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 30rpx;
  background-color: #ffffff;
  border-bottom: 1rpx solid #eeeeee;
  position: sticky;
  top: 0;
  z-index: 10;
}

.back-icon {
  font-size: 40rpx;
  color: #333333;
}

.page-title {
  font-size: 36rpx;
  font-weight: 500;
  color: #333333;
}

.save-btn {
  padding: 14rpx 30rpx;
  background-color: #4a90e2;
  color: #ffffff;
  border-radius: 30rpx;
  font-size: 28rpx;
  font-weight: 500;
}

.save-btn.disabled {
  background-color: #b2b2b2;
}

/* 内容区块通用样式 */
.section {
  background-color: #ffffff;
  margin-bottom: 20rpx;
  padding: 30rpx;
}

.section-title {
  font-size: 30rpx;
  color: #888888;
  margin-bottom: 20rpx;
}

/* 标题输入区域 */
.title-section {
  padding: 30rpx;
}

.title-input {
  font-size: 36rpx;
  font-weight: 500;
  color: #333333;
  border: none;
  width: 100%;
}

/* 训练数据统计区域 */
.stats-section {
  display: flex;
  justify-content: space-around;
  padding: 40rpx 30rpx;
}

.stat-item {
  text-align: center;
}

.stat-value {
  font-size: 40rpx;
  color: #333333;
  font-weight: 500;
}

.stat-label {
  font-size: 28rpx;
  color: #888888;
  margin-top: 10rpx;
}

/* 日期区域 */
.date-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.date-label {
  font-size: 30rpx;
  color: #333333;
}

.date-value {
  font-size: 30rpx;
  color: #888888;
}

/* 肌肉分布图表区域 */
.muscle-chart-section {
  padding: 30rpx;
}

.muscle-chart {
  margin-top: 20rpx;
}

.muscle-bar {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}

.muscle-name {
  width: 100rpx;
  font-size: 28rpx;
  color: #333333;
}

.muscle-bar-outer {
  flex: 1;
  height: 20rpx;
  background-color: #f0f0f0;
  border-radius: 10rpx;
  margin: 0 20rpx;
  overflow: hidden;
}

.muscle-bar-inner {
  height: 100%;
  background-color: #4a90e2;
  border-radius: 10rpx;
}

.muscle-percentage {
  width: 70rpx;
  font-size: 24rpx;
  color: #888888;
  text-align: right;
}

/* 训练内容预览区域 */
.exercises-preview {
  padding: 30rpx;
}

.preview-text {
  font-size: 30rpx;
  color: #333333;
  margin-bottom: 20rpx;
}

.view-details {
  font-size: 28rpx;
  color: #4a90e2;
  text-align: center;
  margin-bottom: 20rpx;
}

.arrow {
  font-size: 24rpx;
  margin-left: 10rpx;
}

.exercise-list {
  margin-top: 20rpx;
  border-top: 1rpx solid #eeeeee;
  padding-top: 20rpx;
}

.exercise-item {
  margin-bottom: 20rpx;
}

.exercise-name {
  font-size: 30rpx;
  color: #333333;
  margin-bottom: 10rpx;
}

.exercise-sets {
  display: flex;
  flex-wrap: wrap;
}

.exercise-set {
  font-size: 26rpx;
  color: #666666;
  background-color: #f5f5f5;
  padding: 6rpx 16rpx;
  border-radius: 20rpx;
  margin-right: 16rpx;
  margin-bottom: 12rpx;
}

/* 图片上传区域 */
.photo-section {
  padding-bottom: 40rpx;
}

/* 自定义图片选择器样式 */
.image-picker {
  width: 100%;
  margin-top: 20rpx;
}

.image-picker-list {
  display: flex;
  flex-wrap: wrap;
}

.image-item {
  width: 200rpx;
  height: 200rpx;
  margin-right: 20rpx;
  margin-bottom: 20rpx;
  position: relative;
  border-radius: 10rpx;
  overflow: hidden;
}

.image-item image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.image-remove {
  position: absolute;
  top: 0;
  right: 0;
  width: 40rpx;
  height: 40rpx;
  background-color: rgba(0, 0, 0, 0.5);
  color: #ffffff;
  text-align: center;
  line-height: 40rpx;
  font-size: 24rpx;
  z-index: 1;
}

.image-add-btn {
  width: 200rpx;
  height: 200rpx;
  background-color: #f5f5f5;
  border-radius: 10rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #888888;
}

.add-icon {
  font-size: 60rpx;
  line-height: 1;
  margin-bottom: 10rpx;
}

.add-text {
  font-size: 24rpx;
}

/* 描述输入区域 */
.desc-section {
  padding-bottom: 40rpx;
}

.desc-textarea {
  width: 100%;
  min-height: 200rpx;
  font-size: 30rpx;
  line-height: 1.6;
  color: #333333;
  padding: 20rpx;
  box-sizing: border-box;
  border: 1rpx solid #eeeeee;
  border-radius: 10rpx;
}

/* 可见性设置区域 */
.visibility-section {
  margin-bottom: 100rpx;
}

.visibility-group {
  display: flex;
  flex-direction: column;
}

.visibility-item {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.visibility-item text {
  margin-left: 20rpx;
  font-size: 30rpx;
  color: #333333;
}

/* 底部区域 */
.bottom-section {
  padding: 30rpx;
  margin-top: auto;
  display: flex;
  justify-content: space-between;
}

.discard-btn {
  width: 30%;
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  background-color: #f5f5f5;
  color: #ff5151;
  font-size: 30rpx;
  border-radius: 40rpx;
}

.save-btn-large {
  width: 65%;
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  background-color: #4a90e2;
  color: #ffffff;
  font-size: 30rpx;
  border-radius: 40rpx;
} 