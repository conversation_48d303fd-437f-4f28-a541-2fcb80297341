/**
 * 格式化日期为 YYYY-MM-DD 格式
 */
var formatDate = function(dateStr) {
  if (!dateStr) return '';
  
  var date = getDate(dateStr);
  var year = date.getFullYear();
  var month = date.getMonth() + 1;
  var day = date.getDate();
  
  // 补零
  if (month < 10) month = '0' + month;
  if (day < 10) day = '0' + day;
  
  return year + '-' + month + '-' + day;
};

/**
 * 格式化时间为 HH:MM:SS 格式
 */
var formatTime = function(seconds) {
  if (!seconds || typeof seconds !== 'number') return '0分0秒';
  
  var hours = Math.floor(seconds / 3600);
  var minutes = Math.floor((seconds % 3600) / 60);
  var remainingSeconds = Math.floor(seconds % 60);
  
  if (hours > 0) {
    return hours + '小时' + minutes + '分钟';
  } else {
    return minutes + '分' + remainingSeconds + '秒';
  }
};

module.exports = {
  formatDate: formatDate,
  formatTime: formatTime
}; 