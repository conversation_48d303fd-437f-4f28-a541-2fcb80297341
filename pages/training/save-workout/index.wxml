<wxs src="./utils.wxs" module="utils" />
<!-- 保存训练页面 -->
<view class="container">
  <view class="header">
    <view class="back-icon" bindtap="discardWorkout">
      <text class="iconfont icon-back"></text>
    </view>
    <view class="page-title">保存训练</view>
    <view class="save-btn {{loading ? 'disabled' : ''}}" bindtap="{{loading ? '' : 'saveWorkout'}}">
      {{loading ? '保存中...' : '保存'}}
    </view>
  </view>

  <!-- 标题输入 -->
  <view class="section title-section">
    <input 
      class="title-input" 
      value="{{title}}" 
      placeholder="训练标题" 
      maxlength="50"
      bindinput="onTitleInput"
    />
  </view>

  <!-- 训练数据统计 -->
  <view class="section stats-section">
    <view class="stat-item">
      <view class="stat-value">{{utils.formatTime(duration)}}</view>
      <view class="stat-label">时长</view>
    </view>
    <view class="stat-item">
      <view class="stat-value">{{totalVolume}} kg</view>
      <view class="stat-label">总重量</view>
    </view>
    <view class="stat-item">
      <view class="stat-value">{{totalSets}}</view>
      <view class="stat-label">组数</view>
    </view>
    <view class="stat-item">
      <view class="stat-value">{{exerciseCount}}</view>
      <view class="stat-label">动作数</view>
    </view>
  </view>

  <!-- 日期时间信息 -->
  <view class="section date-section">
    <text class="date-label">完成时间</text>
    <text class="date-value">{{utils.formatDate(formattedDate)}}</text>
  </view>

  <!-- 肌肉分布图表 -->
  <view class="section muscle-chart-section" wx:if="{{showMuscleDistribution}}">
    <view class="section-title">肌肉分布</view>
    <view class="muscle-chart">
      <!-- 这里可以根据实际需要添加肌肉分布图表组件 -->
      <view class="muscle-bar" wx:for="{{muscleGroups}}" wx:key="name">
        <view class="muscle-name">{{item.name}}</view>
        <view class="muscle-bar-outer">
          <view class="muscle-bar-inner" style="width: {{item.percentage}}%;"></view>
        </view>
        <view class="muscle-percentage">{{item.percentage}}%</view>
      </view>
    </view>
  </view>
  
  <!-- 训练内容预览 -->
  <view class="section exercises-preview">
    <view class="section-title">训练内容</view>
    <view class="preview-text">{{exerciseCount}}个动作，共{{totalSets}}组</view>
    <view class="view-details" bindtap="toggleExerciseDetails">
      {{showExerciseDetails ? '收起详情' : '查看详情'}} 
      <text class="arrow {{showExerciseDetails ? 'up' : 'down'}}">{{showExerciseDetails ? '▲' : '▼'}}</text>
    </view>
    
    <!-- 详细运动列表（可折叠） -->
    <view class="exercise-list" wx:if="{{showExerciseDetails}}">
      <view class="exercise-item" wx:for="{{trainingData.exercises}}" wx:key="id">
        <view class="exercise-name">{{item.name}}</view>
        <view class="exercise-sets">
          <text class="exercise-set" wx:for="{{item.sets}}" wx:for-item="set" wx:key="id">
            {{set.weight}}kg × {{set.reps}}
          </text>
        </view>
      </view>
    </view>
  </view>

  <!-- 图片上传区域 -->
  <view class="section photo-section">
    <view class="section-title">添加图片/视频</view>
    
    <!-- 原生图片选择器实现 -->
    <view class="image-picker">
      <view class="image-picker-list">
        <view class="image-item" wx:for="{{files}}" wx:key="index">
          <image src="{{item.url}}" mode="aspectFill" bindtap="previewImage" data-index="{{index}}"></image>
          <view class="image-remove" bindtap="removeImage" data-index="{{index}}">×</view>
        </view>
        <view class="image-add-btn" bindtap="chooseImage" wx:if="{{files.length < 9}}">
          <view class="add-icon">+</view>
          <view class="add-text">添加图片</view>
        </view>
      </view>
    </view>
  </view>

  <!-- 描述输入区域 -->
  <view class="section desc-section">
    <view class="section-title">训练描述</view>
    <textarea 
      class="desc-textarea" 
      value="{{description}}" 
      placeholder="记录你的训练心得..." 
      maxlength="1000"
      bindinput="onDescriptionInput"
    ></textarea>
  </view>

  <!-- 可见性设置 -->
  <view class="section visibility-section">
    <view class="section-title">可见性</view>
    <radio-group class="visibility-group" bindchange="onVisibilityChange">
      <label class="visibility-item">
        <radio value="everyone" checked="{{visibility === 'everyone'}}" />
        <text>所有人可见</text>
      </label>
      <label class="visibility-item">
        <radio value="friends" checked="{{visibility === 'friends'}}" />
        <text>仅好友可见</text>
      </label>
      <label class="visibility-item">
        <radio value="private" checked="{{visibility === 'private'}}" />
        <text>仅自己可见</text>
      </label>
    </radio-group>
  </view>

  <!-- 底部区域 -->
  <view class="bottom-section">
    <button class="discard-btn" bindtap="discardWorkout">放弃保存</button>
    <button class="save-btn-large" bindtap="saveWorkout" loading="{{loading}}" disabled="{{loading}}">
      {{loading ? '正在保存...' : '保存到社区'}}
    </button>
  </view>
</view> 