const app = getApp();
const api = require('../../../api/community');
const imageHelper = require('../../../utils/image-helper');

Page({
  /**
   * 页面的初始数据
   */
  data: {
    trainingData: null,
    title: '',
    description: '',
    files: [], // 图片文件列表
    visibility: 'everyone', // 可见性：everyone, friends, private
    loading: false,
    duration: 0, // 训练时长(秒)
    totalSets: 0, // 总组数
    totalVolume: 0, // 总重量(kg)
    exerciseCount: 0, // 动作数量
    uploadedImages: [], // 已上传图片的URL列表
    formattedDate: '', // 格式化后的日期
    formatTime: '', // 格式化后的时间
    showExerciseDetails: false, // 是否显示详细的运动列表
    showMuscleDistribution: false, // 是否显示肌肉分布
    muscleGroups: [
      // 默认的肌肉分布数据，后续可通过分析训练数据生成
      { name: '胸部', percentage: 0 },
      { name: '背部', percentage: 0 },
      { name: '肩部', percentage: 0 },
      { name: '手臂', percentage: 0 },
      { name: '腿部', percentage: 0 },
      { name: '核心', percentage: 0 }
    ],
    workoutId: null // 新增workoutId
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    let trainingData = null;
    
    // 从页面参数中获取训练数据
    if (options.trainingData) {
      try {
        trainingData = JSON.parse(decodeURIComponent(options.trainingData));
        
        // 计算训练数据相关统计信息
        const duration = trainingData.timerSeconds || 0;
        const exercises = trainingData.exercises || [];
        const totalSets = exercises.reduce((total, exercise) => total + exercise.sets.length, 0);
        
        // 计算总重量
        let totalVolume = 0;
        exercises.forEach(exercise => {
          exercise.sets.forEach(set => {
            if (set.completed) {
              totalVolume += (set.weight || 0) * (set.reps || 0);
            }
          });
        });
        
        // 设置初始标题
        const title = `训练记录 - ${this.formatDate(new Date())}`;
        const workoutId = trainingData.workoutId;
        const planId = trainingData.planId;
        // 格式化当前日期和时间
        const formattedDate = this.formatDate(new Date());
        const formatTime = this.formatTime(duration);
        
        // 生成肌肉分布数据
        const muscleGroups = this.generateMuscleDistribution(exercises);
        const showMuscleDistribution = muscleGroups.some(group => group.percentage > 0);
        
        this.setData({
          trainingData,
          title,
          duration,
          totalSets,
          totalVolume,
          exerciseCount: exercises.length,
          formattedDate,
          formatTime,
          muscleGroups,
          showMuscleDistribution,
          workoutId // 保存workoutId以便后续使用
        });
      } catch (e) {
        console.error('解析训练数据失败:', e);
        this.showError('无法加载训练数据');
      }
    } else {
      this.showError('缺少训练数据');
    }
  },

  /**
   * 根据训练动作生成肌肉分布数据
   * 这个函数可以根据实际的肌肉分组和动作分类进行调整
   */
  generateMuscleDistribution(exercises) {
    // 肌肉分布映射表 - 可以根据实际的肌肉分组进行调整
    const muscleMap = {
      // 胸部相关动作名称关键词
      '胸部': ['胸', '卧推', '夹胸', '哑铃飞鸟', '俯卧撑'],
      // 背部相关动作名称关键词
      '背部': ['背', '划船', '引体向上', '下拉', '高位下拉'],
      // 肩部相关动作名称关键词
      '肩部': ['肩', '推举', '前平举', '侧平举', '后平举', '耸肩'],
      // 手臂相关动作名称关键词
      '手臂': ['二头', '三头', '弯举', '臂屈伸', '臂肌'],
      // 腿部相关动作名称关键词
      '腿部': ['腿', '深蹲', '蹲', '腿举', '腿屈伸', '腿弯举'],
      // 核心相关动作名称关键词
      '核心': ['核心', '腹', '卷腹', '仰卧', '平板支撑']
    };
    
    // 初始化肌肉分布计数
    const muscleCounts = {
      '胸部': 0,
      '背部': 0,
      '肩部': 0,
      '手臂': 0,
      '腿部': 0,
      '核心': 0
    };
    
    // 遍历所有运动，根据名称匹配肌肉分组
    exercises.forEach(exercise => {
      const name = exercise.name.toLowerCase();
      
      // 对每个肌肉组进行匹配
      for (const [muscleGroup, keywords] of Object.entries(muscleMap)) {
        // 如果运动名称包含关键词，则计数加1
        if (keywords.some(keyword => name.includes(keyword))) {
          muscleCounts[muscleGroup] += 1;
        }
      }
    });
    
    // 计算总匹配次数
    const totalCount = Object.values(muscleCounts).reduce((sum, count) => sum + count, 0) || 1;
    
    // 转换为百分比
    return Object.entries(muscleCounts).map(([name, count]) => ({
      name,
      percentage: Math.round((count / totalCount) * 100) || 0
    }));
  },

  /**
   * 格式化日期为 YYYY-MM-DD 格式
   */
  formatDate(date) {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  },

  /**
   * 格式化时间为 HH:MM:SS 格式
   */
  formatTime(seconds) {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const remainingSeconds = seconds % 60;
    
    if (hours > 0) {
      return `${hours}小时${minutes}分钟`;
    } else {
      return `${minutes}分${remainingSeconds}秒`;
    }
  },

  /**
   * 处理标题输入
   */
  onTitleInput(e) {
    this.setData({
      title: e.detail.value
    });
  },

  /**
   * 处理描述输入
   */
  onDescriptionInput(e) {
    this.setData({
      description: e.detail.value
    });
  },

  /**
   * 切换动作详情显示状态
   */
  toggleExerciseDetails() {
    this.setData({
      showExerciseDetails: !this.data.showExerciseDetails
    });
  },

  /**
   * 选择图片
   */
  chooseImage() {
    const { files } = this.data;
    const remainCount = 9 - files.length;
    
    if (remainCount <= 0) {
      return this.showError('最多只能上传9张图片');
    }
    
    wx.chooseImage({
      count: remainCount,
      sizeType: ['compressed'],
      sourceType: ['album', 'camera'],
      success: (res) => {
        // 将选中的图片添加到图片列表中
        const newFiles = res.tempFilePaths.map(path => ({
          url: path
        }));
        
        this.setData({
          files: [...files, ...newFiles]
        });
      },
      fail: (err) => {
        console.error('选择图片失败:', err);
      }
    });
  },

  /**
   * 移除图片
   */
  removeImage(e) {
    const { index } = e.currentTarget.dataset;
    const { files } = this.data;
    
    // 移除指定索引的图片
    files.splice(index, 1);
    
    this.setData({
      files
    });
  },

  /**
   * 预览图片
   */
  previewImage(e) {
    const { index } = e.currentTarget.dataset;
    const { files } = this.data;
    
    wx.previewImage({
      current: files[index].url,
      urls: files.map(file => file.url)
    });
  },

  /**
   * 处理可见性变更
   */
  onVisibilityChange(e) {
    this.setData({
      visibility: e.detail.value
    });
  },

  /**
   * 上传图片到服务器
   */
  async uploadImages() {
    const { files } = this.data;
    const uploadedImages = [];
    
    if (files.length === 0) {
      return [];
    }
    
    wx.showLoading({ title: '上传图片中...' });
    
    try {
      for (let i = 0; i < files.length; i++) {
        const file = files[i];
        // 如果已经是网络图片URL，直接添加
        if (file.url.startsWith('http')) {
          uploadedImages.push(file.url);
        } else {
          // 上传本地图片
          const result = await this.uploadFile(file.url);
          uploadedImages.push(result);
        }
      }
      
      wx.hideLoading();
      return uploadedImages;
    } catch (error) {
      wx.hideLoading();
      console.error('图片上传失败:', error);
      this.showError('图片上传失败');
      return [];
    }
  },
  
  /**
   * 上传单个文件
   */
  uploadFile(filePath) {
    return new Promise((resolve, reject) => {
      wx.uploadFile({
        url: app.globalData.baseUrl + '/api/v1/upload/image',
        filePath: filePath,
        name: 'file',
        header: {
          'Authorization': 'Bearer ' + wx.getStorageSync('token')
        },
        success: res => {
          const data = JSON.parse(res.data);
          if (data.success && data.url) {
            resolve(data.url);
          } else {
            reject(new Error(data.message || '上传失败'));
          }
        },
        fail: err => {
          reject(err);
        }
      });
    });
  },

  /**
   * 保存训练记录到社区帖子
   */
  async saveWorkout() {
    const { title, description, trainingData, visibility } = this.data;
    
    if (!title.trim()) {
      return this.showError('请输入标题');
    }
    
    this.setData({ loading: true });
    
    try {
      // 1. 上传图片
      const imageUrls = await this.uploadImages();
      
      // 2. 根据后端接口文档准备数据结构
      const shareData = {
        title: title.trim(),
        content: description.trim() || '',
        workout_data: {
          exercises: this.transformExercisesData(trainingData.exercises || []),
          duration_seconds: trainingData.timerSeconds || 0,
          total_sets: this.data.totalSets,
          total_volume: this.data.totalVolume
        },
        images: imageUrls,
        visibility: visibility,
        tags: this.generateTags(trainingData.exercises || [])
      };
      
      console.log('提交数据:', JSON.stringify(shareData, null, 2));
      
      // 3. 调用新的分享接口 (使用0表示创建新记录)
      const workoutId = this.data.workoutId || 0;
      const result = await api.shareWorkout(workoutId, shareData);
      
      // 发布成功
      this.setData({ loading: false });
      
      // 显示成功提示
      wx.showToast({
        title: '发布成功',
        icon: 'success'
      });
      
      // 延迟返回
      setTimeout(() => {
        // 跳转到帖子详情页，使用返回的帖子ID和关联的训练ID
        const postId = result.id;
        const relatedWorkoutId = result.related_workout_id || '';
        wx.redirectTo({
          url: `/pages/post-detail/index?postId=${postId}&workoutId=${relatedWorkoutId}`
        });
      }, 1500);
      
    } catch (error) {
      this.setData({ loading: false });
      console.error('保存失败:', error);
      const errorMessage = error.detail || error.message || '未知错误';
      this.showError('保存失败，请重试: ' + errorMessage);
    }
  },

  /**
   * 转换动作数据为后端期望的格式
   */
  transformExercisesData(exercises) {
    return exercises.map(exercise => {
      // 转换组数据
      const transformedSets = exercise.sets.map(set => ({
        id: set.id || null, // 如果为空则创建新组
        workout_exercise_id: set.workout_exercise_id || exercise.id || null,
        type: set.type || 'normal',
        weight: set.weight || 0,
        reps: set.reps || 0,
        completed: set.completed || false,
        notes: set.notes || ''
      }));

      return {
        id: exercise.id || null, // 如果为空则创建新动作
        name: exercise.name || '',
        imageUrl: exercise.imageUrl || '',
        category: exercise.category || this.getCategoryFromName(exercise.name),
        sets: transformedSets,
        hasCompletedSets: transformedSets.some(set => set.completed),
        isExpanded: exercise.isExpanded || false,
        totalVolume: this.calculateExerciseVolume(transformedSets),
        completedSets: transformedSets.filter(set => set.completed).length
      };
    });
  },

  /**
   * 根据动作名称推断分类
   */
  getCategoryFromName(exerciseName) {
    const name = exerciseName.toLowerCase();
    
    if (name.includes('胸') || name.includes('卧推') || name.includes('夹胸') || name.includes('俯卧撑')) {
      return '胸部';
    } else if (name.includes('背') || name.includes('划船') || name.includes('引体') || name.includes('下拉')) {
      return '背部';
    } else if (name.includes('肩') || name.includes('推举') || name.includes('平举') || name.includes('耸肩')) {
      return '肩部';
    } else if (name.includes('二头') || name.includes('三头') || name.includes('弯举') || name.includes('臂')) {
      return '手臂';
    } else if (name.includes('腿') || name.includes('深蹲') || name.includes('蹲') || name.includes('腿举')) {
      return '腿部';
    } else if (name.includes('腹') || name.includes('核心') || name.includes('卷腹') || name.includes('平板')) {
      return '核心';
    }
    
    return '其他';
  },

  /**
   * 计算单个动作的总容量
   */
  calculateExerciseVolume(sets) {
    return sets.reduce((total, set) => {
      if (set.completed) {
        return total + (set.weight || 0) * (set.reps || 0);
      }
      return total;
    }, 0);
  },

  /**
   * 根据训练内容生成标签
   */
  generateTags(exercises) {
    const tags = ['训练记录'];
    
    // 收集所有涉及的肌肉分类
    const categories = new Set();
    exercises.forEach(exercise => {
      const category = this.getCategoryFromName(exercise.name);
      if (category && category !== '其他') {
        categories.add(category);
      }
    });
    
    // 添加主要的肌肉分类标签
    categories.forEach(category => {
      tags.push(category);
    });
    
    // 限制标签数量，避免过多
    return tags.slice(0, 5);
  },

  /**
   * 放弃训练记录
   */
  discardWorkout() {
    wx.showModal({
      title: '确认放弃',
      content: '确定要放弃保存训练记录吗？',
      success: (res) => {
        if (res.confirm) {
          wx.navigateBack();
        }
      }
    });
  },

  /**
   * 显示错误信息
   */
  showError(message) {
    wx.showToast({
      title: message,
      icon: 'none'
    });
  }
}); 