/**
 * 首页
 * @file home/home.js
 * @description 展示用户的训练和营养数据，包括日历视图和营养摄入进度
 */

/**
 * @typedef {Object} NutritionGoals
 * @property {number} calories - 卡路里目标（千卡）
 * @property {number} carb - 碳水化合物目标（克）
 * @property {number} protein - 蛋白质目标（克）
 * @property {number} fat - 脂肪目标（克）
 */

/**
 * @typedef {Object} NutritionProgress
 * @property {number} caloriesConsumed - 已摄入卡路里（千卡）
 * @property {number} carb - 已摄入碳水化合物（克）
 * @property {number} protein - 已摄入蛋白质（克）
 * @property {number} fat - 已摄入脂肪（克）
 */

/**
 * @typedef {Object} DateInfo
 * @property {string} id - 日期唯一标识
 * @property {string} weekDay - 星期几（中文）
 * @property {number} day - 日
 * @property {number} month - 月
 * @property {number} year - 年
 * @property {boolean} isSelected - 是否选中
 */

const { API_BASE_URL } = require('../../api/config');
const { wechatLogin, testConnection } = require('../../api/auth');

Page({
  data: {
    // 当前选中的日期信息
    cur: {},
    currentDate: '',
    dates: [], // 日历数据
    selectedDate: null,
    today: new Date().toISOString().split('T')[0],
    isTodaySelected: true,
    scrollToView: '',

    // 训练相关
    showTrainingOptions: false,
    trainingSchedule: null,
    homeSampleImg: '/images/home_train.png', // 已更新为本地路径，不再使用云存储

    // 营养摄入目标
    caloriesGoal: 1800,
    carbGoal: 252,
    proteinGoal: 72,
    fatGoal: 56,

    // 当日营养摄入
    caloriesConsumed: 0,
    carb: 0,
    protein: 0,
    fat: 0,

    // 营养摄入进度
    caloriesPercentage: 0,
    carbPercentage: 0,
    proteinPercentage: 0,
    fatPercentage: 0,

    // 用户信息
    userInfo: null,
    hasUserInfo: false,

    // 饮食记录相关
    mealPopupVisible: false,
    mealType: '',
    imageUrl: '',

    // 页面状态
    isLoading: true,
    error: null
  },

  /**
   * 生命周期函数--监听页面加载
   * @param {Object} options - 页面参数
   */
  onLoad(options) {
    console.log('【首页】页面加载');

    // 获取app实例，用于检查登录状态
    const app = getApp();

    // 如果已经在跳转到登录页面，不执行后续操作
    if (app.globalData.isRedirectingToLogin) {
      console.log('【首页】已经在跳转到登录页面，不执行onLoad初始化');
      return;
    }

    // 先检查登录状态
    if (!app.checkLoginStatus()) {
      console.log('【首页】用户未登录，由app统一处理登录跳转');
      app.redirectToLogin();
      return;
    }

    this.loadMonthCalendar();
    this.getOpenId();

    if (this.data.selectedDate) {
      const selectedDate = new Date(this.data.selectedDate);
      this.setSelectedDate(selectedDate);
      const scrollId = `date${selectedDate.getFullYear()}-${selectedDate.getMonth() + 1}-${selectedDate.getDate()}`;
      this.setData({ scrollToView: scrollId });
    } else {
      this.goToToday();
    }

    this.setData({ mealPopupVisible: false });

    // 从全局变量获取TEDD值作为卡路里目标
    this.setCaloriesGoalFromGlobalTEDD();

    // 获取当天的 workout 数据
    this.fetchTodayWorkout();
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    console.log('【首页】页面显示');

    const app = getApp();

    // 如果已经在跳转到登录页面，不执行后续操作
    if (app.globalData.isRedirectingToLogin) {
      console.log('【首页】已经在跳转到登录页面，不执行onShow初始化');
      return;
    }

    // 先检查登录状态
    if (!app.checkLoginStatus()) {
      console.log('【首页】用户未登录，由app统一处理登录跳转');
      app.redirectToLogin();
      return;
    }

    if (app.globalData.selectedDate) {
      this.setData({
        selectedDate: app.globalData.selectedDate
      });
      app.globalData.selectedDate = null;
    }

    if (this.data.selectedDate) {
      const [year, month, day] = this.data.selectedDate.split('-');
      const selectedDate = new Date(year, parseInt(month) - 1, day);
      this.setSelectedDate(selectedDate);

      const scrollId = `date${selectedDate.getFullYear()}-${selectedDate.getMonth() + 1}-${selectedDate.getDate()}`;
      this.setData({ scrollToView: scrollId });
    }
    this.setData({ mealPopupVisible: false });

    // 确保每次页面显示时都使用最新的TEDD值
    this.setCaloriesGoalFromGlobalTEDD();

    // 获取当天的 workout 数据
    this.fetchTodayWorkout();
  },

  /**
   * 获取用户OpenID
   * 优化处理逻辑，避免重复跳转到登录页面
   */
  async getOpenId() {
    // 获取app实例，用于检查登录状态
    const app = getApp();

    // 如果已经在跳转到登录页面，不执行后续操作
    if (app.globalData.isRedirectingToLogin) {
      console.log('【首页】已经在跳转到登录页面，不执行getOpenId');
      return;
    }

    // 先检查登录状态
    if (!app.checkLoginStatus()) {
      console.log('【首页】用户未登录，由app统一处理登录跳转');
      app.redirectToLogin();
      return;
    }

    try {
      // 引入API模块
      const api = require('../../api/index');

      // 使用API获取用户OpenID
      const openid = await api.auth.getOpenId();

      this.setData({
        'userInfo.openId': openid
      });

      this.setData({ mealPopupVisible: false });
      this.fetchTrainingSchedule();
      this.fetchNutritionData();
    } catch (err) {
      console.error('【首页】获取OpenID失败:', err);

      // 避免直接显示toast并跳转，统一由app处理登录跳转
      if (!app.globalData.isRedirectingToLogin) {
        console.log('【首页】获取OpenID失败，由app统一处理登录跳转');
        app.redirectToLogin();
      }
    }
  },

  /**
   * 获取训练计划
   */
  async fetchTrainingSchedule() {
    try {
      // 模拟获取训练计划数据
      const schedule = Math.random() > 0.5 ? { /* schedule data */ } : null;
      this.setData({ trainingSchedule: schedule });
    } catch (error) {
      console.error('获取训练计划失败:', error);
    }
  },

  /**
   * 获取指定日期的 workout 数据
   * @param {string} [date] - 日期字符串，格式为 YYYY-MM-DD，如果不传则获取当天的数据
   */
  async fetchTodayWorkout(date) {
    try {
      let formattedDate;

      if (date) {
        // 使用传入的日期
        formattedDate = date;
      } else {
        // 获取当前日期，格式为 YYYY-MM-DD
        const today = new Date();
        formattedDate = `${today.getFullYear()}-${String(today.getMonth() + 1).padStart(2, '0')}-${String(today.getDate()).padStart(2, '0')}`;
      }

      console.log('获取训练数据，日期:', formattedDate);

      // 引入 API 模块
      const api = require('../../api/index');

      // 调用 getWorkoutsByDate 函数获取指定日期的 workout 数据
      const workouts = await api.trainingPlan.getWorkoutsByDate(formattedDate);

      console.log('获取到的训练数据:', workouts);

      if (workouts && workouts.length > 0) {
        // 取第一个 workout 显示
        const todayWorkout = workouts[0];

        this.setData({
          todayWorkout: todayWorkout,
          hasTodayWorkout: true
        });
      } else {
        this.setData({
          todayWorkout: null,
          hasTodayWorkout: false
        });
      }
    } catch (error) {
      console.error('获取训练数据失败:', error);
      this.setData({
        todayWorkout: null,
        hasTodayWorkout: false
      });
    }
  },

  /**
   * 处理训练按钮点击
   */
  handleTrainingAction(e) {
    const { action } = e.currentTarget.dataset;
    const { todayWorkout } = this.data;

    if (!todayWorkout || !todayWorkout.id) {
      console.error('没有可用的训练数据');
      return;
    }

    const workoutId = todayWorkout.id;

    switch (action) {
      case 'start':
        this.startWorkout(workoutId);
        break;
      case 'continue':
        this.continueWorkout(workoutId);
        break;
      case 'pause':
        this.pauseWorkout(workoutId);
        break;
      case 'complete':
        this.completeWorkout(workoutId);
        break;
      case 'view':
        this.viewWorkoutDetail(workoutId);
        break;
      default:
        console.error('未知的操作:', action);
    }
  },

  /**
   * 开始训练
   */
  async startWorkout(workoutId) {
    try {
      const api = require('../../api/index');
      // 获取计划ID
      const selectedDate = this.data.selectedDate;
      console.log('使用日期获取训练数据:', selectedDate);
      const workoutData = await api.trainingPlan.getWorkoutsByDate(selectedDate);
      let planId = null;

      // 查找当前workout对应的planId
      if (workoutData && workoutData.length > 0) {
        for (let workout of workoutData) {
          if (workout.id == workoutId) {
            planId = workout.training_plan_id || workout.plan_id;
            break;
          }
        }
      }

      if (!planId) {
        console.error('无法获取训练计划ID');
        wx.showToast({
          title: '无法获取训练计划',
          icon: 'none'
        });
        return;
      }

      // 更新状态为进行中
      await api.trainingPlan.updateWorkoutStatus(workoutId, { status: 'in_progress' });

      // 刷新数据
      this.fetchTodayWorkout();

      // 跳转到训练会话页面
      wx.navigateTo({
        url: `/pages/training/training-session/index?planId=${planId}&workoutId=${workoutId}`
      });
    } catch (error) {
      console.error('开始训练失败:', error);
      wx.showToast({
        title: '操作失败',
        icon: 'none'
      });
    }
  },

  /**
   * 继续训练
   */
  async continueWorkout(workoutId) {
    try {
      const api = require('../../api/index');
      // 获取计划ID
      const selectedDate = this.data.selectedDate;
      console.log('使用日期获取训练数据:', selectedDate);
      const workoutData = await api.trainingPlan.getWorkoutsByDate(selectedDate);
      let planId = null;

      // 查找当前workout对应的planId
      if (workoutData && workoutData.length > 0) {
        for (let workout of workoutData) {
          if (workout.id == workoutId) {
            planId = workout.training_plan_id || workout.plan_id;
            break;
          }
        }
      }

      if (!planId) {
        console.error('无法获取训练计划ID');
        wx.showToast({
          title: '无法获取训练计划',
          icon: 'none'
        });
        return;
      }

      // 跳转到训练会话页面
      wx.navigateTo({
        url: `/pages/training/training-session/index?planId=${planId}&workoutId=${workoutId}`
      });
    } catch (error) {
      console.error('继续训练失败:', error);
      wx.showToast({
        title: '操作失败',
        icon: 'none'
      });
    }
  },

  /**
   * 暂停训练
   */
  async pauseWorkout(workoutId) {
    try {
      const api = require('../../api/index');
      await api.trainingPlan.updateWorkoutStatus(workoutId, { status: 'paused' });

      // 刷新数据
      this.fetchTodayWorkout();
    } catch (error) {
      console.error('暂停训练失败:', error);
      wx.showToast({
        title: '操作失败',
        icon: 'none'
      });
    }
  },

  /**
   * 完成训练
   */
  async completeWorkout(workoutId) {
    try {
      const api = require('../../api/index');
      await api.trainingPlan.updateWorkoutStatus(workoutId, { status: 'completed' });

      // 刷新数据
      this.fetchTodayWorkout();
    } catch (error) {
      console.error('完成训练失败:', error);
      wx.showToast({
        title: '操作失败',
        icon: 'none'
      });
    }
  },

  /**
   * 查看训练详情
   */
  viewWorkoutDetail(workoutId) {
    wx.navigateTo({
      url: `/pages/plan-detail/index?id=${workoutId}`
    });
  },

  /**
   * 点击训练计划面板跳转到训练会话页面
   */
  async navigateToTrainingSession() {
    try {
      const { todayWorkout } = this.data;

      if (!todayWorkout || !todayWorkout.id) {
        console.error('没有可用的训练数据');
        return;
      }

      const workoutId = todayWorkout.id;
      const api = require('../../api/index');

      // 获取计划ID
      const selectedDate = this.data.selectedDate;
      console.log('使用日期获取训练数据:', selectedDate);
      const workoutData = await api.trainingPlan.getWorkoutsByDate(selectedDate);
      let planId = null;

      // 查找当前workout对应的planId
      if (workoutData && workoutData.length > 0) {
        for (let workout of workoutData) {
          if (workout.id == workoutId) {
            planId = workout.training_plan_id || workout.plan_id;
            break;
          }
        }
      }

      if (!planId) {
        console.error('无法获取训练计划ID');
        wx.showToast({
          title: '无法获取训练计划',
          icon: 'none'
        });
        return;
      }

      // 根据训练状态决定是否需要更新状态
      if (todayWorkout.status === 'not_started') {
        // 如果是未开始状态，更新为进行中
        await api.trainingPlan.updateWorkoutStatus(workoutId, { status: 'in_progress' });
        // 刷新数据
        this.fetchTodayWorkout();
      }

      // 跳转到训练会话页面
      wx.navigateTo({
        url: `/pages/training/training-session/index?planId=${planId}&workoutId=${workoutId}`
      });
    } catch (error) {
      console.error('跳转到训练会话页面失败:', error);
      wx.showToast({
        title: '操作失败',
        icon: 'none'
      });
    }
  },

  /**
   * 创建训练计划
   */
  createTrainingPlan() {
    // 显示弹窗
    this.setData({
      visible: true  // 使用已有的弹窗组件
    });
  },

  /**
   * 获取营养数据
   */
  async fetchNutritionData() {
    const selectedDate = this.data.selectedDate;
    if (!selectedDate) return;

    this.setData({ isLoading: true });

    try {
      // 引入API模块
      const api = require('../../api/index');

      // 使用API获取每日营养摄入汇总
      const result = await api.meal.getDailyNutrition(selectedDate);

      if (result) {
        console.log('获取到每日营养数据:', result);

        // 更新营养数据
        this.setData({
          caloriesConsumed: parseFloat(result.total_calory.toFixed(0)),
          carb: parseFloat(result.total_carbohydrate.toFixed(1)),
          protein: parseFloat(result.total_protein.toFixed(1)),
          fat: parseFloat(result.total_fat.toFixed(1)),
        });
      } else {
        throw new Error(`服务器返回错误: ${response.statusCode}`);
      }
    } catch (error) {
      console.error('获取营养数据失败:', error);
      this.setData({
        error: '获取营养数据失败，请稍后重试',
        isLoading: false
      });
      
      // 显示错误提示
      wx.showToast({
        title: '获取营养数据失败',
        icon: 'none',
        duration: 2000
      });
    }
  },

  /**
   * 重置营养数据
   */
  resetNutritionData() {
    this.setData({
      caloriesConsumed: 0,
      carb: 0,
      protein: 0,
      fat: 0
    });
  },

  /**
   * 更新营养摄入百分比
   */
  updatePercentages() {
    const {
      caloriesConsumed, carb, protein, fat,
      caloriesGoal, carbGoal, proteinGoal, fatGoal
    } = this.data;

    this.setData({
      caloriesPercentage: this.calculatePercentage(caloriesConsumed, caloriesGoal),
      carbPercentage: this.calculatePercentage(carb, carbGoal),
      fatPercentage: this.calculatePercentage(fat, fatGoal),
      proteinPercentage: this.calculatePercentage(protein, proteinGoal)
    });
  },

  /**
   * 计算百分比
   * @param {number} value - 当前值
   * @param {number} goal - 目标值
   * @return {number} 百分比
   */
  calculatePercentage(value, goal) {
    return goal > 0 ? parseFloat(((value / goal) * 100).toFixed(1)) : 0;
  },

  /**
   * 设置选中日期
   * @param {Date} date - 日期对象
   */
  setSelectedDate(date) {
    const formattedDate = `${date.getMonth() + 1}月${date.getDate()}日`;
    const selected = {
      day: date.getDate(),
      month: date.getMonth() + 1,
      year: date.getFullYear()
    };

    const today = new Date();
    const isToday = selected.day === today.getDate() &&
                    selected.month === today.getMonth() + 1 &&
                    selected.year === today.getFullYear();

    const updatedDates = this.data.dates.map(d => ({
      ...d,
      isSelected: d.day === selected.day &&
                  d.month === selected.month &&
                  d.year === selected.year
    }));

    // 格式化日期为 YYYY-MM-DD 格式，确保月份和日期是两位数
    const selectedDate = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
    const app = getApp();
    app.globalData.selectedDate = selectedDate;

    this.setData({
      currentDate: formattedDate,
      selectedDate,
      isTodaySelected: isToday,
      dates: updatedDates
    });

    // 获取选中日期的训练数据
    this.fetchTodayWorkout(selectedDate);

    // 获取其他数据
    this.fetchTrainingSchedule();
    this.fetchNutritionData();
  },

  /**
   * 加载月历数据
   */
  loadMonthCalendar() {
    const today = new Date();
    const days = [];

    // 生成前后200天的日期数据
    for (let i = -200; i <= 200; i++) {
      const newDate = new Date(today);
      newDate.setDate(today.getDate() + i);

      const dateKey = `date${newDate.getFullYear()}-${newDate.getMonth() + 1}-${newDate.getDate()}`;

      days.push({
        id: dateKey,
        weekDay: this.getWeekDay(newDate.getDay()),
        day: newDate.getDate(),
        month: newDate.getMonth() + 1,
        year: newDate.getFullYear(),
        isSelected: i === 0
      });
    }
    this.setData({ dates: days });
  },

  /**
   * 获取星期几的中文表示
   * @param {number} index - 星期索引（0-6）
   * @return {string} 中文星期名
   */
  getWeekDay(index) {
    const weekDays = ['日', '一', '二', '三', '四', '五', '六'];
    return weekDays[index];
  },

  /**
   * 跳转到今天
   */
  goToToday() {
    const today = new Date();

    // 调用 setSelectedDate 函数，它会获取今天的 workout 数据
    this.setSelectedDate(today);

    const formattedDate = `${today.getMonth() + 1}月${today.getDate()}日`;
    const todayId = `date${today.getFullYear()}-${today.getMonth() + 1}-${today.getDate()}`;

    this.setData({
      currentDate: formattedDate,
      scrollToView: todayId,
    });

    console.log('跳转到今天:', `${today.getFullYear()}-${String(today.getMonth() + 1).padStart(2, '0')}-${String(today.getDate()).padStart(2, '0')}`);
  },

  /**
   * 选择日期
   * @param {WechatMiniprogram.TouchEvent} e - 事件对象
   */
  selectDate(e) {
    const selected = e.currentTarget.dataset.date;
    const selectedDate = new Date(selected.year, selected.month - 1, selected.day);

    // 调用 setSelectedDate 函数，它会获取选中日期的 workout 数据
    this.setSelectedDate(selectedDate);

    console.log('选择日期:', `${selected.year}-${String(selected.month).padStart(2, '0')}-${String(selected.day).padStart(2, '0')}`);
  },

  /**
   * 处理弹出层可见性变化
   * @param {WechatMiniprogram.CustomEvent} e - 事件对象
   */
  onVisibleChange(e) {
    this.setData({
      visible: e.detail.visible,
    });
  },

  /**
   * 打开完整日历
   */
  toggleFullCalendar() {
    wx.navigateTo({
      url: '/pages/calendar/index',
      fail: (err) => {
        console.error('跳转日历页面失败:', err);
        wx.showToast({
          title: '页面跳转失败',
          icon: 'none'
        });
      }
    });
  },

  /**
   * 打开训练计划页面
   */
  openPlan() {
    wx.navigateTo({
      url: '/pages/plan/index',
      fail: (err) => {
        console.error('跳转计划页面失败:', err);
        wx.showToast({
          title: '页面跳转失败',
          icon: 'none'
        });
      }
    });
  },

  /**
   * 打开训练选项
   */
  openTrainingOptions() {
    this.setData({ visible: true });
  },

  /**
   * 关闭训练选项
   */
  onClose() {
    this.setData({ visible: false });
  },

  /**
   * 添加饮食记录
   * @param {WechatMiniprogram.TouchEvent} e - 事件对象
   */
  addMeal(e) {
    const mealType = e.currentTarget.dataset.type;
    this.setData({
      mealPopupVisible: true,
      mealType
    });
  },

  /**
   * 处理饮食记录弹出层可见性变化
   * @param {WechatMiniprogram.CustomEvent} e - 事件对象
   */
  onMealPopupVisibleChange(e) {
    this.setData({ mealPopupVisible: e.detail.visible });
  },

  /**
   * 处理上传照片
   */
  async handleUploadPhoto() {
    const { mealType, userInfo, selectedDate } = this.data;
    const openId = userInfo.openId;

    try {
      wx.showLoading({
        title: '处理中...',
        mask: true
      });

      // 选择图片
      const res = await wx.chooseImage({
        count: 1,
        sizeType: ['compressed'],
        sourceType: ['album', 'camera']
      });

      const tempFilePath = res.tempFilePaths[0];

      // 压缩图片以减小尺寸，用于预览和分析
      const compressRes = await wx.compressImage({
        src: tempFilePath,
        quality: 60,  // 调整为60%的质量，平衡大小和清晰度
        compressedWidth: 800  // 增加宽度以保证识别质量
      });

      // 保存到全局变量
      const app = getApp();
      if (!app.globalData.tempFoodImage) {
        app.globalData.tempFoodImage = {};
      }

      // 使用时间戳作为临时ID
      const tempId = Date.now().toString();

      // 只保存本地文件路径，不再转换为base64
      app.globalData.tempFoodImage[tempId] = {
        originalPath: tempFilePath,         // 原始图片路径
        compressedPath: compressRes.tempFilePath  // 压缩后的图片路径
      };

      console.log('图片已保存到全局变量，准备跳转到预览页面');
      wx.hideLoading();

      // 跳转到预览页面，只传递必要参数
      wx.navigateTo({
        url: `/pages/foodPreview/preview?tempId=${tempId}&mealType=${encodeURIComponent(mealType)}&openId=${encodeURIComponent(openId)}&selectedDate=${encodeURIComponent(selectedDate)}&autoAnalyze=true`,
        fail: (err) => {
          console.error('跳转预览页面失败:', err);
          wx.showToast({
            title: '页面跳转失败',
            icon: 'none'
          });

          // 清理全局变量
          delete app.globalData.tempFoodImage[tempId];
        }
      });

    } catch (error) {
      console.error('处理图片失败:', error);
      wx.hideLoading();
      wx.showToast({
        title: '处理图片失败',
        icon: 'none'
      });
    }
  },

  /**
   * 跳转到饮食记录页面
   */
  navigateToFoodRecord() {
    const mealData = {
      caloriesConsumed: this.data.caloriesConsumed,
      caloriesGoal: this.data.caloriesGoal,
      carb: this.data.carb,
      carbGoal: this.data.carbGoal,
      protein: this.data.protein,
      proteinGoal: this.data.proteinGoal,
      fat: this.data.fat,
      fatGoal: this.data.fatGoal,
      caloriesPercentage: this.data.caloriesPercentage,
      carbPercentage: this.data.carbPercentage,
      proteinPercentage: this.data.proteinPercentage,
      fatPercentage: this.data.fatPercentage
    };

    const mealDataString = encodeURIComponent(JSON.stringify(mealData));
    wx.navigateTo({
      url: `/pages/foodRecord/record?selectedDate=${encodeURIComponent(this.data.selectedDate)}&openId=${encodeURIComponent(this.data.userInfo.openId)}&mealData=${mealDataString}`,
      fail: (err) => {
        console.error('跳转饮食记录页面失败:', err);
        wx.showToast({
          title: '页面跳转失败',
          icon: 'none'
        });
      }
    });
  },

  /**
   * 打开食物数据库页面
   * @param {Object} e - 事件对象
   */
  openFoodDatabase: function(e) {
    const mealType = e.currentTarget?.dataset?.mealType || '';
    
    // 关闭当前弹窗
    this.setData({ mealPopupVisible: false });
    
    // 使用switchTab而不是navigateTo
    wx.switchTab({
      url: '/pages/food-log/food',  // 注意：switchTab不支持传递参数
      fail: err => {
        console.error('跳转食物数据库失败:', err);
        wx.showToast({
          title: '页面跳转失败',
          icon: 'none'
        });
      },
      success: () => {
        // 由于switchTab不能传递参数，可以考虑使用全局变量传递mealType
        const app = getApp();
        if (app.globalData) {
          app.globalData.tempMealType = mealType;
        }
      }
    });
  },

  /**
   * 从全局变量获取TEDD值作为卡路里目标
   */
  setCaloriesGoalFromGlobalTEDD: function() {
    const app = getApp();
    if (app && app.globalData && app.globalData.userInfo && app.globalData.userInfo.tedd) {
      const tedd = app.globalData.userInfo.tedd;
      console.log('从全局变量获取TEDD值:', tedd);

      // 设置卡路里目标为TEDD值
      this.setData({
        caloriesGoal: tedd
      });

      // 更新营养摄入百分比
      this.updatePercentages();
    } else {
      console.log('全局变量中没有TEDD值，使用默认值');
    }
  },

  /**
   * 点击自由训练，跳转到训练会话页面
   */
  customizePlan() {
    // 关闭当前弹窗
    this.setData({ visible: false });
    
    // 跳转到训练会话页面，标记为自由训练模式
    wx.navigateTo({
      url: '/pages/training/training-session/index?fromCustomize=true',
      fail: (err) => {
        console.error('跳转训练会话页面失败:', err);
        wx.showToast({
          title: '页面跳转失败',
          icon: 'none'
        });
      }
    });
  },

  /**
   * 点击训练模板，跳转到训练模板页面
   */
  trainingPlan() {
    // 关闭当前弹窗
    this.setData({ visible: false });
    
    // 跳转到训练模板页面
    wx.navigateTo({
      url: '/pages/training-templates/index',
      fail: (err) => {
        console.error('跳转训练模板页面失败:', err);
        wx.showToast({
          title: '页面跳转失败',
          icon: 'none'
        });
      }
    });
  },

  // 日期选择器变化事件
  onDateChange(e) {
    this.setData({
      selectedDate: e.detail.value,
      isLoading: true
    });
    this.fetchNutritionData();
  },

  // 下拉刷新
  async onPullDownRefresh() {
    try {
      await this.fetchNutritionData();
      wx.stopPullDownRefresh();
    } catch (error) {
      console.error('刷新数据失败:', error);
      wx.stopPullDownRefresh();
    }
  },

  /**
   * AI制定计划
   */
  AItrainingPlan() {
    // 关闭当前弹窗
    this.setData({ visible: false });
    
    // 跳转到AI助手页面
    wx.switchTab({
      url: '/pages/chatbot/index',
      fail: (err) => {
        console.error('跳转AI助手页面失败:', err);
        wx.showToast({
          title: '页面跳转失败',
          icon: 'none'
        });
      }
    });
  }
});