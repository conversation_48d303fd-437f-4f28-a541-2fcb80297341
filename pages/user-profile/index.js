const app = getApp();
const api = require('../../api/index');

Page({
  /**
   * 页面的初始数据
   */
  data: {
    // 用户ID
    userId: null,
    // 是否为当前用户
    isCurrentUser: false,
    // 用户信息
    userInfo: null,
    // 用户统计数据
    userStats: null,
    // 用户帖子
    posts: [],
    // 当前标签页 - posts: 帖子, workouts: 训练记录
    currentTab: 'posts',
    // 加载状态
    loading: true,
    // 帖子加载状态
    postsLoading: false,
    // 训练加载状态
    workoutsLoading: false,
    // 是否已加载全部帖子
    postsLoadedAll: false,
    // 是否已加载全部训练
    workoutsLoadedAll: false,
    // 每页加载数量
    pageSize: 10,
    // 帖子跳过的数量
    postsSkip: 0,
    // 训练跳过的数量
    workoutsSkip: 0,
    // 用户训练记录
    workouts: [],
    // 错误信息
    error: ''
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    const { userId } = options;
    const currentUserId = app.globalData.userInfo ? app.globalData.userInfo.id : null;
    
    // 如果没有传入用户ID，且当前用户已登录，则查看当前用户资料
    let targetUserId = userId || currentUserId;
    
    if (!targetUserId) {
      this.setData({
        loading: false,
        error: '用户ID不能为空'
      });
      return;
    }
    
    this.setData({
      userId: targetUserId,
      isCurrentUser: targetUserId === currentUserId
    });
    
    // 更新导航栏标题
    if (this.data.isCurrentUser) {
      wx.setNavigationBarTitle({ title: '我的主页' });
    }
    
    // 加载用户资料
    this.loadUserProfile();
    
    // 加载用户统计数据
    this.loadUserStats();
    
    // 加载用户帖子
    this.loadUserPosts();
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () {
    // 重置所有数据
    this.setData({
      userInfo: null,
      userStats: null,
      posts: [],
      workouts: [],
      postsSkip: 0,
      workoutsSkip: 0,
      postsLoadedAll: false,
      workoutsLoadedAll: false
    });
    
    // 重新加载
    Promise.all([
      this.loadUserProfile(),
      this.loadUserStats(),
      this.data.currentTab === 'posts' ? this.loadUserPosts() : this.loadUserWorkouts()
    ]).then(() => {
      wx.stopPullDownRefresh();
    });
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () {
    if (this.data.currentTab === 'posts') {
      if (!this.data.postsLoadedAll && !this.data.postsLoading) {
        this.loadMorePosts();
      }
    } else {
      if (!this.data.workoutsLoadedAll && !this.data.workoutsLoading) {
        this.loadMoreWorkouts();
      }
    }
  },

  /**
   * 加载用户资料
   */
  loadUserProfile: function () {
    this.setData({ loading: true });
    
    return api.community.getUserProfile(this.data.userId)
      .then(res => {
        // 获取用户数据
        const userInfo = res.data;
        
        this.setData({ 
          userInfo,
          loading: false
        });
        
        // 如果是其他用户的资料页，更新导航栏标题
        if (!this.data.isCurrentUser && userInfo && userInfo.username) {
          wx.setNavigationBarTitle({ title: userInfo.username });
        }
      })
      .catch(err => {
        console.error('加载用户资料失败:', err);
        this.setData({
          loading: false,
          error: '加载失败，请重试'
        });
        wx.showToast({
          title: '加载失败，请重试',
          icon: 'none'
        });
      });
  },

  /**
   * 加载用户统计数据
   */
  loadUserStats: function () {
    return api.community.getUserStats(this.data.userId)
      .then(res => {
        this.setData({ userStats: res.data });
      })
      .catch(err => {
        console.error('加载用户统计数据失败:', err);
      });
  },

  /**
   * 加载用户帖子
   */
  loadUserPosts: function () {
    if (this.data.postsLoading) return Promise.resolve();
    
    this.setData({ postsLoading: true });
    
    return api.community.getPosts({
      user_id: this.data.userId,
      skip: this.data.postsSkip,
      limit: this.data.pageSize
    })
    .then(res => {
      const newPosts = res.data || [];
      
      this.setData({
        posts: this.data.posts.concat(newPosts),
        postsSkip: this.data.postsSkip + newPosts.length,
        postsLoadedAll: newPosts.length < this.data.pageSize,
        postsLoading: false
      });
    })
    .catch(err => {
      console.error('加载用户帖子失败:', err);
      this.setData({ postsLoading: false });
    });
  },

  /**
   * 加载更多帖子
   */
  loadMorePosts: function () {
    this.loadUserPosts();
  },

  /**
   * 加载用户训练记录
   */
  loadUserWorkouts: function () {
    if (this.data.workoutsLoading) return Promise.resolve();
    
    this.setData({ workoutsLoading: true });
    
    return api.community.getUserWorkouts(this.data.userId, {
      skip: this.data.workoutsSkip,
      limit: this.data.pageSize
    })
    .then(res => {
      const newWorkouts = res.data || [];
      
      this.setData({
        workouts: this.data.workouts.concat(newWorkouts),
        workoutsSkip: this.data.workoutsSkip + newWorkouts.length,
        workoutsLoadedAll: newWorkouts.length < this.data.pageSize,
        workoutsLoading: false
      });
    })
    .catch(err => {
      console.error('加载用户训练记录失败:', err);
      this.setData({ workoutsLoading: false });
    });
  },

  /**
   * 加载更多训练记录
   */
  loadMoreWorkouts: function () {
    this.loadUserWorkouts();
  },

  /**
   * 切换标签页
   */
  switchTab: function (e) {
    const tab = e.currentTarget.dataset.tab;
    
    if (tab === this.data.currentTab) return;
    
    this.setData({ currentTab: tab });
    
    // 如果切换到训练记录标签，并且尚未加载过训练记录，则加载
    if (tab === 'workouts' && this.data.workouts.length === 0) {
      this.loadUserWorkouts();
    }
  },

  /**
   * 关注/取消关注用户
   */
  onFollowUser: function () {
    if (this.data.isCurrentUser) return;
    
    const userInfo = this.data.userInfo;
    if (!userInfo) return;
    
    const isFollowing = !userInfo.is_following;
    
    // 调用关注或取消关注API
    const apiCall = isFollowing ? 
      api.community.followUser(this.data.userId) : 
      api.community.unfollowUser(this.data.userId);
    
    // 更新本地状态
    this.setData({
      'userInfo.is_following': isFollowing
    });
    
    apiCall
      .then(() => {
        wx.showToast({
          title: isFollowing ? '已关注' : '已取消关注',
          icon: 'success'
        });
        
        // 更新粉丝数
        if (this.data.userStats) {
          const userStats = { ...this.data.userStats };
          userStats.followers_count = isFollowing ? 
            (userStats.followers_count || 0) + 1 : 
            Math.max(0, (userStats.followers_count || 0) - 1);
          
          this.setData({ userStats });
        }
      })
      .catch(err => {
        console.error('关注操作失败:', err);
        wx.showToast({
          title: '操作失败，请重试',
          icon: 'none'
        });
        
        // 回滚本地状态
        this.setData({
          'userInfo.is_following': !isFollowing
        });
      });
  },

  /**
   * 点击查看关注列表
   */
  onTapFollowing: function () {
    wx.navigateTo({
      url: `/pages/user-follow/index?userId=${this.data.userId}&type=following`
    });
  },

  /**
   * 点击查看粉丝列表
   */
  onTapFollowers: function () {
    wx.navigateTo({
      url: `/pages/user-follow/index?userId=${this.data.userId}&type=followers`
    });
  },

  /**
   * 编辑个人资料
   */
  onEditProfile: function () {
    if (!this.data.isCurrentUser) return;
    
    wx.navigateTo({
      url: '/pages/user-profile-edit/index'
    });
  },

  /**
   * 点击帖子卡片
   */
  onTapPost: function (e) {
    const { postId } = e.detail;
    wx.navigateTo({
      url: `/pages/post-detail/index?postId=${postId}`
    });
  },

  /**
   * 点击训练记录
   */
  onTapWorkout: function (e) {
    const workoutId = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `/pages/workout-detail/index?id=${workoutId}`
    });
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function () {
    const userInfo = this.data.userInfo || {};
    return {
      title: `${userInfo.username || '用户'}的ScienceFit健身资料`,
      path: `/pages/user-profile/index?userId=${this.data.userId}`
    };
  }
}); 