/* 用户资料页样式 */
.user-profile-container {
  background-color: #f8f8f8;
  min-height: 100vh;
}

/* 加载状态 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 400rpx;
}

.loading-icon {
  color: #999;
  font-size: 28rpx;
}

/* 错误状态 */
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
}

.error-message {
  color: #f56c6c;
  font-size: 28rpx;
  margin-bottom: 20rpx;
}

.retry-btn {
  padding: 12rpx 32rpx;
  background-color: #07C160;
  color: #fff;
  border-radius: 8rpx;
  font-size: 28rpx;
}

/* 用户头部区域 */
.user-header-section {
  background-color: #fff;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.user-header-background {
  height: 300rpx;
  background-size: cover;
  background-position: center;
  position: relative;
}

.user-avatar-container {
  position: absolute;
  left: 40rpx;
  bottom: -80rpx;
  padding: 6rpx;
  background-color: #fff;
  border-radius: 50%;
}

.user-avatar {
  width: 160rpx;
  height: 160rpx;
  border-radius: 50%;
  background-color: #f0f0f0;
}

.user-info-container {
  padding: 100rpx 40rpx 40rpx;
}

.user-name-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.user-name {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
}

.user-actions {
  display: flex;
}

.edit-profile-btn, .follow-btn {
  padding: 10rpx 24rpx;
  border-radius: 30rpx;
  font-size: 26rpx;
}

.edit-profile-btn {
  border: 1rpx solid #ddd;
  color: #333;
}

.follow-btn {
  background-color: #07C160;
  color: #fff;
}

.follow-btn.following {
  background-color: #f0f0f0;
  color: #333;
}

.user-bio {
  font-size: 28rpx;
  color: #666;
  line-height: 1.5;
  margin-bottom: 24rpx;
}

/* 用户统计信息 */
.user-stats {
  display: flex;
  border-top: 1rpx solid #f0f0f0;
  padding-top: 24rpx;
}

.stat-item {
  flex: 1;
  text-align: center;
}

.stat-value {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.stat-label {
  font-size: 24rpx;
  color: #999;
  margin-top: 4rpx;
}

/* 健身数据卡片 */
.fitness-data-section {
  background-color: #fff;
  padding: 24rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 20rpx;
}

.fitness-data-cards {
  display: flex;
  justify-content: space-between;
}

.fitness-card {
  flex: 1;
  text-align: center;
  padding: 16rpx;
  background-color: #f8f8f8;
  border-radius: 8rpx;
  margin: 0 8rpx;
}

.fitness-card:first-child {
  margin-left: 0;
}

.fitness-card:last-child {
  margin-right: 0;
}

.fitness-card-value {
  font-size: 36rpx;
  font-weight: 600;
  color: #07C160;
}

.fitness-card-value .unit {
  font-size: 24rpx;
  font-weight: normal;
  margin-left: 4rpx;
}

.fitness-card-label {
  font-size: 24rpx;
  color: #666;
  margin-top: 8rpx;
}

/* 标签页 */
.tabs-section {
  background-color: #fff;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.tab-header {
  display: flex;
  border-bottom: 1rpx solid #f0f0f0;
}

.tab-item {
  flex: 1;
  text-align: center;
  padding: 24rpx 0;
  font-size: 28rpx;
  color: #666;
  position: relative;
}

.tab-item.active {
  color: #07C160;
  font-weight: 500;
}

.tab-item.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 60rpx;
  height: 4rpx;
  background-color: #07C160;
  border-radius: 2rpx;
}

.tab-content {
  padding: 24rpx;
  min-height: 400rpx;
}

/* 帖子列表 */
.post-list {
  margin-bottom: 20rpx;
}

/* 训练记录列表 */
.workout-list {
  margin-bottom: 20rpx;
}

.workout-card {
  margin-bottom: 24rpx;
  padding: 24rpx;
  background-color: #f8f9fa;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.workout-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.workout-type {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
}

.workout-time {
  font-size: 24rpx;
  color: #999;
}

.workout-stats {
  display: flex;
  justify-content: flex-start;
  margin-bottom: 16rpx;
  padding-bottom: 16rpx;
  border-bottom: 1rpx solid #eee;
}

.workout-stat-item {
  margin-right: 40rpx;
}

.workout-stat-value {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
}

.workout-stat-label {
  font-size: 24rpx;
  color: #999;
}

/* 训练内容预览 */
.exercises-preview {
  margin-top: 16rpx;
}

.exercise-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12rpx;
}

.exercise-name {
  font-size: 26rpx;
  color: #333;
}

.exercise-details {
  font-size: 24rpx;
  color: #666;
}

.exercise-details text {
  margin-left: 16rpx;
}

.more-exercises {
  font-size: 24rpx;
  color: #07C160;
  text-align: center;
  margin-top: 12rpx;
}

/* 加载更多状态 */
.loading-more {
  text-align: center;
  padding: 20rpx 0;
}

.loading-text {
  color: #999;
  font-size: 26rpx;
}

/* 加载完毕提示 */
.end-tip {
  text-align: center;
  padding: 30rpx 0;
  color: #999;
  font-size: 24rpx;
}

/* 空状态 */
.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80rpx 0;
}

.empty-icon {
  font-size: 80rpx;
  color: #ddd;
  margin-bottom: 20rpx;
}

.empty-text {
  color: #999;
  font-size: 28rpx;
} 