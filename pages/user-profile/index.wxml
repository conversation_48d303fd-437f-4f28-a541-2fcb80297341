<view class="user-profile-container">
  <!-- 加载中 -->
  <view class="loading-container" wx:if="{{loading}}">
    <view class="loading-icon">加载中...</view>
  </view>

  <!-- 加载错误 -->
  <view class="error-container" wx:elif="{{error}}">
    <view class="error-message">{{error}}</view>
    <view class="retry-btn" bindtap="loadUserProfile">重试</view>
  </view>

  <!-- 用户资料内容 -->
  <block wx:elif="{{userInfo}}">
    <!-- 用户基本信息 -->
    <view class="user-header-section">
      <view class="user-header-background" style="background-image: url({{userInfo.cover_image ? userInfo.cover_image : '/images/default-cover.jpg'}})">
        <view class="user-avatar-container">
          <image class="user-avatar" src="{{userInfo.avatar ? userInfo.avatar : '/images/default-avatar.png'}}" mode="aspectFill"></image>
        </view>
      </view>
      
      <view class="user-info-container">
        <view class="user-name-row">
          <view class="user-name">{{userInfo.username || '未设置昵称'}}</view>
          <view class="user-actions">
            <block wx:if="{{isCurrentUser}}">
              <view class="edit-profile-btn" bindtap="onEditProfile">编辑资料</view>
            </block>
            <block wx:else>
              <view class="follow-btn {{userInfo.is_following ? 'following' : ''}}" bindtap="onFollowUser">
                {{userInfo.is_following ? '已关注' : '关注'}}
              </view>
            </block>
          </view>
        </view>
        
        <view class="user-bio" wx:if="{{userInfo.bio}}">{{userInfo.bio}}</view>
        
        <!-- 用户统计信息 -->
        <view class="user-stats">
          <view class="stat-item" bindtap="onTapFollowing">
            <view class="stat-value">{{userStats.following_count || 0}}</view>
            <view class="stat-label">关注</view>
          </view>
          <view class="stat-item" bindtap="onTapFollowers">
            <view class="stat-value">{{userStats.followers_count || 0}}</view>
            <view class="stat-label">粉丝</view>
          </view>
          <view class="stat-item">
            <view class="stat-value">{{userStats.post_count || 0}}</view>
            <view class="stat-label">帖子</view>
          </view>
          <view class="stat-item">
            <view class="stat-value">{{userStats.workout_count || 0}}</view>
            <view class="stat-label">训练</view>
          </view>
        </view>
      </view>
    </view>

    <!-- 健身数据卡片 -->
    <view class="fitness-data-section" wx:if="{{userStats.fitness_data}}">
      <view class="section-title">健身数据</view>
      <view class="fitness-data-cards">
        <view class="fitness-card">
          <view class="fitness-card-value">{{userStats.fitness_data.total_workouts || 0}}</view>
          <view class="fitness-card-label">总训练次数</view>
        </view>
        <view class="fitness-card">
          <view class="fitness-card-value">{{userStats.fitness_data.total_volume || 0}}<text class="unit">kg</text></view>
          <view class="fitness-card-label">总重量</view>
        </view>
        <view class="fitness-card">
          <view class="fitness-card-value">{{userStats.fitness_data.total_duration || 0}}<text class="unit">min</text></view>
          <view class="fitness-card-label">总时长</view>
        </view>
      </view>
    </view>

    <!-- 内容标签页 -->
    <view class="tabs-section">
      <view class="tab-header">
        <view class="tab-item {{currentTab === 'posts' ? 'active' : ''}}" bindtap="switchTab" data-tab="posts">帖子</view>
        <view class="tab-item {{currentTab === 'workouts' ? 'active' : ''}}" bindtap="switchTab" data-tab="workouts">训练记录</view>
      </view>
      
      <!-- 帖子内容 -->
      <view class="tab-content" wx:if="{{currentTab === 'posts'}}">
        <block wx:if="{{posts.length > 0}}">
          <view class="post-list">
            <block wx:for="{{posts}}" wx:key="id">
              <post-card 
                post="{{item}}" 
                bindtappost="onTapPost"
              />
            </block>
          </view>
          
          <!-- 加载状态 -->
          <view class="loading-more" wx:if="{{postsLoading}}">
            <view class="loading-text">加载中...</view>
          </view>
          
          <!-- 加载完毕提示 -->
          <view class="end-tip" wx:if="{{postsLoadedAll && posts.length > 0}}">
            <text>- 已经到底了 -</text>
          </view>
        </block>
        
        <!-- 无数据状态 -->
        <view class="empty-container" wx:elif="{{!postsLoading}}">
          <view class="empty-icon">📝</view>
          <view class="empty-text">暂无帖子</view>
        </view>
      </view>
      
      <!-- 训练记录内容 -->
      <view class="tab-content" wx:if="{{currentTab === 'workouts'}}">
        <block wx:if="{{workouts.length > 0}}">
          <view class="workout-list">
            <block wx:for="{{workouts}}" wx:key="id">
              <view class="workout-card" bindtap="onTapWorkout" data-id="{{item.id}}">
                <view class="workout-header">
                  <view class="workout-type">{{item.type || '训练'}}</view>
                  <view class="workout-time">{{item.created_at_formatted || ''}}</view>
                </view>
                
                <view class="workout-stats">
                  <view class="workout-stat-item">
                    <view class="workout-stat-value">{{item.duration || 0}}分钟</view>
                    <view class="workout-stat-label">时长</view>
                  </view>
                  <view class="workout-stat-item">
                    <view class="workout-stat-value">{{item.volume || 0}}kg</view>
                    <view class="workout-stat-label">总重量</view>
                  </view>
                  <view class="workout-stat-item" wx:if="{{item.rating}}">
                    <view class="workout-stat-value">{{item.rating}}⭐</view>
                    <view class="workout-stat-label">评分</view>
                  </view>
                </view>
                
                <!-- 训练内容预览 -->
                <view class="exercises-preview" wx:if="{{item.exercises && item.exercises.length > 0}}">
                  <block wx:for="{{item.exercises}}" wx:for-item="exercise" wx:for-index="exerciseIndex" wx:key="exerciseIndex" wx:if="{{exerciseIndex < 3}}">
                    <view class="exercise-item">
                      <view class="exercise-name">{{exercise.name}}</view>
                      <view class="exercise-details">
                        <text wx:if="{{exercise.sets}}">{{exercise.sets}}组</text>
                        <text wx:if="{{exercise.weight}}">{{exercise.weight}}kg</text>
                      </view>
                    </view>
                  </block>
                  <view class="more-exercises" wx:if="{{item.exercises.length > 3}}">
                    更多 {{item.exercises.length - 3}} 个动作...
                  </view>
                </view>
              </view>
            </block>
          </view>
          
          <!-- 加载状态 -->
          <view class="loading-more" wx:if="{{workoutsLoading}}">
            <view class="loading-text">加载中...</view>
          </view>
          
          <!-- 加载完毕提示 -->
          <view class="end-tip" wx:if="{{workoutsLoadedAll && workouts.length > 0}}">
            <text>- 已经到底了 -</text>
          </view>
        </block>
        
        <!-- 无数据状态 -->
        <view class="empty-container" wx:elif="{{!workoutsLoading}}">
          <view class="empty-icon">🏋️‍♂️</view>
          <view class="empty-text">暂无训练记录</view>
        </view>
      </view>
    </view>
  </block>
</view> 