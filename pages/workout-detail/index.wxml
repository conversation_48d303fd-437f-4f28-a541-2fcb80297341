<!-- workout详情页面 -->
<view class="container">
  <!-- 分享按钮 -->
  <view class="share-button-container" bindtap="handleShare" wx:if="{{!isRecording && !isProcessing && workout}}">
    <image class="share-icon" src="/images/icons/share.png" mode="aspectFit"></image>
  </view>

  <!-- 录制状态覆盖层 -->
  <view class="recording-overlay" wx:if="{{isRecording || isProcessing}}">
    <view class="recording-status">
      <view class="status-icon {{isRecording ? 'recording' : ''}}"></view>
      <text class="status-text">{{statusText}}</text>
      <view class="progress-container" wx:if="{{progress > 0}}">
        <t-progress percentage="{{progress}}" stroke-width="12" theme="plump" />
        <text class="progress-text">{{progress}}%</text>
      </view>
    </view>
  </view>

  <!-- 加载状态 -->
  <view wx:if="{{loading}}" class="loading-container">
    <view class="loading"></view>
    <text class="loading-text">加载中...</text>
  </view>

  <!-- 错误状态 -->
  <view wx:elif="{{error}}" class="error-container">
    <icon type="warn" size="64" color="#ff4d4f"></icon>
    <text class="error-text">{{error}}</text>
    <button class="retry-button" bindtap="loadWorkoutDetail" data-id="{{id}}">重试</button>
  </view>

  <!-- 内容区域 -->
  <view wx:elif="{{workout}}" class="workout-container" id="workout-content">
    <!-- 头部信息 -->
    <view class="workout-header">
      <text class="workout-title">{{workout.name}}</text>
      <view class="workout-info">
        <view class="info-item">
          <text class="info-label">预计时长:</text>
          <text class="info-value">{{workout.estimated_duration}}分钟</text>
        </view>
        <view class="info-item">
          <text class="info-label">动作数量:</text>
          <text class="info-value">{{workout.exercises.length}}个</text>
        </view>
      </view>
      <text wx:if="{{workout.description}}" class="workout-description">{{workout.description}}</text>
    </view>

    <!-- 动作列表 -->
    <view class="exercises-container">
      <text class="section-title">训练动作</text>
      <view class="exercises-grid">
        <block wx:for="{{workout.exercises}}" wx:key="id" wx:for-item="exercise">
          <view class="exercise-card" bindtap="navigateToExerciseDetail" data-id="{{exercise.id}}">
            <!-- 视频播放器 -->
            <video
              wx:if="{{exercise.video_url}}"
              class="exercise-video"
              src="{{exercise.video_url}}"
              poster="{{exercise.image_url}}"
              object-fit="cover"
              show-center-play-btn="{{true}}"
              controls="{{false}}"
              autoplay="{{true}}"
              loop="{{true}}"
              muted="{{true}}"
              catchtap="navigateToExerciseDetail"
              data-id="{{exercise.id}}">
            </video>
            <image
              wx:else
              class="exercise-image"
              src="{{exercise.image_url || '/images/placeholder.png'}}"
              mode="aspectFill">
            </image>

            <!-- 动作信息 -->
            <view class="exercise-info">
              <text class="exercise-name">{{exercise.exercise_name}}</text>
              <view class="exercise-details">
                <text class="detail-item">{{exercise.sets || '-'}} 组</text>
                <text class="detail-item">{{exercise.reps || '-'}} 次</text>
              </view>
            </view>
          </view>
        </block>
      </view>
    </view>
  </view>

  <!-- 空状态 -->
  <view wx:else class="empty-container">
    <icon type="info" size="64" color="#1890ff"></icon>
    <text class="empty-text">没有找到训练数据</text>
  </view>
</view>

<!-- 分享选项对话框 -->
<t-dialog
  visible="{{showShareOptions}}"
  title="分享训练"
  confirm-btn="取消"
  cancel-btn=""
  bind:confirm="closeShareDialog"
>
  <view slot="content" class="share-options">
    <view class="share-option" bindtap="shareToFriend">
      <image class="option-icon" src="/images/icons/share-friend.png" mode="aspectFit"></image>
      <text class="option-text">分享给好友</text>
    </view>
    <view class="share-option" bindtap="saveToAlbum">
      <image class="option-icon" src="/images/icons/save-album.png" mode="aspectFit"></image>
      <text class="option-text">保存到相册</text>
    </view>
  </view>
</t-dialog>

<!-- 隐藏的Canvas用于录制 -->
<canvas
  type="2d"
  id="recording-canvas"
  class="recording-canvas"
  style="width: 750rpx; height: 1334rpx; position: absolute; left: -9999px;"
></canvas>
