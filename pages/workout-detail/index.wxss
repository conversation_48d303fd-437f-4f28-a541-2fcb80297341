/* workout详情页样式 */
.container {
  padding: 30rpx;
  min-height: 100vh;
  background-color: #f8f8f8;
  position: relative;
}

/* 分享按钮 */
.share-button-container {
  position: fixed;
  top: 20rpx;
  right: 30rpx;
  z-index: 100;
  width: 80rpx;
  height: 80rpx;
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}

.share-icon {
  width: 44rpx;
  height: 44rpx;
}

/* 录制状态覆盖层 */
.recording-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.7);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.recording-status {
  background-color: rgba(255, 255, 255, 0.95);
  border-radius: 16rpx;
  padding: 40rpx;
  width: 80%;
  max-width: 600rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.status-icon {
  width: 24rpx;
  height: 24rpx;
  border-radius: 50%;
  background-color: #07c160;
  margin-bottom: 20rpx;
}

.status-icon.recording {
  animation: pulse 1s infinite;
}

@keyframes pulse {
  0% { opacity: 1; }
  50% { opacity: 0.3; }
  100% { opacity: 1; }
}

.status-text {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 30rpx;
  text-align: center;
}

.progress-container {
  width: 100%;
  margin-top: 20rpx;
}

.progress-text {
  font-size: 24rpx;
  color: #666;
  margin-top: 10rpx;
  text-align: center;
}

/* 分享选项对话框 */
.share-options {
  display: flex;
  justify-content: space-around;
  padding: 30rpx 0;
}

.share-option {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20rpx;
}

.option-icon {
  width: 80rpx;
  height: 80rpx;
  margin-bottom: 16rpx;
}

.option-text {
  font-size: 28rpx;
  color: #333;
}

/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 80vh;
}

.loading {
  width: 80rpx;
  height: 80rpx;
  border: 6rpx solid #f3f3f3;
  border-top: 6rpx solid #07c160;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  margin-top: 30rpx;
  font-size: 28rpx;
  color: #666;
}

/* 错误状态 */
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 80vh;
}

.error-text {
  margin: 30rpx 0;
  font-size: 28rpx;
  color: #666;
  text-align: center;
}

.retry-button {
  margin-top: 20rpx;
  background-color: #07c160;
  color: #fff;
  font-size: 28rpx;
  padding: 16rpx 40rpx;
  border-radius: 40rpx;
}

/* 空状态 */
.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 80vh;
}

.empty-text {
  margin-top: 30rpx;
  font-size: 28rpx;
  color: #666;
}

/* workout内容 */
.workout-container {
  display: flex;
  flex-direction: column;
}

.workout-header {
  background-color: #fff;
  padding: 30rpx;
  border-radius: 16rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.workout-title {
  font-size: 40rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.workout-info {
  display: flex;
  margin-bottom: 20rpx;
}

.info-item {
  display: flex;
  margin-right: 30rpx;
}

.info-label {
  font-size: 26rpx;
  color: #666;
  margin-right: 10rpx;
}

.info-value {
  font-size: 26rpx;
  color: #333;
  font-weight: 500;
}

.workout-description {
  font-size: 28rpx;
  color: #666;
  line-height: 1.5;
}

/* 动作列表 */
.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.exercises-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20rpx;
}

.exercise-card {
  background-color: #fff;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.exercise-video,
.exercise-image {
  width: 100%;
  height: 320rpx;
  background-color: #ffffff; /* 修改为白色背景 */
}

.exercise-info {
  padding: 20rpx;
}

.exercise-name {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 10rpx;
  display: block;
}

.exercise-details {
  display: flex;
  font-size: 24rpx;
  color: #666;
}

.detail-item {
  margin-right: 20rpx;
}
