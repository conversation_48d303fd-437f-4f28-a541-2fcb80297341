/**
 * Workout详情页面
 * 显示训练详情和动作列表
 */
const app = getApp();
const imageHelper = require('../../utils/image-helper');
const recorder = require('../../utils/recorder');
const liveApi = require('../../api/live');
const userApi = require('../../api/user');

Page({
  data: {
    id: null,
    workout: null,
    loading: true,
    error: null,
    // 分享相关状态
    isRecording: false,
    isProcessing: false,
    showShareOptions: false,
    progress: 0,
    statusText: '',
    recordedVideoPath: '',
    processedVideoUrl: ''
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    if (options.id) {
      this.setData({
        id: options.id
      });
      this.loadWorkoutDetail(options.id);
    } else {
      this.setData({
        loading: false,
        error: '缺少workout ID'
      });
      wx.showToast({
        title: '缺少workout ID',
        icon: 'none'
      });
    }
  },

  /**
   * 加载workout详情
   * @param {string|number} workoutId - Workout ID
   */
  async loadWorkoutDetail(workoutId) {
    try {
      this.setData({ loading: true, error: null });

      // 导入workout API
      const workoutApi = require('../../api/workout');

      // 获取workout详情
      const workoutDetail = await workoutApi.getWorkoutDetail(workoutId);

      if (!workoutDetail) {
        throw new Error('获取workout详情失败');
      }

      console.log('获取到workout详情:', workoutDetail.name);

      // 处理workout本身的图片URL
      if (workoutDetail.image_url) {
        workoutDetail.image_url = imageHelper.getFullImageUrl(workoutDetail.image_url);
      }

      // 处理exercises的图片和视频URL
      if (workoutDetail.exercises && workoutDetail.exercises.length > 0) {
        console.log('处理exercises的图片和视频URL, exercises数量:', workoutDetail.exercises.length);

        workoutDetail.exercises.forEach((exercise, index) => {
          console.log(`处理exercise[${index}]:`, exercise);

          // 处理 exercise_image 字段 (根据提供的数据结构)
          if (exercise.exercise_image) {
            exercise.image_url = imageHelper.getFullImageUrl(exercise.exercise_image);
            console.log(`从exercise_image创建image_url: ${exercise.image_url}`);
          } else if (exercise.image_url) {
            exercise.image_url = imageHelper.getFullImageUrl(exercise.image_url);
            console.log(`处理image_url: ${exercise.image_url}`);
          }

          // 处理视频文件
          if (exercise.video_file) {
            exercise.video_url = imageHelper.getFullImageUrl(exercise.video_file);
            console.log(`处理video_file: ${exercise.video_url}`);
          } else if (exercise.video_url) {
            exercise.video_url = imageHelper.getFullImageUrl(exercise.video_url);
            console.log(`处理video_url: ${exercise.video_url}`);
          } else {
            // 确保 video_url 为空，这样模板会显示图片
            exercise.video_url = '';
            console.log(`exercise没有视频，将使用image_url显示`);
          }
        });
      } else {
        console.log('没有exercises数据或exercises不是数组');
        // 确保exercises是一个数组
        workoutDetail.exercises = workoutDetail.exercises || [];
      }

      // 更新页面数据
      this.setData({
        workout: workoutDetail,
        loading: false
      });

      // 设置页面标题
      if (workoutDetail.name) {
        wx.setNavigationBarTitle({
          title: workoutDetail.name
        });
      }
    } catch (error) {
      console.error('加载workout详情失败:', error);
      this.setData({
        loading: false,
        error: error.message || '加载失败'
      });
      wx.showToast({
        title: '加载失败',
        icon: 'none'
      });
    }
  },

  /**
   * 导航到动作详情页
   * @param {Object} e - 事件对象
   */
  async navigateToExerciseDetail(e) {
    const exerciseId = e.currentTarget.dataset.id;
    console.log('导航到动作详情页, dataset:', e.currentTarget.dataset);

    if (!exerciseId) {
      console.error('导航到动作详情页失败: 缺少exerciseId');
      return;
    }

    try {
      // 查找对应的exercise
      const workout = this.data.workout;
      if (workout && workout.exercises) {
        const exercise = workout.exercises.find(ex => ex.id === exerciseId);
        if (exercise) {
          // 使用exercise_id字段导航到详情页
          const detailId = exercise.exercise_id;
          if (detailId) {
            console.log(`找到exercise, 使用exercise_id(${detailId})导航到详情页`);

            // 方法1: 获取完整的exercise数据并传递给详情页
            try {
              // 导入exercise API
              const exerciseApi = require('../../api/exercise');

              // 显示加载中提示
              wx.showLoading({
                title: '加载中...',
                mask: true
              });

              // 获取exercise基本信息
              const exerciseData = await exerciseApi.getExercise(detailId);
              console.log('获取到exercise基本信息:', exerciseData);

              // 隐藏加载提示
              wx.hideLoading();

              // 导航到详情页并传递数据
              wx.navigateTo({
                url: `/pages/detail/detail?id=${detailId}`,
                success: function(res) {
                  // 传递完整的动作数据到详情页
                  res.eventChannel.emit('acceptExerciseData', {
                    exercise: exerciseData,
                  });
                },
                fail: (err) => {
                  console.error('导航到动作详情页失败:', err);
                  wx.showToast({
                    title: '页面跳转失败',
                    icon: 'none'
                  });
                }
              });
            } catch (error) {
              console.error('获取exercise数据失败:', error);
              wx.hideLoading();

              // 如果获取数据失败，仍然尝试导航，但不传递数据
              wx.navigateTo({
                url: `/pages/detail/detail?id=${detailId}`,
                fail: (err) => {
                  console.error('导航到动作详情页失败:', err);
                  wx.showToast({
                    title: '页面跳转失败',
                    icon: 'none'
                  });
                }
              });
            }
            return;
          }
        }
      }

      // 如果没有找到exercise_id，尝试使用传入的id
      console.log(`未找到exercise或exercise_id, 使用传入的id(${exerciseId})导航到详情页`);

      // 方法2: 直接使用传入的ID，但仍然尝试获取数据
      try {
        // 导入exercise API
        const exerciseApi = require('../../api/exercise');

        // 显示加载中提示
        wx.showLoading({
          title: '加载中...',
          mask: true
        });

        // 获取exercise基本信息
        const exerciseData = await exerciseApi.getExercise(exerciseId);
        console.log('获取到exercise基本信息:', exerciseData);

        // 隐藏加载提示
        wx.hideLoading();

        // 导航到详情页并传递数据
        wx.navigateTo({
          url: `/pages/detail/detail?id=${exerciseId}`,
          success: function(res) {
            // 传递完整的动作数据到详情页
            res.eventChannel.emit('acceptExerciseData', {
              exercise: exerciseData,
            });
          },
          fail: (err) => {
            console.error('导航到动作详情页失败:', err);
            wx.showToast({
              title: '页面跳转失败',
              icon: 'none'
            });
          }
        });
      } catch (error) {
        console.error('获取exercise数据失败:', error);
        wx.hideLoading();

        // 如果获取数据失败，仍然尝试导航，但不传递数据
        wx.navigateTo({
          url: `/pages/detail/detail?id=${exerciseId}`,
          fail: (err) => {
            console.error('导航到动作详情页失败:', err);
            wx.showToast({
              title: '页面跳转失败',
              icon: 'none'
            });
          }
        });
      }
    } catch (error) {
      console.error('导航到动作详情页过程中出错:', error);
      wx.showToast({
        title: '操作失败',
        icon: 'none'
      });
    }
  },

  /**
   * 处理分享按钮点击
   */
  handleShare: function() {
    console.log('分享按钮点击');
    this.startRecording();
  },

  /**
   * 开始录制页面内容
   */
  async startRecording() {
    try {
      // 设置录制状态
      this.setData({
        isRecording: true,
        progress: 0,
        statusText: '录制中...'
      });

      // 获取页面内容元素
      const query = wx.createSelectorQuery();
      query.select('#workout-content').boundingClientRect();

      query.exec(async (res) => {
        if (!res || !res[0]) {
          throw new Error('无法获取页面内容元素');
        }

        const contentRect = res[0];
        console.log('页面内容区域尺寸:', contentRect);

        // 创建并准备Canvas
        const canvas = await this.prepareCanvas(contentRect);
        if (!canvas) {
          throw new Error('创建Canvas失败');
        }

        // 开始录制
        this.recorderController = recorder.startRecording(
          canvas,
          // 进度回调
          (progress) => {
            this.setData({ progress });
          },
          // 完成回调
          (videoPath) => {
            console.log('录制完成, 临时文件路径:', videoPath);
            this.setData({
              isRecording: false,
              recordedVideoPath: videoPath,
              statusText: '处理中...',
              isProcessing: true,
              progress: 0
            });

            // 处理录制的视频
            this.processRecordedVideo(videoPath);
          },
          // 错误回调
          (error) => {
            console.error('录制失败:', error);
            this.setData({
              isRecording: false,
              isProcessing: false
            });
            wx.showToast({
              title: '录制失败',
              icon: 'none'
            });
          }
        );
      });
    } catch (error) {
      console.error('开始录制失败:', error);
      this.setData({
        isRecording: false,
        isProcessing: false
      });
      wx.showToast({
        title: '录制功能初始化失败',
        icon: 'none'
      });
    }
  },

  /**
   * 准备Canvas用于录制
   * @param {Object} contentRect - 内容区域尺寸信息
   * @returns {Promise<Object>} Canvas对象
   */
  prepareCanvas(contentRect) {
    return new Promise((resolve, reject) => {
      const query = wx.createSelectorQuery();
      query.select('#recording-canvas')
        .fields({ node: true, size: true })
        .exec((res) => {
          if (!res || !res[0] || !res[0].node) {
            reject(new Error('获取Canvas节点失败'));
            return;
          }

          const canvas = res[0].node;
          const ctx = canvas.getContext('2d');

          // 设置Canvas尺寸
          canvas.width = contentRect.width;
          canvas.height = contentRect.height;

          // 清空Canvas
          ctx.clearRect(0, 0, canvas.width, canvas.height);

          // 绘制白色背景
          ctx.fillStyle = '#f8f8f8';
          ctx.fillRect(0, 0, canvas.width, canvas.height);

          // 创建离屏Canvas用于绘制页面内容
          wx.createSelectorQuery()
            .select('#workout-content')
            .fields({ node: true, size: true, rect: true })
            .exec((result) => {
              if (!result || !result[0]) {
                reject(new Error('获取页面内容失败'));
                return;
              }

              // 将页面内容绘制到Canvas
              this.drawPageContent(ctx, result[0], canvas.width, canvas.height)
                .then(() => resolve(canvas))
                .catch(reject);
            });
        });
    });
  },

  /**
   * 绘制页面内容到Canvas
   * @param {Object} ctx - Canvas上下文
   * @param {Object} content - 页面内容元素
   * @param {Number} canvasWidth - Canvas宽度
   * @param {Number} canvasHeight - Canvas高度
   * @returns {Promise} 绘制完成的Promise
   */
  drawPageContent(ctx, content, canvasWidth, canvasHeight) {
    return new Promise((resolve) => {
      // 这里简化处理，实际应用中可能需要更复杂的绘制逻辑
      // 例如使用wx.canvasToTempFilePath捕获页面截图，然后绘制到录制Canvas

      // 绘制标题
      ctx.fillStyle = '#333';
      ctx.font = 'bold 20px sans-serif';
      ctx.fillText(this.data.workout.name, 20, 40);

      // 绘制基本信息
      ctx.font = '16px sans-serif';
      ctx.fillText(`预计时长: ${this.data.workout.estimated_duration}分钟`, 20, 70);
      ctx.fillText(`动作数量: ${this.data.workout.exercises.length}个`, 20, 100);

      // 绘制描述
      if (this.data.workout.description) {
        ctx.fillText(this.data.workout.description, 20, 130);
      }

      // 绘制动作列表标题
      ctx.font = 'bold 18px sans-serif';
      ctx.fillText('训练动作', 20, 170);

      // 绘制动作列表（简化版）
      let y = 200;
      this.data.workout.exercises.forEach((exercise, index) => {
        ctx.fillStyle = '#333';
        ctx.font = '16px sans-serif';
        ctx.fillText(`${index + 1}. ${exercise.exercise_name}`, 20, y);
        y += 30;
      });

      // 绘制完成
      resolve();
    });
  },

  /**
   * 处理录制的视频
   * @param {String} videoPath - 视频文件路径
   */
  async processRecordedVideo(videoPath) {
    try {
      // 更新状态
      this.setData({
        statusText: '上传中...',
        progress: 10
      });

      // 上传视频到后端处理
      const result = await liveApi.uploadVideo(videoPath, {
        quality: 'high',
        outputFormat: 'livephoto',
        title: this.data.workout.name,
        description: `训练详情: ${this.data.workout.name}`
      });

      console.log('视频上传处理结果:', result);

      // 更新进度
      this.setData({
        progress: 50,
        statusText: '下载中...'
      });

      // 下载处理后的视频
      if (result && result.video_url) {
        const processedVideoPath = await liveApi.downloadFile(result.video_url);

        // 更新状态
        this.setData({
          progress: 100,
          statusText: '处理完成',
          isProcessing: false,
          processedVideoUrl: result.video_url,
          recordedVideoPath: processedVideoPath,
          showShareOptions: true
        });

        // 记录分享事件
        this.trackShareEvent('prepare');
      } else {
        throw new Error('处理视频失败');
      }
    } catch (error) {
      console.error('处理视频失败:', error);
      this.setData({
        isProcessing: false
      });
      wx.showToast({
        title: '处理失败',
        icon: 'none'
      });
    }
  },

  /**
   * 分享给好友
   */
  shareToFriend() {
    console.log('分享给好友');
    this.trackShareEvent('friend');

    // 关闭分享选项对话框
    this.setData({
      showShareOptions: false
    });

    // 使用小程序原生分享功能
    wx.showShareMenu({
      withShareTicket: true,
      menus: ['shareAppMessage']
    });
  },

  /**
   * 保存到相册
   */
  async saveToAlbum() {
    try {
      console.log('保存到相册');

      // 检查是否有视频路径
      if (!this.data.recordedVideoPath) {
        throw new Error('没有可保存的视频');
      }

      // 保存到相册
      await wx.saveVideoToPhotosAlbum({
        filePath: this.data.recordedVideoPath
      });

      // 记录分享事件
      this.trackShareEvent('album');

      // 关闭分享选项对话框
      this.setData({
        showShareOptions: false
      });

      wx.showToast({
        title: '保存成功',
        icon: 'success'
      });
    } catch (error) {
      console.error('保存到相册失败:', error);

      // 判断是否是权限问题
      if (error.errMsg && error.errMsg.indexOf('auth') > -1) {
        wx.showModal({
          title: '提示',
          content: '需要您授权保存到相册',
          confirmText: '去授权',
          success: (res) => {
            if (res.confirm) {
              wx.openSetting({
                success: (settingRes) => {
                  console.log('设置结果:', settingRes);
                }
              });
            }
          }
        });
      } else {
        wx.showToast({
          title: '保存失败',
          icon: 'none'
        });
      }
    }
  },

  /**
   * 关闭分享对话框
   */
  closeShareDialog() {
    this.setData({
      showShareOptions: false
    });
  },

  /**
   * 记录分享事件
   * @param {String} shareType - 分享类型
   */
  trackShareEvent(shareType) {
    try {
      // 获取用户ID
      const userInfo = wx.getStorageSync('userInfo') || {};
      const userId = userInfo.id || '';

      // 记录分享数据
      if (userId) {
        userApi.trackShare({
          user_id: userId,
          share_type: shareType,
          page: 'pages/workout-detail/index',
          content_id: this.data.id,
          content_type: 'workout'
        }).catch(err => {
          console.error('记录分享数据失败:', err);
        });
      }
    } catch (error) {
      console.error('记录分享事件失败:', error);
    }
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function() {
    const workout = this.data.workout || {};
    const title = workout.name || '训练详情';

    // 记录分享事件
    this.trackShareEvent('menu');

    return {
      title: title,
      path: `/pages/workout-detail/index?id=${this.data.id}`,
      imageUrl: workout.image_url || '/images/share-image.png'
    };
  }
});
