const { BASE_URL, API_BASE_URL } = require('../../api/config');
const workoutTemplatesApi = require('../../api/workout-templates');

Page({

  /**
   * 页面的初始数据
   */
  data: {
    templates: [],
    isLoading: true,
    isEmpty: false,
    selectedTemplateId: null,
    isShowDeleteConfirm: false,
    fromTrainingSession: false, // 标识是否从训练会话页面跳转而来
    
    // 新增模板相关数据
    showAddTemplateModal: false,
    newTemplateForm: {
      name: '',
      estimated_duration: 60,
      description: ''
    },
    
    // 编辑模板相关数据
    isEditMode: false, // 标识是否为编辑模式
    editingTemplateId: null // 正在编辑的模板ID
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    console.log('【training-templates】页面参数:', options);
    
    // 检查是否从训练会话页面跳转而来
    if (options.fromTrainingSession === 'true') {
      this.setData({
        fromTrainingSession: true
      });
      
      // 设置导航栏标题
      wx.setNavigationBarTitle({
        title: '选择训练模板'
      });
    }
    
    this.fetchTemplates();
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    this.fetchTemplates().then(() => {
      wx.stopPullDownRefresh();
    });
  },

  /**
   * 获取用户的训练模板列表
   */
  async fetchTemplates() {
    this.setData({ isLoading: true });
    
    try {
      console.log('【fetchTemplates】开始获取训练模板');
      
      // 使用新的API模块
      const templates = await workoutTemplatesApi.getWorkoutTemplates();
      
      console.log('【fetchTemplates】获取模板结果:', templates);
      
      // 格式化创建时间和可见性文本
      templates.forEach(template => {
        if (template.created_at) {
          const date = new Date(template.created_at);
          template.formattedDate = `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}-${date.getDate().toString().padStart(2, '0')}`;
        } else {
          template.formattedDate = '未知日期';
        }
        
        // 格式化可见性文本
        const visibilityMap = {
          'everyone': '公开可见',
          'friends': '好友可见',
          'private': '仅自己可见'
        };
        template.visibilityText = visibilityMap[template.visibility] || '未知';
      });
      
      this.setData({
        templates,
        isLoading: false,
        isEmpty: templates.length === 0
      });
      
    } catch (error) {
      console.error('获取训练模板失败:', error);
      this.setData({
        isLoading: false,
        isEmpty: true
      });
      wx.showToast({
        title: '获取失败',
        icon: 'none'
      });
    }
  },

  /**
   * 使用训练模板
   */
  useTemplate(e) {
    const { id } = e.currentTarget.dataset;
    const template = this.data.templates.find(t => t.id === id);
    
    if (!template) return;
    
    console.log('【useTemplate】选择模板:', template);
    
    // 根据来源页面决定行为
    if (this.data.fromTrainingSession) {
      // 从训练会话页面跳转而来，返回选中的模板
      const pages = getCurrentPages();
      const prevPage = pages[pages.length - 2];
      
      if (prevPage) {
        // 通过eventChannel返回模板数据
        const eventChannel = this.getOpenerEventChannel();
        if (eventChannel) {
          eventChannel.emit('templateSelected', { template });
        }
      }
      
      wx.navigateBack();
    } else {
      // 原来的逻辑：跳转到自定义训练计划页面
      wx.showLoading({
        title: '加载模板...',
        mask: true
      });
      
      wx.navigateTo({
        url: '/pages/customizePlan/index?fromTemplate=true',
        success: async (res) => {
          // 传递模板数据到自定义训练计划页面
          res.eventChannel.emit('acceptTemplateData', { template });
          wx.hideLoading();
        },
        fail: (err) => {
          wx.hideLoading();
          console.error('跳转失败:', err);
          wx.showToast({
            title: '操作失败',
            icon: 'none'
          });
        }
      });
    }
  },

  /**
   * 查看模板详情
   */
  viewTemplateDetail(e) {
    const { id } = e.currentTarget.dataset;
    const template = this.data.templates.find(t => t.id === id);
    
    if (!template) {
      wx.showToast({
        title: '模板不存在',
        icon: 'none'
      });
      return;
    }
    
    console.log('【viewTemplateDetail】查看模板详情:', template);
    
    wx.showLoading({
      title: '加载模板...',
      mask: true
    });
    
    // 跳转到 plan-detail 页面查看模板详情
    wx.navigateTo({
      url: `/pages/training/plan-detail/index?mode=viewTemplate&templateId=${id}`,
      success: (res) => {
        // 通过 eventChannel 传递模板数据
        res.eventChannel.emit('templateData', {
          ...template,
          templateId: id // 确保传递模板ID用于后续更新
        });
        wx.hideLoading();
      },
      fail: (err) => {
        wx.hideLoading();
        console.error('跳转模板详情页面失败:', err);
        wx.showToast({
          title: '页面跳转失败',
          icon: 'none'
        });
      }
    });
  },

  /**
   * 显示删除确认框
   */
  showDeleteConfirm(e) {
    const { id } = e.currentTarget.dataset;
    this.setData({
      selectedTemplateId: id,
      isShowDeleteConfirm: true
    });
  },

  /**
   * 取消删除
   */
  cancelDelete() {
    this.setData({
      selectedTemplateId: null,
      isShowDeleteConfirm: false
    });
  },

  /**
   * 确认删除模板
   */
  async confirmDelete() {
    const { selectedTemplateId } = this.data;
    
    if (!selectedTemplateId) {
      this.setData({ isShowDeleteConfirm: false });
      return;
    }
    
    wx.showLoading({
      title: '删除中...',
      mask: true
    });
    
    try {
      console.log('【confirmDelete】删除模板ID:', selectedTemplateId);
      
      // 使用新的API模块
      await workoutTemplatesApi.deleteWorkoutTemplate(selectedTemplateId);
      
      wx.hideLoading();
      this.setData({ isShowDeleteConfirm: false });
      
      // 从列表中移除已删除的模板
      const updatedTemplates = this.data.templates.filter(
        t => t.id !== selectedTemplateId
      );
      
      this.setData({
        templates: updatedTemplates,
        isEmpty: updatedTemplates.length === 0
      });
      
      wx.showToast({
        title: '删除成功',
        icon: 'success'
      });
      
    } catch (error) {
      wx.hideLoading();
      this.setData({ isShowDeleteConfirm: false });
      console.error('删除训练模板失败:', error);
      wx.showToast({
        title: '删除失败',
        icon: 'none'
      });
    }
  },

  /**
   * 返回上一页
   */
  navigateBack() {
    wx.navigateBack();
  },

  /**
   * 添加新模板
   */
  addNewTemplate() {
    // 生成默认的模板名称，格式为"训练模板+日期"
    const today = new Date();
    const defaultName = `训练模板${today.getMonth() + 1}月${today.getDate()}日`;
    
    this.setData({
      showAddTemplateModal: true,
      newTemplateForm: {
        name: defaultName,
        estimated_duration: 60,
        description: ''
      }
    });
  },

  /**
   * 关闭添加模板弹窗
   */
  closeAddTemplateModal() {
    this.setData({
      showAddTemplateModal: false
    });
  },

  /**
   * 处理模板名称输入
   */
  onNewTemplateNameInput(e) {
    this.setData({
      'newTemplateForm.name': e.detail.value
    });
  },

  /**
   * 处理预计时间输入
   */
  onEstimatedDurationInput(e) {
    this.setData({
      'newTemplateForm.estimated_duration': parseInt(e.detail.value) || 60
    });
  },

  /**
   * 处理模板描述输入
   */
  onNewTemplateDescInput(e) {
    this.setData({
      'newTemplateForm.description': e.detail.value
    });
  },

  /**
   * 确认创建模板
   */
  async confirmAddTemplate() {
    const { newTemplateForm } = this.data;
    
    // 验证必填字段
    if (!newTemplateForm.name.trim()) {
      wx.showToast({
        title: '请输入模板名称',
        icon: 'none'
      });
      return;
    }
    
    if (!newTemplateForm.estimated_duration || newTemplateForm.estimated_duration <= 0) {
      wx.showToast({
        title: '请输入有效的预计时间',
        icon: 'none'
      });
      return;
    }
    
    // 关闭模态框
    this.setData({
      showAddTemplateModal: false
    });
    
    // 准备模板数据
    const templateData = {
      name: newTemplateForm.name.trim(),
      description: newTemplateForm.description.trim(),
      estimated_duration: newTemplateForm.estimated_duration,
      visibility: 'private', // 默认为私有
      exercises: [] // 空的训练动作列表
    };
    
    console.log('【confirmAddTemplate】准备跳转到plan-detail页面，模板数据:', templateData);
    
    // 跳转到 plan-detail 页面进行训练动作添加
    wx.navigateTo({
      url: '/pages/training/plan-detail/index?mode=createTemplate',
      success: (res) => {
        // 通过 eventChannel 传递模板数据
        res.eventChannel.emit('templateData', templateData);
      },
      fail: (err) => {
        console.error('跳转计划详情页面失败:', err);
        wx.showToast({
          title: '页面跳转失败',
          icon: 'none'
        });
      }
    });
  }
}); 