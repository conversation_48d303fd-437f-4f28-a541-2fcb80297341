<!--pages/training-templates/index.wxml-->
<view class="container">
  <!-- 加载中状态 -->
  <view class="loading-container" wx:if="{{isLoading}}">
    <view class="loading-spinner"></view>
    <text class="loading-text">加载中...</text>
  </view>
  
  <!-- 空状态 -->
  <view class="empty-container" wx:elif="{{isEmpty}}">
    <image class="empty-icon" src="/images/icons/body.png" mode="aspectFit"></image>
    <text class="empty-text">未找到训练模板</text>
    <button class="add-template-btn" bindtap="addNewTemplate">
      <text class="add-icon">+</text> 添加训练模板
    </button>
  </view>
  
  <!-- 模板列表 -->
  <view class="templates-list" wx:else>
    <block wx:for="{{templates}}" wx:key="id">
      <view class="template-item">
        <!-- 模板内容区域 - 添加点击查看详情功能 -->
        <view class="template-content" bindtap="viewTemplateDetail" data-id="{{item.id}}">
          <view class="template-info">
            <view class="template-name">{{item.name || '未命名模板'}}</view>
            <view class="template-description" wx:if="{{item.description}}">{{item.description}}</view>
            <view class="template-stats">
              <text class="template-count">{{item.exercise_count || 0}}个训练动作</text>
              <text class="template-date">创建于 {{item.formattedDate}}</text>
              <text class="template-visibility">{{item.visibilityText}}</text>
            </view>
          </view>
        </view>
        
        <!-- 底部操作区域 -->
        <view class="template-footer">
          <view class="use-template" bindtap="useTemplate" data-id="{{item.id}}">使用</view>
          <view class="delete-template" catchtap="showDeleteConfirm" data-id="{{item.id}}">
            <image src="/images/icons/delete.png" class="delete-icon"></image>
            <text>删除</text>
          </view>
        </view>
      </view>
    </block>
    
    <!-- 添加新模板按钮 - 当有模板时显示在底部 -->
    <view class="add-new-template-section">
      <button class="add-new-template-btn" bindtap="addNewTemplate">
        <text class="add-icon">+</text> 添加新模板
      </button>
    </view>
  </view>
  
  <!-- 删除确认弹窗 -->
  <view class="modal-mask" wx:if="{{isShowDeleteConfirm}}">
    <view class="modal-content">
      <view class="modal-title">确认删除</view>
      <view class="modal-message">确定要删除这个训练模板吗？</view>
      <view class="modal-buttons">
        <button class="modal-button cancel" bindtap="cancelDelete">取消</button>
        <button class="modal-button confirm" bindtap="confirmDelete">删除</button>
      </view>
    </view>
  </view>

  <!-- 添加新模板弹窗 -->
  <view class="modal-overlay" wx:if="{{showAddTemplateModal}}" bindtap="closeAddTemplateModal">
    <view class="modal-content" catchtap="">
      <view class="modal-header">
        <text class="modal-title">创建训练模板</text>
        <view class="modal-close" bindtap="closeAddTemplateModal">×</view>
      </view>
      
      <view class="modal-body">
        <view class="form-group">
          <text class="form-label">模板名称 *</text>
          <input class="form-input" type="text" placeholder="请输入模板名称" 
                 value="{{newTemplateForm.name}}" bindinput="onNewTemplateNameInput" maxlength="50"/>
        </view>
        
        <view class="form-group">
          <text class="form-label">预计时间（分钟） *</text>
          <input class="form-input" type="number" placeholder="60" 
                 value="{{newTemplateForm.estimated_duration}}" bindinput="onEstimatedDurationInput"/>
        </view>
        
        <view class="form-group">
          <text class="form-label">模板描述</text>
          <textarea class="form-textarea" placeholder="请输入模板描述（可选）" 
                    value="{{newTemplateForm.description}}" bindinput="onNewTemplateDescInput" maxlength="200"/>
        </view>
      </view>
      
      <view class="modal-footer">
        <button class="modal-btn cancel-btn" bindtap="closeAddTemplateModal">取消</button>
        <button class="modal-btn confirm-btn" bindtap="confirmAddTemplate">确认创建</button>
      </view>
    </view>
  </view>
</view> 