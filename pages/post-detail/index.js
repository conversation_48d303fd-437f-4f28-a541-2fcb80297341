const app = getApp();
const api = require('../../api/community.js');
const imageHelper = require('../../utils/image-helper.js');

Page({
  /**
   * 页面的初始数据
   */
  data: {
    // 帖子ID
    postId: null,
    // 帖子详情
    post: null,
    // 评论列表
    comments: [],
    // 评论分页
    commentPage: 0,
    commentLimit: 20,
    hasMoreComments: true,
    // 加载状态
    loading: true,
    loadingComments: false,
    // 输入框相关
    inputValue: '',
    inputFocused: false,
    replyToComment: null, // 回复的评论
    replyToUser: null, // @的用户
    // 训练详情展开状态
    workoutExpanded: false,
    // 图片预览
    currentImageIndex: 0,
    // 当前用户信息
    currentUser: null,
    // 显示的动作列表（最多5个）
    displayExercises: [],
    // 是否显示更多动作
    showMoreExercises: false
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    const postId = options.id;
    if (postId) {
      this.setData({ postId: parseInt(postId) });
      // 获取当前用户信息
      this.getCurrentUser();
      this.loadPostDetail();
    } else {
      wx.showToast({
        title: '帖子不存在',
        icon: 'none'
      });
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
    }
  },

  /**
   * 获取当前用户信息
   */
  getCurrentUser() {
    const app = getApp();
    if (app.globalData.userInfo) {
      this.setData({
        currentUser: app.globalData.userInfo
      });
    }
  },

  /**
   * 加载帖子详情
   */
  async loadPostDetail() {
    try {
      this.setData({ loading: true });

      const response = await api.getPostDetail(this.data.postId);
      console.log('帖子详情响应:', response);

      if (response && response.id) {
        // 处理图片URL
        if (response.images && response.images.length > 0) {
          response.images = response.images.map(img => ({
            ...img,
            url: imageHelper.getFullImageUrl(img.url)
          }));
        }

        // 处理用户头像URL
        if (response.user && response.user.avatar_url) {
          response.user.avatar_url = imageHelper.getFullImageUrl(response.user.avatar_url);
        }

        // 判断是否为当前用户
        if (this.data.currentUser && response.user) {
          response.user.is_current_user = response.user.id === this.data.currentUser.id;
        }

        // 处理训练详情中的动作图片
        if (response.related_workout_detail && response.related_workout_detail.workout_exercises) {
          response.related_workout_detail.workout_exercises = response.related_workout_detail.workout_exercises.map(exercise => ({
            ...exercise,
            exercise_image: imageHelper.getFullImageUrl(exercise.exercise_image)
          }));

          // 处理显示的动作列表（最多5个）
          this.processExerciseDisplay(response.related_workout_detail.workout_exercises);
        }

        this.setData({
          post: response,
          comments: response.comments?.items || []
        });

        // 设置页面标题
        wx.setNavigationBarTitle({
          title: response.user?.nickname || '动态详情'
        });
      } else {
        throw new Error('帖子不存在');
      }
    } catch (error) {
      console.error('加载帖子详情失败:', error);
      wx.showToast({
        title: '加载失败',
        icon: 'none'
      });
    } finally {
      this.setData({ loading: false });
    }
  },

  /**
   * 处理动作显示逻辑
   */
  processExerciseDisplay(exercises) {
    if (!exercises || exercises.length === 0) {
      this.setData({
        displayExercises: [],
        showMoreExercises: false
      });
      return;
    }

    const maxDisplay = 5;
    const displayExercises = exercises.slice(0, maxDisplay);
    const showMoreExercises = exercises.length > maxDisplay;

    this.setData({
      displayExercises,
      showMoreExercises
    });
  },

  /**
   * 加载更多评论
   */
  async loadMoreComments() {
    if (this.data.loadingComments || !this.data.hasMoreComments) {
      return;
    }

    try {
      this.setData({ loadingComments: true });

      const response = await api.getComments(this.data.postId, {
        skip: this.data.commentPage * this.data.commentLimit,
        limit: this.data.commentLimit
      });

      if (response && response.items) {
        const newComments = [...this.data.comments, ...response.items];
        this.setData({
          comments: newComments,
          commentPage: this.data.commentPage + 1,
          hasMoreComments: response.items.length === this.data.commentLimit
        });
      }
    } catch (error) {
      console.error('加载评论失败:', error);
      wx.showToast({
        title: '加载评论失败',
        icon: 'none'
      });
    } finally {
      this.setData({ loadingComments: false });
    }
  },

  /**
   * 点赞帖子
   */
  async onLikePost() {
    try {
      const isLiked = !this.data.post.is_liked_by_current_user;
      const response = await api.likePost(this.data.postId, isLiked);

      if (response.success) {
        this.setData({
          'post.is_liked_by_current_user': isLiked,
          'post.like_count': response.data.like_count
        });
      }
    } catch (error) {
      console.error('点赞失败:', error);
      wx.showToast({
        title: '操作失败',
        icon: 'none'
      });
    }
  },

  /**
   * 关注用户
   */
  async onFollowUser() {
    try {
      const isFollowing = !this.data.post.user.is_following;
      const response = await api.followUser(this.data.post.user.id, isFollowing);

      if (response.success) {
        this.setData({
          'post.user.is_following': isFollowing,
          'post.user.follower_count': response.data.follower_count
        });

        wx.showToast({
          title: isFollowing ? '关注成功' : '取消关注成功',
          icon: 'success'
        });
      }
    } catch (error) {
      console.error('关注操作失败:', error);
      wx.showToast({
        title: '操作失败',
        icon: 'none'
      });
    }
  },

  /**
   * 切换训练详情展开状态
   */
  onToggleWorkoutDetail() {
    this.setData({
      workoutExpanded: !this.data.workoutExpanded
    });
  },

  /**
   * 切换显示更多动作
   */
  onToggleMoreExercises() {
    if (!this.data.post.related_workout_detail || !this.data.post.related_workout_detail.workout_exercises) {
      return;
    }

    const exercises = this.data.post.related_workout_detail.workout_exercises;
    if (this.data.showMoreExercises) {
      // 当前显示的是前5个，切换为显示全部
      this.setData({
        displayExercises: exercises,
        showMoreExercises: false
      });
    } else {
      // 当前显示全部，切换为显示前5个
      this.setData({
        displayExercises: exercises.slice(0, 5),
        showMoreExercises: true
      });
    }
  },

  /**
   * 预览图片
   */
  onPreviewImage(e) {
    const { index } = e.currentTarget.dataset;
    const images = this.data.post.images || [];

    wx.previewImage({
      current: images[index].url,
      urls: images.map(img => img.url)
    });
  },

  /**
   * 回复评论
   */
  onReplyComment(e) {
    const { comment, user } = e.currentTarget.dataset;

    this.setData({
      replyToComment: comment,
      replyToUser: user,
      inputFocused: true,
      inputValue: user ? `@${user.nickname} ` : ''
    });
  },

  /**
   * 输入框内容变化
   */
  onInputChange(e) {
    this.setData({
      inputValue: e.detail.value
    });
  },

  /**
   * 输入框获得焦点
   */
  onInputFocus() {
    this.setData({
      inputFocused: true
    });
  },

  /**
   * 输入框失去焦点
   */
  onInputBlur() {
    // 延迟处理，避免点击发送按钮时输入框失焦
    setTimeout(() => {
      this.setData({
        inputFocused: false
      });
    }, 200);
  },

  /**
   * 发送评论
   */
  async onSendComment() {
    const content = this.data.inputValue.trim();
    if (!content) {
      wx.showToast({
        title: '请输入评论内容',
        icon: 'none'
      });
      return;
    }

    try {
      wx.showLoading({ title: '发送中...' });

      let response;
      if (this.data.replyToComment) {
        // 回复评论
        response = await api.replyComment(this.data.replyToComment.id, {
          content: content,
          reply_to_user_id: this.data.replyToUser?.id
        });
      } else {
        // 评论帖子
        response = await api.createComment(this.data.postId, {
          content: content
        });
      }

      if (response && response.id) {
        // 重新加载评论列表
        this.setData({
          inputValue: '',
          replyToComment: null,
          replyToUser: null,
          inputFocused: false
        });

        // 更新评论数量
        if (this.data.post.comment_count !== undefined) {
          this.setData({
            'post.comment_count': this.data.post.comment_count + 1
          });
        }

        // 重新加载帖子详情以获取最新评论
        this.loadPostDetail();

        wx.showToast({
          title: '发送成功',
          icon: 'success'
        });
      }
    } catch (error) {
      console.error('发送评论失败:', error);
      wx.showToast({
        title: '发送失败',
        icon: 'none'
      });
    } finally {
      wx.hideLoading();
    }
  },

  /**
   * 取消回复
   */
  onCancelReply() {
    this.setData({
      replyToComment: null,
      replyToUser: null,
      inputValue: '',
      inputFocused: false
    });
  },

  /**
   * 分享帖子
   */
  onSharePost() {
    return {
      title: this.data.post.title || '分享一个精彩动态',
      path: `/pages/post-detail/index?id=${this.data.postId}`,
      imageUrl: this.data.post.images?.[0]?.url
    };
  },

  /**
   * 页面滚动到底部
   */
  onReachBottom() {
    this.loadMoreComments();
  }
});