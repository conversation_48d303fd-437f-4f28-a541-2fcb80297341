Page({
  data: {
    userInfo: {
      name: '',
      age: '',
      height: '',
      weight: '',
      gender: '',
      photos: {
        front: '',
        back: '',
        left: '',
        right: '',
      }
    },
    assessments: [],
    postureRecords: [],
  },

  navigateToWiki() {
    wx.navigateTo({ url: '/pages/postureWiki/index' });
  },
  startAssessment() {
    wx.navigateTo({ url: '/pages/data_collection/dataCollection' });
  },

  onLoad() {
    this.fetchPostureRecords();
  },
  onShow() {
    this.fetchPostureRecords();
  },

  fetchPostureRecords() {
    // 已移除云函数调用，改为使用自建后端API
    const api = require('../../api/index');

    // 使用API获取用户姿势记录
    api.posture.getUserPostureRecords()
      .then(data => {
        const formattedRecords = data.map(record => {
          record.timestamp = this.formatTimestamp(record.timestamp);
          return record;
        });

        this.setData({
          postureRecords: formattedRecords
        });

        console.log('获取姿势记录成功:', formattedRecords.length);
      })
      .catch(err => {
        wx.showToast({
          title: '获取数据失败，请稍后再试',
          icon: 'none'
        });
        console.error('获取姿势记录失败:', err);
      });
  },

  formatTimestamp(timestamp) {
    const date = new Date(timestamp);
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    return `${year}-${month}-${day} ${hours}:${minutes}`;
  },

  viewDetails(e) {
    const id = e.currentTarget.dataset._id;
    console.log(id)
    wx.navigateTo({
      url: `/pages/posture_detail/index?id=${id}`
    });
  }
});
