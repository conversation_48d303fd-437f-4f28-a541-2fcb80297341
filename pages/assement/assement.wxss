.page-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  height: 100vh;
  justify-content: space-evenly;
  background-color: #ffffff;
}
.section-container {
  width: 90%;
  height: 6%; /* Divide the page into three equal parts */
  display: flex;
  align-items: center;
  justify-content: center;
}

.assessment-container {
  width: 90%;
  height: 22%; /* Divide the page into three equal parts */
  /* background: linear-gradient(to bottom right, #c5d3f8, #ffffff); */
  /* background-color: #cfdaf3; */
  background: linear-gradient(to bottom right, #acc2ff, #c0e9f3);
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 15rpx;
  box-shadow: 0 5rpx 10rpx rgba(0, 0, 0, 0.1);
}

.carousel-image {
  width: 100%;
  height: 50%;
  align-items: center;
  object-fit: cover;
}

.section-container {
  display: flex;
  width: 100%;
  justify-content: space-between;
}

.section-right {
  width: 95%;
  height: 100%;
  background: linear-gradient(to bottom right, #acc2ff, #c0e9f3);
  /* background-color: #a3c8ff; */
  /* background: linear-gradient(to bottom right, #b2f3d5, #aecdf1); */
  text-align: center;
  padding: 20rpx;
  border-radius: 15rpx;
  margin: 0% 5%;
}

.assessment-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
}

.assessment-title {
  font-size: 30rpx;
  font-weight: bold;
}

.assessment-subtitle {
  font-size: 26rpx;
  color: red;
  font-weight: bold;
  margin-bottom: 20rpx;
}

.assessment-button {
  width: 70%;
  padding: 15rpx;
  background-color: #007aff;
  color: #fff;
  border-radius: 25rpx;
  border: none;
}
.record-list {
  height: 60%; /* Define the height for the scrollable area */
  margin-top: 0rpx;
  width: 90%;
}

/* */
.record-list-title {
  font-weight: bold;
  font-size: 34rpx;
  text-align: left;
  padding-left: 10rpx;
  margin-bottom: 10rpx;
  position: sticky; /* Keep the title sticky */
  top: 0; /* Fixed at the top of the view */
  z-index: 10; /* Keeps it above the scroll content */
}

.scrollable-records {
  height: 100%; /* Set the scrollable height */
  overflow-y: scroll; /* Enables vertical scrolling */
}


.record-item {
  display: flex;
  padding: 20rpx;
  /* background-color: #dee5fb; */
  background: linear-gradient(to bottom right, #acc2ff, #c0e9f3);
  border-radius: 20rpx;
  margin-bottom: 20rpx;
  position: relative;
}

.record-image {
  width: 20%;
  height: auto;
  border-radius: 10rpx;
  margin-right: 20rpx;
}

.record-info {
  flex-grow: 1;
}

.record-name, .record-score, .record-status, .record-time {
  font-size: 24rpx;
  color: #333;
  margin-bottom: 10rpx;
}

.record-tag {
  position: absolute;
  top: 20rpx;
  right: 20rpx;
  background-color: #007aff;
  color: rgb(255, 255, 255);
  padding: 5rpx 10rpx;
  border-radius: 10rpx;
  font-size: 25rpx;
}
