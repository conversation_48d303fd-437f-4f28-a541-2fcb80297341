<view class="page-container">
  <!-- Carousel -->
  <!-- <swiper class="carousel" autoplay="true" interval="4000">
    <swiper-item>
      <image src="/images/icons/1.png" class="carousel-image" bindtap="navigateToPage1" />
    </swiper-item>
    <swiper-item>
      <image src="/images/icons/1.png" class="carousel-image" bindtap="navigateToPage2" />
    </swiper-item>
  </swiper> -->

  <!-- Left and Right Sections -->
  <view class="section-container">
    <!-- <view class="section-left" bindtap="navigateToManual">
      <view class="section-title">自助体态评估操作手册</view>
      <view class="section-subtitle">Manual</view>
    </view> -->
    <view class="section-right" bindtap="navigateToWiki">
      <view class="section-title">常见体态病症百科</view>
      <view class="section-subtitle">Wiki</view>
    </view>
  </view>

  <!-- Assessment Button -->
  <view class="assessment-container">
    <view class="assessment-title">精准的人工智能体态评估服务</view>
    <view class="assessment-subtitle">仅需4张照片，即可秒出报告</view>
    <button class="assessment-button" bindtap="startAssessment">开始评估</button>
  </view>

  <!-- User Posture Assessment Records -->
  <view class="record-list">
    <text class="record-list-title">评估报告</text>
    <scroll-view scroll-y="true" class="scrollable-records">
      <block wx:for="{{postureRecords}}" wx:key="_id">
        <view class="record-item" data-_id="{{item._id}}" bind:tap="viewDetails">
          <image src="{{item.body_front}}" class="record-image" />
          <view class="record-info">
            <view class="record-name">{{item.name}}</view>
            <view class="record-score">综合评分：85 分</view>
            <view class="record-status">体态问题：4 项{{item.paymentStatus}}</view>
            <view class="record-time">{{item.timestamp}}</view>
          </view>
          <view class="record-tag">查看详情</view>
        </view>
      </block>
    </scroll-view>

  </view>
</view>
