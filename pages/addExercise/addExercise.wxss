/* 页面容器 */
.container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f5f5f5;
}

/* 标签页 */
.tabs {
  display: flex;
  background-color: #fff;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  position: relative;
  z-index: 10;
}

.tab-item {
  flex: 1;
  text-align: center;
  padding: 24rpx 0;
  font-size: 28rpx;
  color: #666;
  position: relative;
}

.tab-item.active {
  color: #07c160;
  font-weight: bold;
}

.tab-item.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 25%;
  width: 50%;
  height: 4rpx;
  background-color: #07c160;
  border-radius: 2rpx;
}

/* 内容区域 */
.content-area {
  flex: 1;
  padding: 30rpx;
  box-sizing: border-box;
}

.form-section {
  padding-bottom: 40rpx;
}

.form-section.hidden {
  display: none;
}

/* 表单元素样式 */
.form-item {
  margin-bottom: 30rpx;
  background-color: #fff;
  border-radius: 12rpx;
  padding: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.form-label {
  display: block;
  font-size: 28rpx;
  color: #333;
  margin-bottom: 15rpx;
  font-weight: 500;
}

.form-input, .form-picker {
  width: 100%;
  height: 80rpx;
  background-color: #f8f9fa;
  border-radius: 8rpx;
  padding: 0 20rpx;
  box-sizing: border-box;
  font-size: 28rpx;
  color: #333;
  border: 1rpx solid #eee;
}

.picker-value {
  height: 80rpx;
  line-height: 80rpx;
  color: #333;
}

/* 媒体上传样式 */
.upload-section {
  margin-bottom: 30rpx;
  background-color: #fff;
  border-radius: 12rpx;
  padding: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.upload-title {
  font-size: 28rpx;
  font-weight: 500;
  margin-bottom: 20rpx;
  display: block;
  color: #333;
}

.upload-container {
  width: 100%;
  height: 300rpx;
  background-color: #f8f9fa;
  border-radius: 8rpx;
  border: 1rpx dashed #ddd;
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: hidden;
}

.upload-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.upload-icon {
  width: 80rpx;
  height: 80rpx;
  margin-bottom: 20rpx;
  opacity: 0.5;
}

.upload-text {
  font-size: 28rpx;
  color: #999;
}

.preview-image, .preview-video {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* 肌肉选择样式 */
.muscle-type-switch {
  display: flex;
  margin-bottom: 20rpx;
  background-color: #fff;
  border-radius: 12rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.type-option {
  flex: 1;
  text-align: center;
  padding: 20rpx 0;
  font-size: 28rpx;
  color: #666;
}

.type-option.active {
  background-color: #07c160;
  color: #fff;
}

.muscles-container {
  margin-bottom: 20rpx;
}

.body-image-container {
  display: flex;
  justify-content: space-between;
  gap: 15rpx;
}

.front-body-container, .back-body-container {
  width: 48%;
  background-color: #fff;
  border-radius: 12rpx;
  padding: 20rpx;
  box-sizing: border-box;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.body-subtitle {
  font-size: 24rpx;
  color: #666;
  text-align: center;
  display: block;
  margin-bottom: 10rpx;
}

.body-image-wrapper {
  width: 100%;
  position: relative;
}

.body-image-base {
  width: 100%;
  height: auto;
}

.muscle-area {
  position: absolute;
  cursor: pointer;
}

.muscle-highlight {
  width: 100%;
  height: 100%;
  border-radius: 4rpx;
  opacity: 0.7;
}

.muscle-highlight.primary {
  background-color: #ff6b6b;
}

.muscle-highlight.secondary {
  background-color: #5c7cfa;
}

.selected-muscles {
  background-color: #fff;
  border-radius: 12rpx;
  padding: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.section-title {
  font-size: 28rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
  color: #333;
}

.muscle-category {
  margin-bottom: 20rpx;
}

.category-label {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 10rpx;
  display: block;
}

.muscle-tags {
  display: flex;
  flex-wrap: wrap;
}

.muscle-tag {
  padding: 10rpx 20rpx;
  background-color: #f2f4f6;
  border-radius: 30rpx;
  font-size: 24rpx;
  margin-right: 15rpx;
  margin-bottom: 15rpx;
  display: flex;
  align-items: center;
}

.muscle-tag.primary {
  background-color: #ffebee;
  color: #ff6b6b;
}

.muscle-tag.secondary {
  background-color: #e7f5ff;
  color: #5c7cfa;
}

.remove-tag {
  margin-left: 10rpx;
  font-weight: bold;
  font-size: 28rpx;
}

.empty-muscle-state {
  display: flex;
  justify-content: center;
  padding: 30rpx 0;
}

.empty-text {
  color: #999;
  font-size: 28rpx;
}

/* 动作描述样式 */
.description-section {
  background-color: #fff;
  border-radius: 12rpx;
  padding: 20rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.instruction-list, .tips-list {
  margin-bottom: 20rpx;
}

.instruction-item, .tip-item {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 15rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.instruction-content, .tip-content {
  flex: 1;
  display: flex;
  align-items: flex-start;
}

.instruction-step {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 40rpx;
  height: 40rpx;
  border-radius: 50%;
  background-color: #07c160;
  color: #fff;
  font-size: 24rpx;
  margin-right: 15rpx;
  flex-shrink: 0;
}

.tip-bullet {
  color: #07c160;
  font-size: 36rpx;
  margin-right: 15rpx;
  line-height: 1;
}

.instruction-text, .tip-text {
  font-size: 28rpx;
  color: #333;
  line-height: 1.5;
}

.instruction-delete, .tip-delete {
  color: #ff6b6b;
  font-size: 32rpx;
  font-weight: bold;
  padding: 0 10rpx;
}

.add-instruction, .add-tip {
  margin-top: 20rpx;
}

.instruction-input, .tip-input {
  width: 100%;
  height: 120rpx;
  background-color: #f9f9f9;
  border-radius: 8rpx;
  padding: 20rpx;
  box-sizing: border-box;
  font-size: 28rpx;
  margin-bottom: 15rpx;
}

.add-button-small {
  width: 200rpx;
  height: 70rpx;
  background-color: #07c160;
  color: #fff;
  font-size: 28rpx;
  border-radius: 35rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-left: auto;
}

/* 底部提交按钮 */
.footer {
  padding: 20rpx 30rpx;
  background-color: #fff;
  border-top: 1rpx solid #eee;
}

.submit-button {
  width: 100%;
  height: 90rpx;
  background-color: #07c160;
  color: #fff;
  font-size: 32rpx;
  font-weight: bold;
  border-radius: 45rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}

/* 加载遮罩 */
.loading-mask {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(255, 255, 255, 0.8);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.loading {
  width: 60rpx;
  height: 60rpx;
  border: 6rpx solid #f3f3f3;
  border-top: 6rpx solid #07c160;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

.loading-text {
  font-size: 30rpx;
  color: #666;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
} 