<view class="container">
  <!-- 标签页切换 -->
  <view class="tabs">
    <view class="tab-item {{activeTab === 'basic' ? 'active' : ''}}" 
          bindtap="switchTab" data-tab="basic">基本信息</view>
    <view class="tab-item {{activeTab === 'media' ? 'active' : ''}}" 
          bindtap="switchTab" data-tab="media">媒体上传</view>
    <view class="tab-item {{activeTab === 'muscles' ? 'active' : ''}}" 
          bindtap="switchTab" data-tab="muscles">肌肉选择</view>
    <view class="tab-item {{activeTab === 'description' ? 'active' : ''}}" 
          bindtap="switchTab" data-tab="description">动作描述</view>
  </view>
  
  <!-- 表单内容区域 -->
  <scroll-view scroll-y="true" class="content-area">
    <!-- 基本信息表单 -->
    <view class="form-section {{activeTab === 'basic' ? '' : 'hidden'}}">
      <view class="form-item">
        <text class="form-label">动作名称</text>
        <input class="form-input" placeholder="请输入动作名称" value="{{newExerciseData.name}}" bindinput="onInputChange" data-field="name"/>
      </view>
      
      <view class="form-item">
        <text class="form-label">难度级别</text>
        <picker class="form-picker" bindchange="onLevelChange" value="{{levelIndex}}" range="{{levelOptions}}">
          <view class="picker-value">{{levelOptions[levelIndex]}}</view>
        </picker>
      </view>
      
      <view class="form-item">
        <text class="form-label">身体部位</text>
        <picker mode="multiSelector" bindchange="onBodyPartChange" value="{{bodyPartIndices}}" range="{{bodyPartNames}}">
          <view class="picker-value">{{selectedBodyParts || '选择身体部位'}}</view>
        </picker>
      </view>
      
      <view class="form-item">
        <text class="form-label">训练器材</text>
        <picker mode="multiSelector" bindchange="onEquipmentChange" value="{{equipmentIndices}}" range="{{equipmentNames}}">
          <view class="picker-value">{{selectedEquipments || '选择训练器材'}}</view>
        </picker>
      </view>
    </view>
    
    <!-- 媒体上传表单 -->
    <view class="form-section {{activeTab === 'media' ? '' : 'hidden'}}">
      <view class="upload-section">
        <text class="upload-title">动作图片</text>
        <view class="upload-container" bindtap="chooseImage">
          <image wx:if="{{tempImagePath}}" class="preview-image" src="{{tempImagePath}}" mode="aspectFill"></image>
          <view wx:else class="upload-placeholder">
            <image class="upload-icon" src="/images/icons/upload.png"></image>
            <text class="upload-text">上传图片</text>
          </view>
        </view>
      </view>
      
      <view class="upload-section">
        <text class="upload-title">动作GIF</text>
        <view class="upload-container" bindtap="chooseGif">
          <image wx:if="{{tempGifPath}}" class="preview-image" src="{{tempGifPath}}" mode="aspectFill"></image>
          <view wx:else class="upload-placeholder">
            <image class="upload-icon" src="/images/icons/upload.png"></image>
            <text class="upload-text">上传GIF</text>
          </view>
        </view>
      </view>
      
      <view class="upload-section">
        <text class="upload-title">动作视频</text>
        <view class="upload-container" bindtap="chooseVideo">
          <video wx:if="{{tempVideoPath}}" class="preview-video" src="{{tempVideoPath}}"></video>
          <view wx:else class="upload-placeholder">
            <image class="upload-icon" src="/images/icons/upload.png"></image>
            <text class="upload-text">上传视频</text>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 肌肉选择表单 -->
    <view class="form-section {{activeTab === 'muscles' ? '' : 'hidden'}}">
      <view class="muscle-type-switch">
        <view class="type-option {{muscleSelectType === 'primary' ? 'active' : ''}}" 
              bindtap="switchMuscleType" data-type="primary">主要肌肉</view>
        <view class="type-option {{muscleSelectType === 'secondary' ? 'active' : ''}}" 
              bindtap="switchMuscleType" data-type="secondary">次要肌肉</view>
      </view>
      
      <!-- 肌肉图 -->
      <view class="muscles-container">
        <view class="body-image-container">
          <view class="front-body-container">
            <text class="body-subtitle">前视图</text>
            <view class="body-image-wrapper">
              <image class="body-image-base" src="/images/muscles/front_base.png" mode="widthFix"></image>
              <!-- 可点击区域 -->
              <view wx:for="{{frontMuscleAreas}}" wx:key="id" class="muscle-area"
                    style="left:{{item.left}}; top:{{item.top}}; width:{{item.width}}; height:{{item.height}};"
                    data-id="{{item.id}}" data-name="{{item.name}}" bindtap="selectMuscle">
                <view class="muscle-highlight {{isPrimarySelected(item.id) ? 'primary' : ''}} {{isSecondarySelected(item.id) ? 'secondary' : ''}}"></view>
              </view>
            </view>
          </view>
          
          <view class="back-body-container">
            <text class="body-subtitle">后视图</text>
            <view class="body-image-wrapper">
              <image class="body-image-base" src="/images/muscles/back_base.png" mode="widthFix"></image>
              <!-- 可点击区域 -->
              <view wx:for="{{backMuscleAreas}}" wx:key="id" class="muscle-area"
                    style="left:{{item.left}}; top:{{item.top}}; width:{{item.width}}; height:{{item.height}};"
                    data-id="{{item.id}}" data-name="{{item.name}}" bindtap="selectMuscle">
                <view class="muscle-highlight {{isPrimarySelected(item.id) ? 'primary' : ''}} {{isSecondarySelected(item.id) ? 'secondary' : ''}}"></view>
              </view>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 已选肌肉展示 -->
      <view class="selected-muscles">
        <view class="section-title">已选肌肉</view>
        
        <view class="muscle-category" wx:if="{{newExerciseData.target_muscles_id.length > 0}}">
          <text class="category-label">主要肌肉:</text>
          <view class="muscle-tags">
            <view wx:for="{{primaryMuscleNames}}" wx:key="index" class="muscle-tag primary">
              {{item}}
              <text class="remove-tag" bindtap="removeMuscle" data-type="primary" data-index="{{index}}">×</text>
            </view>
          </view>
        </view>
        
        <view class="muscle-category" wx:if="{{newExerciseData.synergist_muscles_id.length > 0}}">
          <text class="category-label">次要肌肉:</text>
          <view class="muscle-tags">
            <view wx:for="{{secondaryMuscleNames}}" wx:key="index" class="muscle-tag secondary">
              {{item}}
              <text class="remove-tag" bindtap="removeMuscle" data-type="secondary" data-index="{{index}}">×</text>
            </view>
          </view>
        </view>
        
        <view class="empty-muscle-state" wx:if="{{newExerciseData.target_muscles_id.length === 0 && newExerciseData.synergist_muscles_id.length === 0}}">
          <text class="empty-text">请在人体图上选择肌肉</text>
        </view>
      </view>
    </view>
    
    <!-- 动作描述表单 -->
    <view class="form-section {{activeTab === 'description' ? '' : 'hidden'}}">
      <view class="description-section">
        <view class="section-title">动作介绍</view>
        <view class="instruction-list">
          <view wx:for="{{newExerciseData.ex_instructions}}" wx:key="index" class="instruction-item">
            <view class="instruction-content">
              <text class="instruction-step">{{index + 1}}</text>
              <text class="instruction-text">{{item}}</text>
            </view>
            <view class="instruction-delete" bindtap="removeInstruction" data-index="{{index}}">×</view>
          </view>
          
          <view class="add-instruction">
            <textarea class="instruction-input" placeholder="添加动作步骤说明..." 
                      value="{{newInstruction}}" bindinput="onInstructionInput"></textarea>
            <view class="add-button-small" bindtap="addInstruction">添加</view>
          </view>
        </view>
      </view>
      
      <view class="description-section">
        <view class="section-title">动作要点</view>
        <view class="tips-list">
          <view wx:for="{{newExerciseData.exercise_tips}}" wx:key="index" class="tip-item">
            <view class="tip-content">
              <text class="tip-bullet">•</text>
              <text class="tip-text">{{item}}</text>
            </view>
            <view class="tip-delete" bindtap="removeTip" data-index="{{index}}">×</view>
          </view>
          
          <view class="add-tip">
            <textarea class="tip-input" placeholder="添加动作要点..." 
                      value="{{newTip}}" bindinput="onTipInput"></textarea>
            <view class="add-button-small" bindtap="addTip">添加</view>
          </view>
        </view>
      </view>
    </view>
  </scroll-view>
  
  <!-- 底部提交按钮 -->
  <view class="footer">
    <view class="submit-button" bindtap="submitExercise">创建动作</view>
  </view>
</view>

<!-- 加载提示 -->
<view class="loading-mask" wx:if="{{isLoading}}">
  <view class="loading"></view>
  <text class="loading-text">{{loadingText}}</text>
</view> 