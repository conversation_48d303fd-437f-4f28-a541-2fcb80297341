/**
 * 添加动作页面
 * @file addExercise.js
 * @description 添加健身动作的表单页面，支持添加基本信息、上传媒体、选择肌肉和编辑动作描述
 */

// 引入API模块
const { exerciseAdapter } = require('../../api/index');

// 定义前视图肌肉区域
const FRONT_MUSCLE_AREAS = [
  { id: '8', name: '三角肌前束', left: '30%', top: '15%', width: '10%', height: '5%' },
  { id: '24', name: '胸大肌上束', left: '35%', top: '20%', width: '30%', height: '5%' },
  { id: '25', name: '胸大肌', left: '35%', top: '25%', width: '30%', height: '8%' },
  { id: '28', name: '腹直肌', left: '40%', top: '35%', width: '20%', height: '15%' },
  { id: '22', name: '腹斜肌', left: '30%', top: '35%', width: '10%', height: '10%' },
  { id: '4', name: '肱二头肌', left: '23%', top: '25%', width: '8%', height: '10%' },
  { id: '44', name: '肱三头肌', left: '20%', top: '25%', width: '8%', height: '10%' },
  { id: '27', name: '股四头肌', left: '40%', top: '55%', width: '10%', height: '15%' },
  { id: '12', name: '腓肠肌', left: '40%', top: '75%', width: '8%', height: '10%' },
];

// 定义后视图肌肉区域
const BACK_MUSCLE_AREAS = [
  { id: '9', name: '三角肌后束', left: '30%', top: '15%', width: '10%', height: '5%' },
  { id: '43', name: '斜方肌', left: '40%', top: '15%', width: '20%', height: '10%' },
  { id: '20', name: '背阔肌', left: '35%', top: '25%', width: '30%', height: '15%' },
  { id: '13', name: '臀大肌', left: '40%', top: '45%', width: '20%', height: '10%' },
  { id: '17', name: '腘绳肌', left: '40%', top: '60%', width: '10%', height: '10%' },
  { id: '44', name: '肱三头肌', left: '20%', top: '25%', width: '8%', height: '10%' },
  { id: '12', name: '腓肠肌', left: '40%', top: '75%', width: '8%', height: '10%' },
];

// 难度级别选项
const LEVEL_OPTIONS = ['初级', '中级', '高级'];

Page({
  data: {
    // 标签页状态
    activeTab: 'basic',
    // 肌肉选择类型（主要/次要）
    muscleSelectType: 'primary',
    // 难度级别相关
    levelOptions: LEVEL_OPTIONS,
    levelIndex: 0,
    // 身体部位相关
    bodyPartNames: [],
    bodyPartCategories: [],
    bodyPartIndices: [0],
    selectedBodyParts: '',
    // 器材相关
    equipmentNames: [],
    equipmentCategories: [],
    equipmentIndices: [0],
    selectedEquipments: '',
    // 肌肉选择相关
    frontMuscleAreas: FRONT_MUSCLE_AREAS,
    backMuscleAreas: BACK_MUSCLE_AREAS,
    primaryMuscleNames: [],
    secondaryMuscleNames: [],
    // 媒体文件临时路径
    tempImagePath: '',
    tempGifPath: '',
    tempVideoPath: '',
    // 动作介绍与要点
    newInstruction: '',
    newTip: '',
    // 加载状态
    isLoading: false,
    loadingText: '处理中...',
    // 新动作数据
    newExerciseData: {
      name: '',
      level: '初级',
      body_part_id: [],
      equipment_id: [],
      target_muscles_id: [],
      synergist_muscles_id: [],
      ex_instructions: [],
      exercise_tips: [],
      image_name: '',
      gif_url: '',
      video_file: ''
    }
  },

  onLoad: function() {
    // 初始化身体部位和器材数据
    this.initCategoryData();
  },

  // 初始化分类数据
  initCategoryData: function() {
    // 获取身体部位分类
    const bodyPartCategories = exerciseAdapter.getBodyPartCategories();
    const bodyPartNames = bodyPartCategories.map(item => item.name);
    
    // 获取器材分类
    const equipmentCategories = exerciseAdapter.getEquipmentCategories();
    const equipmentNames = equipmentCategories.map(item => item.name);
    
    this.setData({
      bodyPartCategories,
      bodyPartNames: [bodyPartNames],
      equipmentCategories,
      equipmentNames: [equipmentNames]
    });
  },

  // 切换标签页
  switchTab: function(e) {
    const tab = e.currentTarget.dataset.tab;
    this.setData({
      activeTab: tab
    });
  },

  // 切换肌肉选择类型（主要/次要）
  switchMuscleType: function(e) {
    const type = e.currentTarget.dataset.type;
    this.setData({
      muscleSelectType: type
    });
  },

  // 输入框值变化处理
  onInputChange: function(e) {
    const field = e.currentTarget.dataset.field;
    const value = e.detail.value;
    
    this.setData({
      [`newExerciseData.${field}`]: value
    });
  },

  // 难度级别选择
  onLevelChange: function(e) {
    const index = e.detail.value;
    const level = this.data.levelOptions[index];
    
    this.setData({
      levelIndex: index,
      'newExerciseData.level': level
    });
  },

  // 身体部位选择
  onBodyPartChange: function(e) {
    const indices = e.detail.value;
    const selectedIndices = indices[0];
    
    // 获取选中的身体部位
    const selectedItems = [this.data.bodyPartCategories[selectedIndices]];
    const selectedIds = selectedItems.map(item => item.id);
    const selectedNames = selectedItems.map(item => item.name).join(', ');
    
    this.setData({
      bodyPartIndices: indices,
      selectedBodyParts: selectedNames,
      'newExerciseData.body_part_id': selectedIds
    });
  },

  // 器材选择
  onEquipmentChange: function(e) {
    const indices = e.detail.value;
    const selectedIndices = indices[0];
    
    // 获取选中的器材
    const selectedItems = [this.data.equipmentCategories[selectedIndices]];
    const selectedIds = selectedItems.map(item => item.id);
    const selectedNames = selectedItems.map(item => item.name).join(', ');
    
    this.setData({
      equipmentIndices: indices,
      selectedEquipments: selectedNames,
      'newExerciseData.equipment_id': selectedIds
    });
  },

  // 选择图片
  chooseImage: function() {
    wx.chooseMedia({
      count: 1,
      mediaType: ['image'],
      sourceType: ['album', 'camera'],
      success: (res) => {
        this.setData({
          tempImagePath: res.tempFiles[0].tempFilePath
        });
      }
    });
  },

  // 选择GIF
  chooseGif: function() {
    wx.chooseMedia({
      count: 1,
      mediaType: ['image'],
      sourceType: ['album'],
      success: (res) => {
        this.setData({
          tempGifPath: res.tempFiles[0].tempFilePath
        });
      }
    });
  },

  // 选择视频
  chooseVideo: function() {
    wx.chooseMedia({
      count: 1,
      mediaType: ['video'],
      sourceType: ['album', 'camera'],
      maxDuration: 60,
      camera: 'back',
      success: (res) => {
        this.setData({
          tempVideoPath: res.tempFiles[0].tempFilePath
        });
      }
    });
  },

  // 肌肉选择
  selectMuscle: function(e) {
    const { id, name } = e.currentTarget.dataset;
    const type = this.data.muscleSelectType;
    const muscleId = parseInt(id);
    
    if (type === 'primary') {
      let targetMuscles = [...this.data.newExerciseData.target_muscles_id];
      let muscleNames = [...this.data.primaryMuscleNames];
      
      const index = targetMuscles.indexOf(muscleId);
      if (index > -1) {
        // 取消选择
        targetMuscles.splice(index, 1);
        muscleNames.splice(index, 1);
      } else {
        // 添加选择
        targetMuscles.push(muscleId);
        muscleNames.push(name);
      }
      
      this.setData({
        'newExerciseData.target_muscles_id': targetMuscles,
        primaryMuscleNames: muscleNames
      });
    } else {
      let synergistMuscles = [...this.data.newExerciseData.synergist_muscles_id];
      let muscleNames = [...this.data.secondaryMuscleNames];
      
      const index = synergistMuscles.indexOf(muscleId);
      if (index > -1) {
        // 取消选择
        synergistMuscles.splice(index, 1);
        muscleNames.splice(index, 1);
      } else {
        // 添加选择
        synergistMuscles.push(muscleId);
        muscleNames.push(name);
      }
      
      this.setData({
        'newExerciseData.synergist_muscles_id': synergistMuscles,
        secondaryMuscleNames: muscleNames
      });
    }
  },

  // 判断肌肉是否被选为主要肌肉
  isPrimarySelected: function(id) {
    return this.data.newExerciseData.target_muscles_id.includes(parseInt(id));
  },

  // 判断肌肉是否被选为次要肌肉
  isSecondarySelected: function(id) {
    return this.data.newExerciseData.synergist_muscles_id.includes(parseInt(id));
  },

  // 移除已选肌肉
  removeMuscle: function(e) {
    const { type, index } = e.currentTarget.dataset;
    
    if (type === 'primary') {
      let targetMuscles = [...this.data.newExerciseData.target_muscles_id];
      let muscleNames = [...this.data.primaryMuscleNames];
      
      targetMuscles.splice(index, 1);
      muscleNames.splice(index, 1);
      
      this.setData({
        'newExerciseData.target_muscles_id': targetMuscles,
        primaryMuscleNames: muscleNames
      });
    } else {
      let synergistMuscles = [...this.data.newExerciseData.synergist_muscles_id];
      let muscleNames = [...this.data.secondaryMuscleNames];
      
      synergistMuscles.splice(index, 1);
      muscleNames.splice(index, 1);
      
      this.setData({
        'newExerciseData.synergist_muscles_id': synergistMuscles,
        secondaryMuscleNames: muscleNames
      });
    }
  },

  // 动作介绍输入
  onInstructionInput: function(e) {
    this.setData({
      newInstruction: e.detail.value
    });
  },

  // 添加动作介绍步骤
  addInstruction: function() {
    if (!this.data.newInstruction.trim()) {
      wx.showToast({
        title: '请输入动作步骤',
        icon: 'none'
      });
      return;
    }
    
    const instructions = [...this.data.newExerciseData.ex_instructions, this.data.newInstruction.trim()];
    
    this.setData({
      'newExerciseData.ex_instructions': instructions,
      newInstruction: ''
    });
  },

  // 移除动作介绍步骤
  removeInstruction: function(e) {
    const index = e.currentTarget.dataset.index;
    const instructions = [...this.data.newExerciseData.ex_instructions];
    
    instructions.splice(index, 1);
    
    this.setData({
      'newExerciseData.ex_instructions': instructions
    });
  },

  // 动作要点输入
  onTipInput: function(e) {
    this.setData({
      newTip: e.detail.value
    });
  },

  // 添加动作要点
  addTip: function() {
    if (!this.data.newTip.trim()) {
      wx.showToast({
        title: '请输入动作要点',
        icon: 'none'
      });
      return;
    }
    
    const tips = [...this.data.newExerciseData.exercise_tips, this.data.newTip.trim()];
    
    this.setData({
      'newExerciseData.exercise_tips': tips,
      newTip: ''
    });
  },

  // 移除动作要点
  removeTip: function(e) {
    const index = e.currentTarget.dataset.index;
    const tips = [...this.data.newExerciseData.exercise_tips];
    
    tips.splice(index, 1);
    
    this.setData({
      'newExerciseData.exercise_tips': tips
    });
  },

  // 校验表单
  validateForm: function() {
    const { name, body_part_id, equipment_id } = this.data.newExerciseData;
    
    if (!name.trim()) {
      wx.showToast({
        title: '请输入动作名称',
        icon: 'none'
      });
      this.setData({ activeTab: 'basic' });
      return false;
    }
    
    if (body_part_id.length === 0) {
      wx.showToast({
        title: '请选择身体部位',
        icon: 'none'
      });
      this.setData({ activeTab: 'basic' });
      return false;
    }
    
    if (equipment_id.length === 0) {
      wx.showToast({
        title: '请选择训练器材',
        icon: 'none'
      });
      this.setData({ activeTab: 'basic' });
      return false;
    }
    
    if (!this.data.tempImagePath) {
      wx.showToast({
        title: '请上传动作图片',
        icon: 'none'
      });
      this.setData({ activeTab: 'media' });
      return false;
    }
    
    return true;
  },

  // 上传媒体文件
  uploadMediaFiles: async function() {
    this.setData({
      isLoading: true,
      loadingText: '上传文件中...'
    });
    
    try {
      // 上传图片
      if (this.data.tempImagePath) {
        const imageFileManager = wx.getFileSystemManager();
        const imageFileContent = imageFileManager.readFileSync(this.data.tempImagePath);
        
        const imageResult = await exerciseAdapter.uploadExerciseMedia(imageFileContent, 'image');
        if (!imageResult.success) {
          throw new Error('上传图片失败');
        }
        
        this.setData({
          'newExerciseData.image_name': imageResult.fileName
        });
      }
      
      // 上传GIF
      if (this.data.tempGifPath) {
        const gifFileManager = wx.getFileSystemManager();
        const gifFileContent = gifFileManager.readFileSync(this.data.tempGifPath);
        
        const gifResult = await exerciseAdapter.uploadExerciseMedia(gifFileContent, 'gif');
        if (!gifResult.success) {
          throw new Error('上传GIF失败');
        }
        
        this.setData({
          'newExerciseData.gif_url': gifResult.fileName
        });
      }
      
      // 上传视频
      if (this.data.tempVideoPath) {
        const videoFileManager = wx.getFileSystemManager();
        const videoFileContent = videoFileManager.readFileSync(this.data.tempVideoPath);
        
        const videoResult = await exerciseAdapter.uploadExerciseMedia(videoFileContent, 'video');
        if (!videoResult.success) {
          throw new Error('上传视频失败');
        }
        
        this.setData({
          'newExerciseData.video_file': videoResult.fileName
        });
      }
      
      return true;
    } catch (error) {
      console.error('上传文件失败:', error);
      wx.showToast({
        title: error.message || '上传文件失败',
        icon: 'none'
      });
      return false;
    } finally {
      this.setData({
        isLoading: false
      });
    }
  },

  // 提交动作数据
  submitExercise: async function() {
    // 表单校验
    if (!this.validateForm()) {
      return;
    }
    
    this.setData({
      isLoading: true,
      loadingText: '创建动作中...'
    });
    
    try {
      // 上传媒体文件
      const uploadSuccess = await this.uploadMediaFiles();
      if (!uploadSuccess) {
        return;
      }
      
      // 创建动作
      const result = await exerciseAdapter.createExercise(this.data.newExerciseData);
      
      if (!result.success) {
        throw new Error(result.error || '创建动作失败');
      }
      
      wx.showToast({
        title: '创建成功',
        icon: 'success',
        duration: 2000
      });
      
      // 成功后返回列表页
      setTimeout(() => {
        wx.navigateBack({
          delta: 1
        });
      }, 2000);
      
    } catch (error) {
      console.error('创建动作失败:', error);
      wx.showToast({
        title: error.message || '创建失败',
        icon: 'none'
      });
    } finally {
      this.setData({
        isLoading: false
      });
    }
  }
}); 