.container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  width: 100%;
  background-color: #f5f5f5;
  overflow: hidden;
}

/* 搜索栏样式 */
.search-bar {
  width: 100%;
  padding: 20rpx 30rpx;
  background-color: #fff;
  position: fixed;
  top: 0;
  z-index: 999;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.08);
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-sizing: border-box;
}

.search-input-wrapper {
  flex: 1;
  display: flex;
  align-items: center;
  background-color: #f2f4f6;
  border-radius: 30rpx;
  padding: 14rpx 20rpx;
}

.search-input {
  flex: 1;
  font-size: 28rpx;
  color: #333;
  padding-left: 10rpx;
}

.search-icon {
  width: 32rpx;
  height: 32rpx;
  opacity: 0.6;
}

/* 内容区布局 */
.content {
  display: flex;
  margin-top: 110rpx; /* 增加顶部边距，避免被搜索栏遮挡 */
  height: calc(100vh - 110rpx - 120rpx); /* 减去底部操作栏的高度 */
  width: 100%;
  box-sizing: border-box;
}

/* 侧边栏样式 */
.side-bar {
  width: 160rpx;
  background-color: #fff;
  box-shadow: 2rpx 0 8rpx rgba(0, 0, 0, 0.05);
  height: 100%;
  overflow: hidden;
  flex-shrink: 0;
}

.muscle-scroll {
  height: 100%;
  width: 100%;
}

.muscle-item {
  padding: 24rpx 10rpx;
  text-align: center;
  position: relative;
  font-size: 26rpx;
  color: #666;
  transition: all 0.3s;
  border-bottom: 1rpx solid #f0f0f0;
}

.muscle-item.selected {
  color: #07c160;
  font-weight: bold;
  background-color: #f0fff6;
}

.muscle-item.selected::before {
  content: '';
  position: absolute;
  left: 0;
  top: 25%;
  height: 50%;
  width: 6rpx;
  background-color: #07c160;
  border-radius: 3rpx;
}

/* 右侧内容区 */
.right-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;
  position: relative;
}

/* 滚动内容区域 */
.content-area {
  flex: 1;
  height: calc(100% - 50px); /* 减去分类标签的高度 */
  box-sizing: border-box;
  padding: 0 20rpx;
  overflow: hidden;
}

/* 当有底部按钮时，调整内容区域的样式 */
.content-area.has-bottom-bar {
  padding-bottom: 150rpx; /* 给底部按钮留出足够的空间 */
}

/* 原生按钮底部栏 - 最终版本 */
.action-bar-wrapper {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #ffffff;
  border-top: 1rpx solid #e5e5e5;
  box-shadow: 0 -4rpx 10rpx rgba(0, 0, 0, 0.1);
  z-index: 1000;
}

.action-bar {
  display: flex;
  align-items: center;
  padding: 20rpx 30rpx;
  gap: 20rpx;
}

/* 微信小程序原生按钮样式 */
button.action-button {
  width: 90% !important;
  font-size: 32rpx !important;
  font-weight: bold !important;
  border-radius: 45rpx !important;
  background-color: #07c160 !important;
  color: #ffffff !important;
  padding: 0 !important;
  margin: 0 !important;
  line-height: 2.8 !important;
}

/* 分类标签栏 */
.sticky-category-bar {
  background-color: #fff;
  padding: 15rpx 0;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  z-index: 10;
  width: 100%;
  box-sizing: border-box;
}

.category-scroll {
  white-space: nowrap;
  padding: 0 20rpx;
}

.category-item {
  display: inline-block;
  padding: 10rpx 20rpx;
  margin-right: 15rpx;
  background-color: #f2f4f6;
  border-radius: 30rpx;
  font-size: 24rpx;
  color: #666;
  transition: all 0.3s;
}

.category-item.selected {
  background-color: #07c160;
  color: #fff;
  font-weight: 500;
}

/* 动作列表区域 */
.exercises-container {
  padding-bottom: 30rpx;
  box-sizing: border-box;
}

.exercise-section {
  margin-top: 20rpx;
}

.section-title {
  font-size: 30rpx;
  font-weight: bold;
  margin-bottom: 15rpx;
  padding-left: 15rpx;
  position: relative;
  color: #333;
}

.section-title::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 6rpx;
  height: 24rpx;
  background-color: #07c160;
  border-radius: 3rpx;
}

.exercises-list {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20rpx;
  width: 100%;
  box-sizing: border-box;
}

.exercise-card {
  background-color: #fff;
  border-radius: 12rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.08);
  transition: transform 0.2s;
  width: 100%;
  box-sizing: border-box;
  position: relative;
}

.exercise-card:active {
  transform: scale(0.98);
}

.exercise-image {
  width: 100%;
  height: 180rpx;
  background-color: #ffffff;
  object-fit: contain;
  padding: 10rpx;
  box-sizing: border-box;
  border-bottom: 1rpx solid #f0f0f0;
}

.gif-image {
  object-fit: contain;
  background-color: #ffffff;
}

.exercise-info {
  padding: 12rpx 16rpx;
}

.exercise-name {
  font-size: 26rpx;
  color: #333;
  line-height: 1.4;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  margin-bottom: 6rpx;
}

.exercise-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 22rpx;
}

.exercise-part {
  color: #666;
  max-width: 50%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.exercise-level {
  padding: 2rpx 10rpx;
  border-radius: 10rpx;
  font-size: 20rpx;
  background-color: #e8f7f0;
  color: #07c160;
}

.level-1 {
  background-color: #e8f7f0;
  color: #07c160;
}

.level-2 {
  background-color: #fff0e8;
  color: #ff9500;
}

.level-3 {
  background-color: #ffe8e8;
  color: #ff3b30;
}

/* 选中指示器 */
.selection-indicator {
  position: absolute;
  top: 10rpx;
  right: 10rpx;
  width: 40rpx;
  height: 40rpx;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.8);
  border: 2rpx solid #ddd;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0.7;
}

.selection-indicator.selected {
  background-color: #07c160;
  border-color: #07c160;
  opacity: 1;
}

.selection-icon {
  color: white;
  font-size: 24rpx;
  font-weight: bold;
}

/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40rpx 0;
  width: 100%;
}

.loading {
  width: 40rpx;
  height: 40rpx;
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid #07c160;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 10rpx;
}

.loading-text {
  font-size: 26rpx;
  color: #999;
}

.loading-more {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 30rpx 0;
  color: #999;
  font-size: 24rpx;
}

.loading-more .loading {
  width: 30rpx;
  height: 30rpx;
  margin: 0 10rpx 0 0;
}

.no-more {
  text-align: center;
  color: #999;
  font-size: 24rpx;
  padding: 30rpx 0;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80rpx 0;
  width: 100%;
}

.empty-icon {
  width: 120rpx;
  height: 120rpx;
  opacity: 0.5;
  margin-bottom: 20rpx;
}

.empty-text {
  font-size: 30rpx;
  color: #666;
  margin-bottom: 10rpx;
}

.empty-subtext {
  font-size: 26rpx;
  color: #999;
}

/* 底部操作栏 */
.bottom-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  background-color: #fff;
  box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.15);
  padding: 20rpx 30rpx;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  z-index: 1000; /* 增加z-index确保在最上层 */
  border-top: 1rpx solid #eee; /* 添加顶部边框增强视觉区分 */
}

.selected-count {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 15rpx;
}

/* 已选动作列表 */
.selected-exercises-scroll {
  width: 100%;
  white-space: nowrap;
  margin-bottom: 15rpx;
  background-color: #f9f9f9; /* 添加背景色 */
  border-radius: 8rpx;
  padding: 10rpx;
}

.selected-exercises-list {
  display: inline-flex;
  padding: 10rpx 0;
  width: 100%;
}

.selected-exercise-item {
  position: relative;
  margin-right: 20rpx;
  width: 120rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  border: 1rpx solid #eee; /* 添加边框 */
  padding: 5rpx;
  background-color: #fff; /* 确保背景为白色 */
  border-radius: 6rpx;
}

.selected-exercise-image {
  width: 100rpx;
  height: 100rpx;
  border-radius: 8rpx;
  background-color: #f5f5f5;
  object-fit: cover;
  border: 1rpx solid #ddd; /* 添加边框 */
}

.selected-exercise-name {
  font-size: 22rpx;
  color: #333;
  width: 120rpx;
  text-align: center;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  margin-top: 6rpx;
  font-weight: 500; /* 加粗文字 */
}

.remove-btn {
  position: absolute;
  top: -10rpx;
  right: 0;
  width: 36rpx;
  height: 36rpx;
  border-radius: 50%;
  background-color: #ff4d4f;
  color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  font-weight: bold;
  box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
}

.options-section {
  margin-bottom: 15rpx;
  width: 100%;
}

.set-type-picker {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #f5f5f5;
  padding: 15rpx 20rpx;
  border-radius: 8rpx;
  font-size: 26rpx;
  color: #333;
  border: 1rpx solid #e0e0e0; /* 添加边框 */
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.05); /* 添加阴影 */
}

.picker-arrow {
  width: 0;
  height: 0;
  border-left: 10rpx solid transparent;
  border-right: 10rpx solid transparent;
  border-top: 10rpx solid #999;
}

.confirm-button {
  width: 32%;
  height: 80rpx;
  background-color: #07c160;
  color: #fff;
  border-radius: 8rpx;
  font-size: 30rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0;
  box-shadow: 0 4rpx 8rpx rgba(7, 193, 96, 0.2); /* 添加阴影 */
  font-weight: 500;
}

.join-plan-btn {
  width: 32%;
  height: 80rpx;
  background-color: #1989fa;
  color: #fff;
  border-radius: 8rpx;
  font-size: 30rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0;
  box-shadow: 0 4rpx 8rpx rgba(25, 137, 250, 0.2); /* 添加阴影 */
  font-weight: 500;
}

.start-training-btn {
  width: 32%;
  height: 80rpx;
  background-color: #ff9500;
  color: #fff;
  border-radius: 8rpx;
  font-size: 30rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0;
  box-shadow: 0 4rpx 8rpx rgba(255, 149, 0, 0.2); /* 添加阴影 */
  font-weight: 500;
}

.button-container {
  display: flex;
  justify-content: space-between;
  width: 100%;
  margin-top: 10rpx; /* 增加顶部间距 */
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 已选择动作摘要 */
.selected-exercises-summary {
  display: flex;
  align-items: center;
  gap: 16rpx;
  padding: 16rpx;
  background-color: #f8f9fa;
  border-radius: 16rpx;
  min-width: 160rpx;
}

.selected-icon-wrapper {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.selected-icon {
  font-size: 32rpx;
}

.selected-count {
  position: absolute;
  top: -8rpx;
  right: -8rpx;
  background-color: #ff4757;
  color: #ffffff;
  font-size: 20rpx;
  font-weight: bold;
  min-width: 28rpx;
  height: 28rpx;
  line-height: 28rpx;
  text-align: center;
  border-radius: 14rpx;
  padding: 0 6rpx;
}

.selected-text {
  font-size: 26rpx;
  color: #333333;
}

/* 组类型选择器 */
.set-type-selector {
  flex: 1;
  padding: 16rpx;
  background-color: #f8f9fa;
  border-radius: 16rpx;
}

.set-type-display {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.set-type-label {
  font-size: 26rpx;
  color: #666666;
}

.set-type-value {
  font-size: 26rpx;
  color: #333333;
  font-weight: 500;
}

.picker-arrow {
  font-size: 20rpx;
  color: #999999;
  margin-left: auto;
}

/* 完成按钮 */
.action-button {
  background-color: #07c160;
  color: #ffffff;
  font-size: 30rpx;
  font-weight: 500;
  border-radius: 24rpx;
  padding: 20rpx 32rpx;
  min-width: 180rpx;
  border: none;
}

.action-button:active {
  opacity: 0.8;
}

.action-button::after {
  border: none;
}

/* 已选择动作详情弹窗 */
.selected-exercises-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 2000;
  display: none;
  align-items: flex-end;
}

.selected-exercises-modal.show {
  display: flex;
}

.modal-content {
  width: 100%;
  max-height: 70vh;
  background-color: #ffffff;
  border-radius: 24rpx 24rpx 0 0;
  overflow: hidden;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32rpx 40rpx;
  border-bottom: 1rpx solid #e5e5e5;
}

.modal-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333333;
}

.modal-close {
  font-size: 40rpx;
  color: #999999;
  padding: 8rpx;
}

.selected-exercises-list {
  max-height: 60vh;
  padding: 20rpx 40rpx;
}

.selected-exercise-item {
  display: flex;
  align-items: center;
  padding: 24rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.selected-exercise-item:last-child {
  border-bottom: none;
}

.selected-exercise-image {
  width: 100rpx;
  height: 100rpx;
  border-radius: 12rpx;
  margin-right: 24rpx;
  background-color: #f5f5f5;
}

.selected-exercise-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.selected-exercise-name {
  font-size: 30rpx;
  font-weight: 500;
  color: #333333;
}

.selected-exercise-category {
  font-size: 24rpx;
  color: #666666;
}

.selected-exercise-type {
  font-size: 22rpx;
  color: #07c160;
  background-color: #f6ffed;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  align-self: flex-start;
}

.remove-exercise-btn {
  padding: 16rpx;
  margin-left: 16rpx;
}

.remove-icon {
  font-size: 32rpx;
}

.selected-empty {
  text-align: center;
  padding: 60rpx 0;
}

.selected-empty-text {
  font-size: 28rpx;
  color: #999999;
}

/* 为内容区域添加底部间距，避免被操作栏遮挡 */
.content-area.has-bottom-bar {
  padding-bottom: 200rpx;
}
