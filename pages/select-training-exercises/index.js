/**
 * 训练动作选择页面
 * @file index.js
 * @description 基于动作库页面，添加了动作选择、组数类型选择和确认功能
 */

// 引入工具类
const ExerciseListHelper = require('../../utils/exercise-list-helper');
const imageHelper = require('../../utils/image-helper');

Page({
  data: {
    planId: null,
    muscleGroups: [],
    categories: [],
    bodyPartMapping: {},
    equipmentMapping: {},
    exercisesList: [],
    filteredExercises: [],
    exerciseGroups: [],
    isLoading: true,
    scrollToView: '',
    searchQuery: '',
    selectedBodyPartId: ExerciseListHelper.getDefaultBodyPartId(),
    selectedEquipmentId: ExerciseListHelper.getDefaultEquipmentId(),
    currentPage: 1,
    hasMore: true,
    isLoadingMore: false,
    preloadedData: {},
    isPreloading: false,
    showEmptyState: false,
    selectedExercises: {}, // 存储已选择的动作，使用对象以便快速查找
    selectedExercisesArray: [], // 已选择动作的数组形式，用于显示
    setTypes: ['热身', '正式', '递减'],
    defaultSetType: 1, // 默认选择"正式"
    showSelectedModal: false, // 控制已选择动作弹窗显示
    _isRefreshing: false,
    reachBottomTimer: null,
    hasBottomBarPadding: false
  },

  onLoad(options) {
    if (options.planId) {
      console.log('接收到的planId:', options.planId);
      this.setData({
        planId: options.planId
      });
    }

    // 确保 selectedExercises 正确初始化为空对象
    this.setData({
      selectedExercises: {},
      selectedExercisesArray: [],
      hasBottomBarPadding: false,
      showSelectedModal: false
    }, () => {
      // 确认数据初始化
      console.log('初始化完成，selectedExercises:', JSON.stringify(this.data.selectedExercises));
      console.log('初始化完成，selectedExercisesArray:', JSON.stringify(this.data.selectedExercisesArray));
      console.log('初始化完成，hasBottomBarPadding:', this.data.hasBottomBarPadding);
    });

    console.log('页面加载，初始化 selectedExercises:', this.data.selectedExercises);
    console.log('检查小程序基础库版本...');
    const version = wx.getSystemInfoSync().SDKVersion;
    console.log('当前基础库版本:', version);

    // 初始化分类数据
    this.initCategories();

    // 打印当前环境信息，帮助调试
    console.log('当前环境信息:');
    try {
      const systemInfo = wx.getSystemInfoSync();
      console.log('系统信息:', systemInfo);
      console.log('页面路径:', getCurrentPages()[0].route);
    } catch (error) {
      console.error('获取系统信息失败:', error);
    }
  },

  /**
   * 初始化分类数据
   */
  initCategories() {
    // 使用工具类初始化分类数据
    const categoryData = ExerciseListHelper.initCategories();

    this.setData({
      ...categoryData
    });

    // 首先加载默认筛选条件的数据
    this.fetchExerciseList(true);
  },

  /**
   * 获取动作列表数据
   * @param {Boolean} isInitialLoad - 是否初始加载
   * @param {Boolean} forceRefresh - 是否强制刷新当前分类的数据
   */
  async fetchExerciseList(isInitialLoad = false, forceRefresh = false) {
    try {
      if (!isInitialLoad && !this.data.hasMore && !forceRefresh) {
        return;
      }

      // 如果不是初始加载，则显示加载更多状态
      if (!isInitialLoad) {
        this.setData({
          isLoadingMore: true
        });
      }

      const currentPage = isInitialLoad || forceRefresh ? 1 : this.data.currentPage + 1;

      // 使用工具类获取数据
      const result = await ExerciseListHelper.fetchExerciseList({
        bodyPartId: this.data.selectedBodyPartId,
        equipmentId: this.data.selectedEquipmentId,
        page: currentPage,
        useCache: isInitialLoad || forceRefresh,
        preloadedData: this.data.preloadedData
      });

      // 更新状态
      let updatedExercisesList = [];
      if (isInitialLoad || forceRefresh) {
        updatedExercisesList = result.exercisesList;
      } else {
        updatedExercisesList = [...this.data.exercisesList, ...result.exercisesList];
      }

      this.setData({
        exercisesList: updatedExercisesList,
        currentPage,
        hasMore: result.hasMore,
        isLoading: false,
        isLoadingMore: false,
        showEmptyState: updatedExercisesList.length === 0
      }, () => {
        this.organizeExercises();
      });

      // 预加载其他分类的数据
      if (!this.data.isPreloading) {
        this.preloadOtherCategories();
      }
    } catch (error) {
      console.error('获取动作列表失败:', error);
      wx.showToast({
        title: '获取数据失败',
        icon: 'none'
      });

      this.setData({
        isLoading: false,
        isLoadingMore: false
      });
    }
  },

  /**
   * 预加载其他分类的数据
   */
  async preloadOtherCategories() {
    this.setData({ isPreloading: true });

    try {
      // 预加载其他身体部位的数据（除当前选中的）
      const bodyPartCategories = ExerciseListHelper.getBodyPartCategories();
      const otherBodyParts = bodyPartCategories
        .filter(part => part.id !== this.data.selectedBodyPartId)
        .slice(0, 3); // 取前3个，避免过多请求

      // 预加载其他器材的数据（除当前选中的）
      const equipmentCategories = ExerciseListHelper.getEquipmentCategories();
      const otherEquipments = equipmentCategories
        .filter(equip => equip.id !== this.data.selectedEquipmentId)
        .slice(0, 3); // 取前3个，避免过多请求

      // 预加载身体部位
      for (const part of otherBodyParts) {
        await this.preloadCategory(part.id, this.data.selectedEquipmentId);
      }

      // 预加载器材
      for (const equip of otherEquipments) {
        await this.preloadCategory(this.data.selectedBodyPartId, equip.id);
      }
    } catch (error) {
      console.error('预加载数据失败:', error);
    } finally {
      this.setData({ isPreloading: false });
    }
  },

  /**
   * 预加载特定分类的数据
   * @param {Number} bodyPartId - 身体部位ID
   * @param {Number} equipmentId - 器材ID
   */
  async preloadCategory(bodyPartId, equipmentId) {
    try {
      // 使用工具类预加载数据
      const result = await ExerciseListHelper.preloadCategory(bodyPartId, equipmentId);

      // 缓存预加载的数据
      const preloadedData = this.data.preloadedData;
      preloadedData[result.key] = result.data;

      this.setData({
        preloadedData
      });
    } catch (error) {
      console.error(`预加载分类数据失败 [身体部位=${bodyPartId}, 器材=${equipmentId}]:`, error);
    }
  },

  /**
   * 组织动作数据，按类别分组
   */
  organizeExercises() {
    const { exercisesList } = this.data;

    // 使用工具类组织数据
    const result = ExerciseListHelper.organizeExercises(exercisesList);

    this.setData({
      exerciseGroups: result.exerciseGroups,
      showEmptyState: result.showEmptyState
    });
  },

  /**
   * 选择肌肉组
   */
  selectMuscleGroup(e) {
    const selectedId = parseInt(e.currentTarget.dataset.id);

    // 每次选择肌肉组时，重置器材选择为"自重"(ID=2)
    const DEFAULT_EQUIPMENT_ID = ExerciseListHelper.getDefaultEquipmentId();

    const muscleGroups = this.data.muscleGroups.map(item => ({
      ...item,
      selected: item.id === selectedId
    }));

    const categories = this.data.categories.map(item => ({
      ...item,
      selected: item.id === DEFAULT_EQUIPMENT_ID
    }));

    // 检查是否有预加载的数据
    const key = `${selectedId}_${DEFAULT_EQUIPMENT_ID}`;
    const preloadedData = this.data.preloadedData[key];

    // 检查预加载数据是否存在且有效
    const hasValidPreloadedData = preloadedData &&
                                preloadedData.exercises &&
                                preloadedData.exercises.length > 0 &&
                                Date.now() - (preloadedData.timestamp || 0) < 3600000; // 1小时内的缓存有效

    if (hasValidPreloadedData) {
      this.setData({
        muscleGroups,
        categories,
        selectedBodyPartId: selectedId,
        selectedEquipmentId: DEFAULT_EQUIPMENT_ID,
        currentPage: 1,
        hasMore: preloadedData.hasMore,
        exercisesList: preloadedData.exercises,
        isLoading: false
      }, () => {
        this.organizeExercises();
      });
    } else {
      this.setData({
        muscleGroups,
        categories,
        selectedBodyPartId: selectedId,
        selectedEquipmentId: DEFAULT_EQUIPMENT_ID,
        currentPage: 1,
        hasMore: true,
        exercisesList: [],
        exerciseGroups: [],
        isLoading: true
      }, () => {
        this.fetchExerciseList(true);
      });
    }
  },

  /**
   * 选择器材分类
   */
  selectCategory(e) {
    const selectedId = parseInt(e.currentTarget.dataset.id);

    const categories = this.data.categories.map(item => ({
      ...item,
      selected: item.id === selectedId
    }));

    // 检查是否有预加载的数据
    const key = `${this.data.selectedBodyPartId}_${selectedId}`;
    const preloadedData = this.data.preloadedData[key];

    // 检查预加载数据是否存在且有效
    const hasValidPreloadedData = preloadedData &&
                                preloadedData.exercises &&
                                preloadedData.exercises.length > 0 &&
                                Date.now() - (preloadedData.timestamp || 0) < 3600000; // 1小时内的缓存有效

    if (hasValidPreloadedData) {
      this.setData({
        categories,
        selectedEquipmentId: selectedId,
        currentPage: 1,
        hasMore: preloadedData.hasMore,
        exercisesList: preloadedData.exercises,
        isLoading: false
      }, () => {
        this.organizeExercises();
      });
    } else {
      this.setData({
        categories,
        selectedEquipmentId: selectedId,
        currentPage: 1,
        hasMore: true,
        exercisesList: [],
        exerciseGroups: [],
        isLoading: true
      }, () => {
        this.fetchExerciseList(true);
      });
    }
  },

  /**
   * 搜索功能
   */
  onSearchInput(e) {
    const searchQuery = e.detail.value.trim();

    // 如果搜索词为空，恢复原始列表
    if (!searchQuery) {
      this.fetchExerciseList(true);
      return;
    }

    // 使用工具类在当前加载的数据中搜索
    const { exercisesList } = this.data;
    const filteredExercises = ExerciseListHelper.searchExercises(exercisesList, searchQuery);

    // 更新UI
    this.setData({
      searchQuery,
      exercisesList: filteredExercises,
      hasMore: false // 禁用加载更多
    }, () => {
      this.organizeExercises();
    });
  },

  /**
   * 滚动到底部加载更多
   */
  onReachBottom() {
    // 使用节流避免频繁触发
    if (this.data.reachBottomTimer) {
      clearTimeout(this.data.reachBottomTimer);
    }

    this.setData({
      reachBottomTimer: setTimeout(() => {
        if (this.data.hasMore && !this.data.isLoadingMore && !this.data.isLoading) {
          this.fetchExerciseList(false);
        }
      }, 300)
    });
  },

  /**
   * 选择动作
   */
  selectExercise(e) {
    try {
      const { id } = e.currentTarget.dataset;

      if (!id) {
        console.error('未获取到动作ID');
        return;
      }

      console.log('选择动作，ID:', id);
      console.log('当前selectedExercises状态:', JSON.stringify(this.data.selectedExercises));

      // 找到对应的动作数据
      const exercise = this.data.exercisesList.find(item => Number(item.id) === Number(id));

      if (!exercise) {
        console.error('未找到对应的动作数据');
        return;
      }

      // 更新选中状态
      const selectedExercises = { ...this.data.selectedExercises };

      if (selectedExercises[id]) {
        // 如果已选中，则取消选中
        delete selectedExercises[id];
        console.log(`取消选中动作: ${id}, 当前已选: ${Object.keys(selectedExercises).length}`);
      } else {
        // 如果未选中，则添加到选中列表
        const imageUrl = exercise.full_image_url || exercise.full_gif_url;
        // 处理图片URL，确保使用完整路径
        const processedImageUrl = imageHelper.getFullImageUrl(imageUrl);

        selectedExercises[id] = {
          id: exercise.id,
          exercise_id: exercise.id,
          name: exercise.name,
          imageUrl: processedImageUrl,
          category: exercise.move_cate,
          setType: this.data.setTypes[this.data.defaultSetType]
        };

        console.log(`选中动作: ${id}, 当前已选: ${Object.keys(selectedExercises).length}`);
        console.log('图片URL:', exercise.full_image_url || exercise.full_gif_url);
        console.log('选中的动作数据:', selectedExercises[id]);

        // 显示添加成功提示
        wx.showToast({
          title: '已添加',
          icon: 'success',
          duration: 500
        });
      }

      const selectedCount = Object.keys(selectedExercises).length;

      console.log('更新前的底部栏状态:', this.data.hasBottomBarPadding);
      console.log('更新前的selectedExercises:', JSON.stringify(this.data.selectedExercises));

      // 更新selectedExercisesArray，用于显示
      const selectedExercisesArray = Object.values(selectedExercises);

      // 更新数据，简化底部按钮显示
      this.setData({
        selectedExercises,
        selectedExercisesArray,
        hasBottomBarPadding: selectedCount > 0
      }, () => {
        // 在回调中确认数据已更新
        console.log('数据更新完成后的状态:');
        console.log('- selectedExercises:', JSON.stringify(this.data.selectedExercises));
        console.log('- 选中数量:', Object.keys(this.data.selectedExercises).length);
        console.log('- hasBottomBarPadding:', this.data.hasBottomBarPadding);

        // 强制更新UI
        if (selectedCount > 0 && !this.data.hasBottomBarPadding) {
          this.setData({ hasBottomBarPadding: true });
        }
      });

      // 打印详细日志
      console.log('=== 选择动作后状态 ===');
      console.log(`已选动作数量: ${selectedCount}`);
      console.log(`底部区域padding状态: ${this.data.hasBottomBarPadding}`);
      if(selectedCount > 0) {
        console.log('已选动作内容:', JSON.stringify(Object.values(selectedExercises)[0]));
      }
    } catch (error) {
      console.error('选择动作出错:', error);
    }
  },

  /**
   * 确认选择
   */
  confirmSelection() {
    const selectedExercises = this.data.selectedExercises;
    const selectedCount = Object.keys(selectedExercises).length;
    const planId = this.data.planId;
    const mode = this.options.mode || 'add'; // 获取模式，默认为add

    console.log('[DEBUG] 确认选择，当前已选动作数量:', selectedCount);
    console.log('[DEBUG] 已选动作详情:', JSON.stringify(selectedExercises));
    console.log('[DEBUG] 当前planId:', planId);
    console.log('[DEBUG] 当前模式:', mode);

    if (selectedCount === 0) {
      wx.showToast({
        title: '请至少选择一个动作',
        icon: 'none'
      });
      return;
    }

    // 将选中的动作转换为数组
    const exercisesArray = Object.values(selectedExercises);
    console.log('[DEBUG] 转换为数组后的已选动作:', JSON.stringify(exercisesArray));

    // 获取组类型
    const setType = this.data.setTypes[this.data.defaultSetType];
    console.log('[DEBUG] 选择的组类型:', setType);

    // 准备返回数据
    const selectedData = {
      exercises: exercisesArray,
      setType: setType,
      planId: planId // 传递planId确保一致性
    };

    console.log('[DEBUG] 准备传递的完整数据:', JSON.stringify(selectedData));

    // 返回上一页
    wx.navigateBack({
      success: () => {  // 使用箭头函数保持this的指向
        try {
          // 仅使用事件通道进行通信
          const eventChannel = this.getOpenerEventChannel && this.getOpenerEventChannel();
          if (eventChannel && eventChannel.emit) {
            // 根据不同的模式发送不同的事件
            const eventName = mode === 'replace' ? 'replaceExercise' : 'addExercises';
            const eventData = mode === 'replace' ? 
              { exercise: selectedData.exercises[0] } : // 替换模式只需要第一个选中的动作
              { exercises: selectedData.exercises };    // 添加模式传递所有选中的动作
              
            console.log(`[DEBUG] 通过事件通道发送 ${eventName} 事件`);
            eventChannel.emit(eventName, eventData);
          } else {
            console.error('[DEBUG] 无法获取事件通道');
          }
        } catch (error) {
          console.error('[DEBUG] 处理返回数据错误:', error);
        }
      },
      fail: function(error) {
        console.error('[DEBUG] 返回上一页失败:', error);
      }
    });
  },

  /**
   * 开始训练
   */
  startTraining() {
    const selectedExercises = this.data.selectedExercises;
    const selectedCount = Object.keys(selectedExercises).length;

    if (selectedCount === 0) {
      wx.showToast({
        title: '请至少选择一个动作',
        icon: 'none'
      });
      return;
    }

    // 将选中的动作转换为数组
    const exercisesArray = Object.values(selectedExercises);

    // 获取组类型
    const setType = this.data.setTypes[this.data.defaultSetType];

    // 准备数据
    const trainingData = {
      exercises: exercisesArray,
      setType: setType,
      date: new Date().toISOString().split('T')[0] // 今天的日期
    };

    // 跳转到训练页面
    wx.navigateTo({
      url: '/pages/training/index',
      success: function(res) {
        // 传递数据
        res.eventChannel.emit('acceptTrainingData', trainingData);
      },
      fail: function(err) {
        console.error('跳转到训练页面失败:', err);
        wx.showToast({
          title: '操作失败，请重试',
          icon: 'none'
        });
      }
    });
  },

  /**
   * 移除已选动作
   */
  removeSelectedExercise(e) {
    try {
      const { id } = e.currentTarget.dataset;

      if (!id) {
        console.error('未获取到动作ID');
        return;
      }

      console.log(`尝试移除动作: ${id}`);
      console.log('当前selectedExercises状态:', JSON.stringify(this.data.selectedExercises));

      // 更新选中状态
      const selectedExercises = { ...this.data.selectedExercises };

      if (selectedExercises[id]) {
        // 删除选中的动作
        delete selectedExercises[id];
        console.log(`成功移除动作: ${id}, 剩余已选: ${Object.keys(selectedExercises).length}`);

        const selectedCount = Object.keys(selectedExercises).length;

        // 更新selectedExercisesArray，用于显示
        const selectedExercisesArray = Object.values(selectedExercises);

        this.setData({
          selectedExercises,
          selectedExercisesArray,
          hasBottomBarPadding: selectedCount > 0
        }, () => {
          // 在回调中确认数据已更新
          console.log('移除动作后的状态:');
          console.log('- selectedExercises:', JSON.stringify(this.data.selectedExercises));
          console.log('- 选中数量:', Object.keys(this.data.selectedExercises).length);
        });

        console.log('=== 移除动作后状态 ===');
        console.log(`剩余已选动作数量: ${selectedCount}`);
        console.log(`底部区域padding状态: ${this.data.hasBottomBarPadding}`);
      } else {
        console.error(`未找到要移除的动作: ${id}`);
      }
    } catch (error) {
      console.error('移除动作出错:', error);
    }
  },

  /**
   * 处理组类型变化
   */
  handleSetTypeChange(e) {
    const typeIndex = parseInt(e.detail.value);

    this.setData({
      defaultSetType: typeIndex
    });

    // 更新所有已选动作的组类型
    const selectedExercises = { ...this.data.selectedExercises };

    Object.keys(selectedExercises).forEach(id => {
      selectedExercises[id].setType = this.data.setTypes[typeIndex];
    });

    // 更新selectedExercisesArray，用于显示
    const selectedExercisesArray = Object.values(selectedExercises);

    this.setData({
      selectedExercises,
      selectedExercisesArray
    });
  },

  /**
   * 处理图片加载错误
   * @param {Object} e - 错误事件对象
   */
  onImageError(e) {
    const id = e.currentTarget.dataset.id;
    console.log('图片加载失败:', id);

    // 使用工具类处理图片错误
    const updatedList = ExerciseListHelper.handleImageError(this.data.exercisesList, id);

    this.setData({
      exercisesList: updatedList
    }, () => {
      // 重新组织数据
      this.organizeExercises();
    });
  },

  /**
   * 处理已选动作图片加载错误
   * @param {Object} e - 错误事件对象
   */
  onSelectedImageError(e) {
    const id = e.currentTarget.dataset.id;
    console.log('已选动作图片加载失败:', id);

    // 使用工具类处理已选动作图片错误
    const updatedSelected = { ...this.data.selectedExercises };

    if (updatedSelected[id]) {
      console.log('尝试修复图片:', updatedSelected[id].imageUrl);

      try {
        // 导入图片处理工具
        const imageHelper = require('../../utils/image-helper');

        // 使用图片处理工具处理图片URL
        updatedSelected[id].imageUrl = imageHelper.handleImageError(
          updatedSelected[id].imageUrl,
          '/images/exercises/default.png'
        );

        console.log('修复后的图片URL:', updatedSelected[id].imageUrl);

        this.setData({
          selectedExercises: updatedSelected
        });
      } catch (error) {
        console.error('处理图片错误失败:', error);

        // 如果图片URL包含http但不包含https，尝试将http替换为https
        if (updatedSelected[id].imageUrl && updatedSelected[id].imageUrl.includes('http:') && !updatedSelected[id].imageUrl.includes('https:')) {
          updatedSelected[id].imageUrl = updatedSelected[id].imageUrl.replace('http:', 'https:');
          console.log('修复后的图片URL:', updatedSelected[id].imageUrl);
        } else {
          // 如果无法修复，使用默认图片
          updatedSelected[id].imageUrl = '/images/exercises/default.png';
          console.log('使用默认图片');
        }

        this.setData({
          selectedExercises: updatedSelected
        });
      }
    }
  },

  /**
   * 显示已选择动作详情
   */
  showSelectedExercises() {
    console.log('显示已选择动作详情');
    this.setData({
      showSelectedModal: true
    });
  },

  /**
   * 隐藏已选择动作详情
   */
  hideSelectedExercises() {
    console.log('隐藏已选择动作详情');
    this.setData({
      showSelectedModal: false
    });
  }
});
