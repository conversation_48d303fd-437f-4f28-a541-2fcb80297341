---
description: 
globs: 
alwaysApply: true
---

# 微信小程序开发规范

## 核心原则
- 编写清晰高效的代码，遵循微信小程序最佳实践
- 使用平台支持的ES6+特性
- 使用JavaScript进行开发
- 遵循微信的安全和性能指南
- 使用描述性变量名（例如isLoading, hasUserInfo）
- 按照小程序规范组织文件结构

## 文件结构与命名
- 组件和页面名称使用kebab-case（例如user-profile）
- 文件组织到pages/、components/、utils/和services/目录下
- 遵循小程序文件扩展名：.wxml、.wxss、.js、.json
- JavaScript文件使用.js扩展名
- 配置信息保存在app.json和页面级.json文件中
- 目录中的主文件使用index命名

## 组件指南
- 为常用UI元素创建可复用组件
- 保持组件小而专注
- 使用属性进行组件配置
- 为组件属性提供默认值和验证
- 实现清晰命名的事件处理程序（例如handleTap, onSubmit）
- 正确实现生命周期方法

## JavaScript/WXML
- 使用async/await处理异步操作
- 为API响应和请求数据创建清晰的数据结构
- 为API调用实现适当的错误处理
- 使用wx.showToast()提供用户反馈
- 充分利用小程序内置组件
- 遵循MVVM模式使用setData()
- 使用模板字符串处理动态内容
- 尽量避免使用setTimeout/setInterval

## 性能优化
- 在列表渲染中使用wx:key
- 正确实现页面生命周期方法
- 使用lazy-load优化图片加载
- 高效使用createSelectorQuery
- 最小化setData调用和数据大小
- 正确实现下拉刷新
- 异步加载非关键资源

## 安全性
- 验证所有用户输入
- 使用适当的数据加密方法
- 实现安全的身份验证
- 遵循微信的安全指南
- 妥善处理敏感数据

## 存储与状态管理
- 使用适当的存储方法（wx.setStorage）
- 为存储的数据结构提供清晰的注释
- 实现高效的数据缓存
- 适当处理全局状态
- 谨慎使用getApp()访问全局状态
- 登出时清除敏感数据

## 关键约定
1. 遵循微信的设计指南
2. 实现适当的错误处理
3. 优化移动端性能
4. 遵循小程序安全标准
5. 使用JSDoc注释提高代码可读性

## 测试
- 在各种设备和操作系统版本上测试
- 实现适当的错误日志记录
- 编写测试用例
- 使用小程序调试工具
- 测试网络条件
- 验证微信API兼容性

## JavaScript特定指南
- 为函数添加JSDoc注释说明参数和返回值
- 使用常量对象管理固定值
- 创建结构清晰的数据对象
- 为可重用组件和工具函数编写清晰文档
- 使用注释说明复杂逻辑

> 参考微信小程序文档了解组件、API和最佳实践。

