/**
 * 登录页面占位图片
 * 在实际项目中应当使用设计好的图片资源替换
 */

// 用于产生简单的Base64格式占位图片
const generatePlaceholderLogo = () => {
  return 'data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxMDAiIGhlaWdodD0iMTAwIiB2aWV3Qm94PSIwIDAgMTAwIDEwMCI+PGNpcmNsZSBjeD0iNTAiIGN5PSI1MCIgcj0iNTAiIGZpbGw9IiM0Qzg4RjUiLz48dGV4dCB4PSI1MCIgeT0iNTAiIGZvbnQtc2l6ZT0iMzYiIGZpbGw9IiNGRkYiIHRleHQtYW5jaG9yPSJtaWRkbGUiIGRvbWluYW50LWJhc2VsaW5lPSJtaWRkbGUiPkg8L3RleHQ+PC9zdmc+';
};

// 微信图标占位符
const generateWechatIcon = () => {
  return 'data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyNCAyNCI+PHBhdGggZmlsbD0iIzA3QzE2MCIgZD0iTTkuNSwxMy43YzAsMC41LTAuNCwwLjktMC45LDAuOWMtMC41LDAtMC45LTAuNC0wLjktMC45YzAtMC41LDAuNC0wLjksMC45LTAuOUMzLjEsOS4xLDkuNSwxMy4yLDkuNSwxMy43TTE4LDE2LjhjMCwwLjUtMC40LDAuOS0wLjksMC45Yy0wLjUsMC0wLjktMC40LTAuOS0wLjljMC0wLjUsMC40LTAuOSwwLjktMC45QzE3LjYsMTUuOSwxOCwxNi4zLDE4LDE2LjhNMTQuNSw5LjNjMCwwLjMtMC4yLDAuNS0wLjUsMC41Yy0wLjMsMC0wLjUtMC4yLTAuNS0wLjVjMC0wLjMsMC4yLTAuNSwwLjUtMC41QzE0LjMsOC44LDE0LjUsOSwxNC41LDkuM00yMC4yLDkuM2MwLDAuMy0wLjIsMC41LTAuNSwwLjVjLTAuMywwLTAuNS0wLjItMC41LTAuNWMwLTAuMywwLjItMC41LDAuNS0wLjVDMjAsOC44LDIwLjIsOSwyMC4yLDkuM00yMi44LDIwLjRjMCwwLjksMCwxLjgtMC4xLDIuN0gyLjFjMC0wLjktMC4xLTEuOC0wLjEtMi43YzAtNi42LDQuNi0xMiwxMC40LTEyQzE4LjIsOC40LDIyLjgsMTMuOCwyMi44LDIwLjRNMTguOCw0LjNjMC0wLjktMC43LTEuNi0xLjYtMS42Yy0wLjksMC0xLjYsMC43LTEuNiwxLjZjMCwwLjksMC43LDEuNiwxLjYsMS42QzE4LjEsNS45LDE4LjgsNS4yLDE4LjgsNC4zTTExLjQsNi44YzAsMS40LTEuMSwyLjUtMi41LDIuNWMtMS40LDAtMi41LTEuMS0yLjUtMi41YzAtMS40LDEuMS0yLjUsMi41LTIuNUMxMC4zLDQuMywxMS40LDUuNCwxMS40LDYuOCIvPjwvc3ZnPg==';
};

module.exports = {
  generatePlaceholderLogo,
  generateWechatIcon
}; 