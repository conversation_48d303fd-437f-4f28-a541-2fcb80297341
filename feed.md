# ScienceFit 微信小程序方案文档

## 一、页面结构

### 1. 社区动态页（Social Feed）
- 路径: `/pages/community/index`
- 功能: 展示所有用户分享的训练信息
- 组件: 帖子卡片、点赞按钮、评论区、训练概览

### 2. 帖子详情页（Post Detail）
- 路径: `/pages/post-detail/index`
- 功能: 展示帖子内容和训练的详细信息
- 组件: 用户信息栏、帖子内容、训练统计、肌肉分布图、运动列表、评论区

### 3. 用户资料页（User Profile）
- 路径: `/pages/user-profile/index`
- 功能: 展示用户资料和训练历史
- 组件: 用户信息卡、活动统计图、训练列表、关注按钮

## 二、主要功能

### 社区动态页
- 浏览所有用户的训练分享
- 点赞/评论/分享帖子
- 查看训练概览信息
- 跳转至帖子详情或用户资料

### 帖子详情页
- 查看帖子完整内容和图片
- 查看完整训练数据（时长、总重量、组数等）
- 查看肌肉分布情况
- 查看训练中的每个动作详情
- 点赞/评论帖子

### 用户资料页
- 查看用户基本信息和训练统计
- 关注/取消关注用户
- 查看用户的训练计划和历史记录
- 浏览用户活动图表

## 三、组件设计

### 公共组件
1. **Post-Card**: 帖子卡片组件
2. **Comment-Section**: 评论区组件
3. **Like-Button**: 点赞按钮组件
4. **User-Info-Bar**: 用户信息栏组件
5. **Exercise-Item**: 运动项目组件
6. **Muscle-Split**: 肌肉分布图组件

### 页面专用组件
1. **Post-Content**: 帖子内容组件
2. **Workout-Stats**: 训练统计组件
3. **Activity-Chart**: 活动图表组件
4. **Workout-List**: 训练列表组件
5. **Follow-Button**: 关注按钮组件

## 四、低保真界面设计

### 1. 社区动态页
```
┌──────────────────────────────┐
│ ┌────┐ 用户名         •••   │
│ │头像│ 2小时前              │
│ └────┘                      │
│                             │
│ 训练标题                     │
│ 训练内容描述...              │
│                             │
│ ┌─────────────────────────┐ │
│ │ 训练类型  时长  总重量   │ │
│ │ ┌───────────────────┐   │ │
│ │ │ 运动1  组数 重量  │   │ │
│ │ │ 运动2  组数 重量  │   │ │
│ │ └───────────────────┘   │ │
│ └─────────────────────────┘ │
│                             │
│ 👍 评论 分享                │
└──────────────────────────────┘
```

### 2. 帖子详情页
```
┌──────────────────────────────┐
│ ← 帖子详情           •••    │
│                              │
│ ┌────┐ 用户名               │
│ │头像│ 日期 - 时间          │
│ └────┘                      │
│                             │
│ 帖子标题                     │
│ 帖子内容描述...              │
│ [帖子图片/视频]              │
│                             │
│ 训练笔记...                  │
│                             │
│ 时长    总重量    评分       │
│                             │
│ 肌肉分布                     │
│ ████████████▁▁▁▁ 腿 100%    │
│                             │
│ 训练内容                     │
│ ┌─────────────────────────┐ │
│ │ 🚶‍♂️ 步行               │ │
│ │ 组1: 0km - 10min 0s     │ │
│ └─────────────────────────┘ │
│                             │
│ ┌─────────────────────────┐ │
│ │ 🏋️‍♂️ 罗马尼亚硬拉      │ │
│ │ 组1: 20kg x 20 次       │ │
│ │ 组2: 40kg x 12 次       │ │
│ │ 组3: 50kg x 12 次       │ │
│ │ 组4: 55kg x 5 次        │ │
│ └─────────────────────────┘ │
│                             │
│ 👍 评论 分享                │
└──────────────────────────────┘
```

### 3. 用户资料页
```
┌──────────────────────────────┐
│ ← 用户名             •••    │
│                              │
│ ┌────┐ 用户名               │
│ │头像│ 简介                 │
│ └────┘                      │
│                             │
│ 训练数 | 关注者 | 关注中     │
│ 498    | 566    | 345       │
│                             │
│ 本周训练活动                 │
│ █ █ █ █ █ █ █               │
│ 一 二 三 四 五 六 日         │
│                             │
│ 训练计划                     │
│ ┌─────────────────────────┐ │
│ │ 计划名称1               │ │
│ │ 计划名称2      >        │ │
│ └─────────────────────────┘ │
│                             │
│ 最近训练                     │
│ ┌─────────────────────────┐ │
│ │ 训练标题                │ │
│ │ 时间 重量 评分          │ │
│ │ 训练描述...             │ │
│ └─────────────────────────┘ │
└──────────────────────────────┘
```

## 五、交互流程

1. **社区动态浏览流程**
   - 用户打开小程序 → 进入社区页面
   - 浏览帖子列表 → 点击帖子 → 跳转至帖子详情页
   - 点击用户头像 → 跳转至用户资料页
   - 点击点赞按钮 → 点赞数+1
   - 点击评论按钮 → 展开评论区
   - 下拉刷新 → 加载最新帖子

2. **帖子详情查看流程**
   - 用户点击帖子 → 进入帖子详情页
   - 查看帖子内容和图片 → 查看训练数据 → 查看肌肉分布 → 查看各运动详情
   - 点击用户头像 → 跳转至用户资料页
   - 点击返回 → 返回社区页面

3. **用户资料查看流程**
   - 用户点击头像 → 进入用户资料页
   - 查看用户信息 → 查看训练统计 → 查看训练历史
   - 点击关注按钮 → 关注/取消关注用户
   - 点击训练计划 → 查看详细计划
   - 点击返回 → 返回上一页面

## 六、数据对接
接口在/api 中进行实现管理
### 社区动态页
- 调用 `GET /api/v1/community/posts/` 获取帖子列表
- 调用 `POST /api/v1/community/posts/{post_id}/like/` 实现点赞功能
- 调用 `POST /api/v1/community/posts/{post_id}/comments/` 发表评论

### 帖子详情页
- 调用 `GET /api/v1/community/posts/{post_id}` 获取帖子详情
- 调用 `GET /api/v1/community/daily-workouts/{workout_id}` 获取训练详情
- 调用 `POST /api/v1/community/posts/{post_id}/like/` 点赞帖子
- 调用 `POST /api/v1/community/posts/{post_id}/comments/` 评论帖子

### 用户资料页
- 调用 `GET /api/v1/community/users/{user_id}` 获取用户资料
- 调用 `GET /api/v1/community/users/{user_id}/workouts` 获取用户训练列表
- 调用 `GET /api/v1/community/users/{user_id}/stats` 获取统计数据
- 调用 `POST /api/v1/community/users/{user_id}/follow/` 关注用户

## 七、后端接口优化建议

1. 新增以下接口:
   - `GET /api/v1/community/posts/{post_id}` - 获取单个帖子详情（包含关联训练）
   - `GET /api/v1/community/daily-workouts/{workout_id}` - 获取单个训练详情
   - `GET /api/v1/community/users/{user_id}` - 获取用户资料
   - `GET /api/v1/community/users/{user_id}/workouts` - 获取用户训练列表
   - `GET /api/v1/community/users/{user_id}/stats` - 获取用户训练统计数据

2. 修改以下接口:
   - 在 `GET /api/v1/community/posts/` 中增加用户信息和训练概览
   - 确保帖子API返回相关图片信息
