{"compileType": "miniprogram", "libVersion": "trial", "packOptions": {"ignore": [], "include": []}, "miniprogramRoot": "./", "setting": {"coverView": true, "es6": true, "postcss": true, "minified": true, "enhance": true, "showShadowRootInWxmlPanel": true, "babelSetting": {"ignore": [], "disablePlugins": [], "outputPath": ""}, "uglifyFileName": false, "condition": false, "urlCheck": true}, "condition": {}, "editorSetting": {"tabIndent": "auto", "tabSize": 2}, "appid": "wx39e96f46f7ddb61b", "dependencies": {"wx-server-sdk": "*"}, "packNpmManually": true, "packNpmRelationList": [{"packageJsonPath": "./package.json", "miniprogramNpmDistDir": "./"}], "projectArchitecture": "multiPlatform", "simulatorPluginLibVersion": {"wxext14566970e7e9f62": "3.2.5-51"}, "srcMiniprogramRoot": ""}