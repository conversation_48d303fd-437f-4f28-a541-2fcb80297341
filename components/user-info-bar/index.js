Component({
  /**
   * 组件的属性列表
   */
  properties: {
    // 用户数据
    user: {
      type: Object,
      value: {}
    },
    // 时间
    time: {
      type: String,
      value: ''
    },
    // 是否显示关注按钮
    showFollow: {
      type: Boolean,
      value: false
    },
    // 是否已关注
    isFollowing: {
      type: Boolean,
      value: false
    },
    // 是否显示更多按钮
    showMore: {
      type: Boolean,
      value: false
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    // 格式化后的时间
    formattedTime: ''
  },

  /**
   * 数据监听器
   */
  observers: {
    'time': function(time) {
      if (time) {
        this.setData({
          formattedTime: this.formatTime(time)
        });
      }
    }
  },

  /**
   * 组件的方法列表
   */
  methods: {
    /**
     * 格式化时间
     */
    formatTime(timeStr) {
      if (!timeStr) return '';
      
      const date = new Date(timeStr);
      const now = new Date();
      const diff = Math.floor((now - date) / 1000); // 秒差
      
      if (diff < 60) {
        return '刚刚';
      } else if (diff < 3600) {
        return Math.floor(diff / 60) + '分钟前';
      } else if (diff < 86400) {
        return Math.floor(diff / 3600) + '小时前';
      } else if (diff < 2592000) {
        return Math.floor(diff / 86400) + '天前';
      } else {
        const year = date.getFullYear();
        const month = date.getMonth() + 1;
        const day = date.getDate();
        return `${year}年${month}月${day}日`;
      }
    },

    /**
     * 点击用户头像或名字
     */
    onTapUser() {
      this.triggerEvent('tapuser', { userId: this.data.user.id });
    },

    /**
     * 点击关注按钮
     */
    onTapFollow() {
      this.triggerEvent('follow', { 
        userId: this.data.user.id,
        isFollowing: !this.data.isFollowing
      });
    },

    /**
     * 点击更多按钮
     */
    onTapMore() {
      this.triggerEvent('more', { userId: this.data.user.id });
    }
  }
}); 