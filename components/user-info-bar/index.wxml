<view class="user-info-bar">
  <view class="user-avatar" bindtap="onTapUser">
    <image class="avatar" src="{{user.avatar || '/images/default-avatar.png'}}" mode="aspectFill"></image>
  </view>
  
  <view class="user-details">
    <view class="user-name-row">
      <view class="user-name" bindtap="onTapUser">{{user.username || '用户名'}}</view>
      <view class="follow-btn {{isFollowing ? 'following' : ''}}" wx:if="{{showFollow}}" bindtap="onTapFollow">
        {{isFollowing ? '已关注' : '关注'}}
      </view>
    </view>
    
    <view class="user-info-extra">
      <view class="time" wx:if="{{formattedTime}}">{{formattedTime}}</view>
      <view class="user-extra-info" wx:if="{{user.extra_info}}">{{user.extra_info}}</view>
    </view>
  </view>
  
  <view class="more-btn" wx:if="{{showMore}}" bindtap="onTapMore">
    <text class="more-icon">•••</text>
  </view>
</view> 