/* 用户信息栏样式 */
.user-info-bar {
  display: flex;
  align-items: center;
  padding: 16rpx 0;
}

/* 用户头像 */
.user-avatar {
  margin-right: 16rpx;
}

.avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  background-color: #f0f0f0;
}

/* 用户详情 */
.user-details {
  flex: 1;
}

.user-name-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.user-name {
  font-size: 30rpx;
  font-weight: 500;
  color: #333;
  margin-right: 16rpx;
}

.follow-btn {
  font-size: 24rpx;
  padding: 4rpx 16rpx;
  border-radius: 30rpx;
  background-color: #07C160;
  color: #fff;
}

.follow-btn.following {
  background-color: #f0f0f0;
  color: #666;
}

/* 额外信息 */
.user-info-extra {
  display: flex;
  align-items: center;
  margin-top: 4rpx;
}

.time {
  font-size: 24rpx;
  color: #999;
}

.user-extra-info {
  font-size: 24rpx;
  color: #666;
  margin-left: 16rpx;
}

/* 更多按钮 */
.more-btn {
  padding: 0 16rpx;
}

.more-icon {
  font-size: 28rpx;
  color: #999;
} 