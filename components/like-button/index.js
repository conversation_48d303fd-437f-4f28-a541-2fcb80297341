Component({
  /**
   * 组件的属性列表
   */
  properties: {
    // 点赞数量
    count: {
      type: Number,
      value: 0
    },
    // 是否已点赞
    isLiked: {
      type: Boolean,
      value: false
    },
    // 是否显示数量
    showCount: {
      type: Boolean,
      value: true
    },
    // 按钮大小
    size: {
      type: String,
      value: 'medium' // small, medium, large
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    // 点击中状态
    tapping: false
  },

  /**
   * 组件的方法列表
   */
  methods: {
    /**
     * 点赞按钮点击
     */
    onTap() {
      if (this.data.tapping) return;
      
      this.setData({ tapping: true });
      
      // 触发点赞事件
      this.triggerEvent('like', {
        isLiked: !this.data.isLiked
      });
      
      // 按钮反馈动画
      setTimeout(() => {
        this.setData({ tapping: false });
      }, 200);
    }
  }
}); 