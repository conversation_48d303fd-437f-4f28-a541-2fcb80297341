/* 评论区样式 */
.comment-section {
  padding: 0 8rpx;
}

/* 评论列表 */
.comment-list {
  margin-bottom: 20rpx;
}

.comment-item {
  padding: 16rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.comment-item.reply-comment {
  margin-left: 40rpx;
  background-color: #f9f9f9;
  border-radius: 8rpx;
  padding: 16rpx 12rpx;
}

/* 评论头部 */
.comment-header {
  display: flex;
}

.avatar-container {
  margin-right: 16rpx;
}

.avatar {
  width: 64rpx;
  height: 64rpx;
  border-radius: 50%;
  background-color: #f0f0f0;
}

.comment-info {
  flex: 1;
}

.username {
  font-size: 26rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 4rpx;
}

.comment-content {
  font-size: 26rpx;
  color: #333;
  line-height: 1.5;
  word-wrap: break-word;
}

.reply-to {
  color: #07C160;
  margin-right: 4rpx;
}

/* 评论操作 */
.comment-actions {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 8rpx;
}

.comment-time {
  font-size: 22rpx;
  color: #999;
}

.action-buttons {
  display: flex;
  align-items: center;
}

.action-btn {
  font-size: 24rpx;
  color: #666;
  margin-left: 20rpx;
  padding: 4rpx 0;
}

.action-btn.liked {
  color: #07C160;
}

/* 加载更多 */
.load-more {
  text-align: center;
  font-size: 26rpx;
  color: #07C160;
  padding: 20rpx 0;
}

.loading {
  text-align: center;
  padding: 20rpx 0;
}

.loading-text {
  font-size: 26rpx;
  color: #999;
}

.no-more {
  text-align: center;
  font-size: 24rpx;
  color: #999;
  padding: 20rpx 0;
}

/* 无评论状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40rpx 0;
}

.empty-icon {
  font-size: 64rpx;
  color: #ddd;
  margin-bottom: 16rpx;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
}

/* 评论输入区 */
.comment-input-area {
  position: sticky;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #fff;
  padding: 16rpx 0;
  border-top: 1rpx solid #f0f0f0;
}

.reply-info {
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 24rpx;
  color: #666;
  padding: 8rpx 16rpx;
  background-color: #f8f9fa;
  border-radius: 8rpx;
  margin-bottom: 12rpx;
}

.cancel-reply {
  padding: 0 10rpx;
  font-size: 28rpx;
}

.input-container {
  display: flex;
  align-items: center;
}

.comment-input {
  flex: 1;
  height: 72rpx;
  background-color: #f5f5f5;
  border-radius: 36rpx;
  padding: 0 24rpx;
  font-size: 28rpx;
}

.send-btn {
  width: 120rpx;
  height: 72rpx;
  line-height: 72rpx;
  text-align: center;
  font-size: 28rpx;
  color: #999;
}

.send-btn.active {
  color: #07C160;
} 