// components/comment-section/index.js
Component({
  /**
   * 组件的属性列表
   */
  properties: {
    // 评论列表
    comments: {
      type: Array,
      value: []
    },
    // 帖子ID
    postId: {
      type: Number,
      value: 0
    },
    // 是否加载中
    loading: {
      type: Boolean,
      value: false
    },
    // 是否已加载全部
    loadedAll: {
      type: Boolean,
      value: false
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    // 评论内容
    commentText: '',
    // 提交中
    submitting: false,
    // 回复的评论ID
    replyTo: null,
    // 回复的用户名
    replyToUsername: ''
  },

  /**
   * 组件的方法列表
   */
  methods: {
    /**
     * 处理输入框内容变化
     */
    onInputChange(e) {
      this.setData({
        commentText: e.detail.value
      });
    },

    /**
     * 提交评论
     */
    onSubmitComment() {
      if (!this.data.commentText.trim()) {
        wx.showToast({
          title: '评论内容不能为空',
          icon: 'none'
        });
        return;
      }

      if (this.data.submitting) return;

      this.setData({ submitting: true });

      // 构造评论数据
      const commentData = {
        content: this.data.commentText,
        parent_id: this.data.replyTo
      };

      // 触发提交事件
      this.triggerEvent('submit', {
        postId: this.data.postId,
        commentData: commentData
      });

      // 清空输入
      this.setData({
        commentText: '',
        replyTo: null,
        replyToUsername: '',
        submitting: false
      });
    },

    /**
     * 回复评论
     */
    onReplyComment(e) {
      const { commentId, username } = e.currentTarget.dataset;
      this.setData({
        replyTo: commentId,
        replyToUsername: username
      });
      // 聚焦输入框
      this.selectComponent('.comment-input').focus();
    },

    /**
     * 取消回复
     */
    onCancelReply() {
      this.setData({
        replyTo: null,
        replyToUsername: ''
      });
    },

    /**
     * 点赞评论
     */
    onLikeComment(e) {
      const { commentId, index } = e.currentTarget.dataset;
      
      // 获取当前评论
      const comment = this.data.comments[index];
      const isLiked = !comment.is_liked;
      let likeCount = comment.like_count || 0;
      
      if (isLiked) {
        likeCount += 1;
      } else {
        likeCount = Math.max(0, likeCount - 1);
      }
      
      // 更新本地状态
      const comments = [...this.data.comments];
      comments[index] = {
        ...comment,
        is_liked: isLiked,
        like_count: likeCount
      };
      
      this.setData({ comments });
      
      // 触发点赞事件
      this.triggerEvent('like', { 
        commentId,
        isLiked
      });
    },

    /**
     * 加载更多评论
     */
    onLoadMore() {
      if (this.data.loading || this.data.loadedAll) return;
      
      this.triggerEvent('loadmore', {
        postId: this.data.postId,
        skip: this.data.comments.length
      });
    },

    /**
     * 点击用户头像
     */
    onTapAvatar(e) {
      const { userId } = e.currentTarget.dataset;
      this.triggerEvent('tapavatar', { userId });
    },

    /**
     * 更多操作
     */
    onTapMore(e) {
      const { commentId, index } = e.currentTarget.dataset;
      const comment = this.data.comments[index];
      
      const itemList = ['举报'];
      if (comment.is_owner) {
        itemList.unshift('删除');
      }
      
      wx.showActionSheet({
        itemList: itemList,
        success: (res) => {
          if (comment.is_owner && res.tapIndex === 0) {
            // 删除评论
            this.triggerEvent('delete', { commentId });
          } else {
            // 举报评论
            this.triggerEvent('report', { commentId });
          }
        }
      });
    }
  }
}); 