<view class="comment-section">
  <!-- 评论列表 -->
  <block wx:if="{{comments && comments.length > 0}}">
    <view class="comment-list">
      <block wx:for="{{comments}}" wx:key="id">
        <view class="comment-item {{item.parent_id ? 'reply-comment' : ''}}">
          <!-- 评论头部 -->
          <view class="comment-header">
            <view class="avatar-container" bindtap="onTapAvatar" data-user-id="{{item.user_id}}">
              <image class="avatar" src="{{item.user_avatar || '/images/default-avatar.png'}}" mode="aspectFill"></image>
            </view>
            
            <view class="comment-info">
              <view class="username">{{item.username || '用户'}}</view>
              <view class="comment-content">
                <text wx:if="{{item.reply_to}}" class="reply-to">回复 {{item.reply_to}}: </text>
                {{item.content}}
              </view>
              
              <!-- 评论操作 -->
              <view class="comment-actions">
                <view class="comment-time">{{item.created_at_formatted || '刚刚'}}</view>
                
                <view class="action-buttons">
                  <view class="action-btn" bindtap="onReplyComment" data-comment-id="{{item.id}}" data-username="{{item.username}}">
                    回复
                  </view>
                  
                  <view class="action-btn {{item.is_liked ? 'liked' : ''}}" bindtap="onLikeComment" data-comment-id="{{item.id}}" data-index="{{index}}">
                    👍 {{item.like_count || ''}}
                  </view>
                  
                  <view class="action-btn" bindtap="onTapMore" data-comment-id="{{item.id}}" data-index="{{index}}">
                    •••
                  </view>
                </view>
              </view>
            </view>
          </view>
        </view>
      </block>
    </view>
    
    <!-- 加载更多 -->
    <view class="load-more" wx:if="{{!loadedAll && !loading}}" bindtap="onLoadMore">
      加载更多评论
    </view>
    
    <view class="loading" wx:if="{{loading}}">
      <view class="loading-text">加载中...</view>
    </view>
    
    <view class="no-more" wx:if="{{loadedAll && comments.length > 0}}">
      没有更多评论了
    </view>
  </block>
  
  <!-- 无评论状态 -->
  <view class="empty-state" wx:elif="{{!loading}}">
    <view class="empty-icon">💬</view>
    <view class="empty-text">暂无评论，快来抢沙发吧！</view>
  </view>
  
  <!-- 加载中状态 -->
  <view class="loading" wx:if="{{loading && comments.length === 0}}">
    <view class="loading-text">加载中...</view>
  </view>
  
  <!-- 评论输入区 -->
  <view class="comment-input-area">
    <view class="reply-info" wx:if="{{replyTo}}">
      <text>回复 {{replyToUsername}}</text>
      <view class="cancel-reply" bindtap="onCancelReply">×</view>
    </view>
    
    <view class="input-container">
      <input 
        class="comment-input"
        type="text"
        placeholder="{{replyTo ? '回复 ' + replyToUsername : '说点什么...'}}"
        value="{{commentText}}"
        bindinput="onInputChange"
        confirm-type="send"
        confirm-hold
      />
      
      <view class="send-btn {{commentText ? 'active' : ''}}" bindtap="onSubmitComment">
        发送
      </view>
    </view>
  </view>
</view> 