.custom-button {
  box-sizing: border-box;
  padding: 0 30rpx;
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  font-size: 30rpx;
  border-radius: 8rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  transition: all 0.2s;
}

/* 类型样式 */
.custom-button.default {
  background-color: #f8f8f8;
  color: #333333;
  border: 1rpx solid #dddddd;
}

.custom-button.primary {
  background-color: #4a90e2;
  color: #ffffff;
  border: 1rpx solid #4a90e2;
}

.custom-button.secondary {
  background-color: #f5f5f5;
  color: #666666;
  border: 1rpx solid #dddddd;
}

.custom-button.danger {
  background-color: #ff5151;
  color: #ffffff;
  border: 1rpx solid #ff5151;
}

/* 大小样式 */
.custom-button.small {
  height: 60rpx;
  line-height: 60rpx;
  font-size: 24rpx;
}

.custom-button.normal {
  height: 80rpx;
  line-height: 80rpx;
  font-size: 30rpx;
}

.custom-button.large {
  height: 100rpx;
  line-height: 100rpx;
  font-size: 34rpx;
}

/* 圆角样式 */
.custom-button.circle {
  border-radius: 40rpx;
}

/* 禁用状态 */
.custom-button.disabled {
  opacity: 0.6;
  background-color: #b2b2b2;
  color: #ffffff;
  border: 1rpx solid #b2b2b2;
} 