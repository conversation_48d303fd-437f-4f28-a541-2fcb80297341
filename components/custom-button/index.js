Component({
  /**
   * 组件的属性列表
   */
  properties: {
    // 按钮类型
    type: {
      type: String,
      value: 'default' // default, primary, secondary, danger
    },
    // 按钮大小
    size: {
      type: String,
      value: 'normal' // small, normal, large
    },
    // 是否为圆角按钮
    circle: {
      type: Boolean,
      value: false
    },
    // 是否禁用
    disabled: {
      type: Boolean,
      value: false
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    
  },

  /**
   * 组件的方法列表
   */
  methods: {
    handleTap() {
      if (!this.properties.disabled) {
        this.triggerEvent('click');
      }
    }
  }
}) 