/* PrismJS 1.15.0
https://prismjs.com/download.html#themes=prism&languages=markup+css+clike+javascript+basic+markup-templating+go+java+json+php+sql+python+typescript */
/**
 * prism.js default theme for JavaScript, CSS and HTML
 * Based on dabblet (http://dabblet.com)
 * <AUTHOR>
 */

 .wemark_inline_code_comment,
 .wemark_inline_code_prolog,
 .wemark_inline_code_doctype,
 .wemark_inline_code_cdata {
	 color: slategray;
 }

 .wemark_inline_code_punctuation,
 .wemark_inline_code_interpolation-punctuation {
	 color: #999;
 }

 .wemark_inline_code_namespace {
	 opacity: .7;
 }

 .wemark_inline_code_property,
 .wemark_inline_code_tag,
 .wemark_inline_code_boolean,
 .wemark_inline_code_number,
 .wemark_inline_code_constant,
 .wemark_inline_code_symbol,
 .wemark_inline_code_deleted {
	 color: #905;
 }

 .wemark_inline_code_selector,
 .wemark_inline_code_attr-name,
 .wemark_inline_code_string,
 .wemark_inline_code_char,
 .wemark_inline_code_builtin,
 .wemark_inline_code_inserted {
	 color: #690;
 }

 .wemark_inline_code_operator,
 .wemark_inline_code_entity,
 .wemark_inline_code_url,
 .language-css .wemark_inline_code_string,
 .style .wemark_inline_code_string {
	 color: #9a6e3a;
	 background: hsla(0, 0%, 100%, .5);
 }

 .wemark_inline_code_atrule,
 .wemark_inline_code_attr-value,
 .wemark_inline_code_keyword {
	 color: #07a;
 }

 .wemark_inline_code_function,
 .wemark_inline_code_class-name {
	 color: #DD4A68;
 }

 .wemark_inline_code_regex,
 .wemark_inline_code_important,
 .wemark_inline_code_variable,
 .wemark_inline_code_interpolation {
	 color: #e90;
 }

 .wemark_inline_code_important,
 .wemark_inline_code_bold {
	 font-weight: bold;
 }
 .wemark_inline_code_italic {
	 font-style: italic;
 }

 .wemark_inline_code_entity {
	 cursor: help;
 }
