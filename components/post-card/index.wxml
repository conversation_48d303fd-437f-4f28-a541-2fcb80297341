<view class="post-card">
  <!-- 用户信息栏 -->
  <view class="post-header">
    <view class="user-info-container">
      <view class="avatar-container" bindtap="onTapAvatar">
        <image class="avatar" src="{{post.user.avatar_url || post.user_avatar || '/images/default-avatar.png'}}" mode="aspectFill"></image>
      </view>
      <view class="user-details">
        <view class="username" bindtap="onTapAvatar">{{post.user.nickname || post.user.fullname || post.user.username || post.username || '用户名'}}</view>
        <view class="post-time">{{formattedTime || '刚刚'}}</view>
      </view>
    </view>
    <view class="follow-container" wx:if="{{!post.user.is_current_user}}">
      <button class="follow-btn {{post.user.is_following ? 'following' : ''}}" bindtap="onTapFollow">
        {{post.user.is_following ? '已关注' : '关注'}}
      </button>
    </view>
    <view class="more-actions" bindtap="onTapMore" wx:else>
      <view class="more-icon">•••</view>
    </view>
  </view>

  <!-- 内容区域 -->
  <view class="post-content" bindtap="onTapPost">
    <view class="post-title" wx:if="{{post.title}}">{{post.title}}</view>
    <view class="post-text {{showFullContent ? 'expanded' : 'collapsed'}}">{{post.content || ''}}</view>
  </view>

  <!-- 训练统计 -->
  <view class="workout-stats" wx:if="{{post.related_workout_detail}}">
    <view class="stat-item">
      <text class="stat-value">{{post.related_workout_detail.actual_duration || 0}}</text>
      <text class="stat-label">分钟</text>
    </view>
    <view class="stat-item">
      <text class="stat-value">{{workoutTotalVolume}}</text>
      <text class="stat-label">kg</text>
    </view>
    <view class="stat-item">
      <text class="stat-value">{{post.related_workout_detail.workout_exercises.length || 0}}</text>
      <text class="stat-label">个动作</text>
    </view>
  </view>

  <!-- 媒体滑动区 -->
  <view class="media-container" wx:if="{{hasMediaContent}}">
    <swiper class="media-swiper" indicator-dots="{{true}}" indicator-color="rgba(255,255,255,0.5)" indicator-active-color="#fff">
      <!-- 图片页面 -->
      <block wx:for="{{post.images}}" wx:key="index">
        <swiper-item>
          <view class="media-item">
            <image class="media-image" src="{{item.url}}" mode="aspectFill" bindtap="onPreviewImage" data-index="{{index}}"></image>
          </view>
        </swiper-item>
      </block>
      
      <!-- 训练详情页面 -->
      <swiper-item wx:if="{{post.related_workout_detail && post.related_workout_detail.workout_exercises.length > 0}}">
        <view class="media-item workout-detail">
          <view class="workout-title">训练详情</view>
          <view class="exercise-list">
            <block wx:for="{{displayExercises}}" wx:key="index">
              <view class="exercise-item">
                <image class="exercise-image" src="{{item.exercise_image || '/images/default-exercise.png'}}" mode="aspectFill"></image>
                <view class="exercise-info">
                  <text class="exercise-sets">{{item.sets}}组</text>
                  <text class="exercise-name">{{item.exercise_name}}</text>
                </view>
              </view>
            </block>
          </view>
          <view class="view-more" wx:if="{{post.related_workout_detail.workout_exercises.length > 5}}" bindtap="onTapPost">
            查看更多
          </view>
        </view>
      </swiper-item>
    </swiper>
  </view>

  <!-- 交互按钮栏 -->
  <view class="post-actions">
    <view class="action-item {{isLiked ? 'liked' : ''}}" bindtap="onTapLike">
      <view class="action-icon">{{isLiked ? '❤️' : '🤍'}}</view>
      <view class="action-text">{{post.like_count || 0}}</view>
    </view>
    
    <view class="action-item" bindtap="onTapComment">
      <view class="action-icon">💬</view>
      <view class="action-text">{{post.comment_count || 0}}</view>
    </view>
    
    <view class="action-item" bindtap="onTapShare">
      <view class="action-icon">↗️</view>
      <view class="action-text">分享</view>
    </view>
  </view>
</view> 