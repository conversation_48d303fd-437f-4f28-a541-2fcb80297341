Component({
  /**
   * 组件的属性列表
   */
  properties: {
    // 帖子数据
    post: {
      type: Object,
      value: {}
    },
    // 是否显示完整内容
    showFullContent: {
      type: Boolean,
      value: false
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    // 是否已点赞
    isLiked: false,
    // 展示评论区
    showComments: false,
    // 格式化后的时间
    formattedTime: '',
    // 加载状态
    loading: false,
    // 是否有媒体内容
    hasMediaContent: false,
    // 显示的运动列表（最多5个）
    displayExercises: [],
    // 训练总重量
    workoutTotalVolume: 0
  },

  /**
   * 数据监听器
   */
  observers: {
    'post': function(post) {
      if (post && post.created_at) {
        this.setData({
          formattedTime: this.formatTime(post.created_at),
          isLiked: post.is_liked_by_current_user || false
        });
        
        // 处理媒体内容
        this.processMediaContent(post);
        
        // 计算训练统计
        this.calculateWorkoutStats(post);
      }
    }
  },

  /**
   * 组件的方法列表
   */
  methods: {
    /**
     * 处理媒体内容
     */
    processMediaContent(post) {
      const hasImages = post.images && post.images.length > 0;
      const hasWorkout = post.related_workout_detail && post.related_workout_detail.workout_exercises && post.related_workout_detail.workout_exercises.length > 0;
      
      this.setData({
        hasMediaContent: hasImages || hasWorkout,
        displayExercises: hasWorkout ? post.related_workout_detail.workout_exercises.slice(0, 5) : []
      });
    },

    /**
     * 计算训练统计
     */
    calculateWorkoutStats(post) {
      if (!post.related_workout_detail || !post.related_workout_detail.workout_exercises) {
        return;
      }
      
      let totalVolume = 0;
      post.related_workout_detail.workout_exercises.forEach(exercise => {
        if (exercise.set_records) {
          exercise.set_records.forEach(record => {
            if (record.completed) {
              totalVolume += (record.weight || 0) * (record.reps || 0);
            }
          });
        }
      });
      
      this.setData({
        workoutTotalVolume: Math.round(totalVolume)
      });
    },

    /**
     * 格式化时间
     */
    formatTime(timeStr) {
      const date = new Date(timeStr);
      const now = new Date();
      const diff = Math.floor((now - date) / 1000); // 秒差
      
      if (diff < 60) {
        return '刚刚';
      } else if (diff < 3600) {
        return Math.floor(diff / 60) + '分钟前';
      } else if (diff < 86400) {
        return Math.floor(diff / 3600) + '小时前';
      } else if (diff < 2592000) {
        return Math.floor(diff / 86400) + '天前';
      } else {
        const year = date.getFullYear();
        const month = date.getMonth() + 1;
        const day = date.getDate();
        return `${year}年${month}月${day}日`;
      }
    },

    /**
     * 点击用户头像
     */
    onTapAvatar() {
      const userId = this.data.post.user?.id || this.data.post.user_id;
      this.triggerEvent('tapavatar', { userId: userId });
    },

    /**
     * 点击关注按钮
     */
    onTapFollow() {
      if (this.data.loading) return;
      
      this.setData({ loading: true });
      
      const userId = this.data.post.user?.id || this.data.post.user_id;
      const isFollowing = this.data.post.user?.is_following || false;
      
      // 触发关注事件
      this.triggerEvent('follow', { 
        userId: userId,
        isFollowing: !isFollowing
      });
      
      this.setData({ loading: false });
    },

    /**
     * 点击帖子内容
     */
    onTapPost() {
      this.triggerEvent('tappost', { postId: this.data.post.id });
    },

    /**
     * 点击训练内容
     */
    onTapWorkout() {
      if (this.data.post.related_workout_id) {
        this.triggerEvent('tapworkout', { 
          workoutId: this.data.post.related_workout_id,
          postId: this.data.post.id
        });
      }
    },

    /**
     * 预览图片
     */
    onPreviewImage(e) {
      const { index } = e.currentTarget.dataset;
      const images = this.data.post.images || [];
      
      wx.previewImage({
        current: images[index].url,
        urls: images.map(img => img.url)
      });
    },

    /**
     * 点赞操作
     */
    onTapLike() {
      if (this.data.loading) return;
      
      this.setData({ loading: true });
      
      const isLiked = !this.data.isLiked;
      let likeCount = this.data.post.like_count || 0;
      
      if (isLiked) {
        likeCount += 1;
      } else {
        likeCount = Math.max(0, likeCount - 1);
      }
      
      // 更新本地状态
      this.setData({
        isLiked: isLiked,
        'post.like_count': likeCount
      });
      
      // 触发点赞事件
      this.triggerEvent('like', { 
        postId: this.data.post.id,
        isLiked: isLiked
      });
      
      this.setData({ loading: false });
    },

    /**
     * 点击评论按钮
     */
    onTapComment() {
      this.triggerEvent('tappost', { postId: this.data.post.id });
    },

    /**
     * 点击分享按钮
     */
    onTapShare() {
      this.triggerEvent('share', { postId: this.data.post.id });
    },

    /**
     * 更多操作
     */
    onTapMore() {
      const itemList = ['举报'];
      if (this.data.post.is_owner) {
        itemList.unshift('编辑', '删除');
      }
      
      wx.showActionSheet({
        itemList: itemList,
        success: (res) => {
          const index = res.tapIndex;
          if (this.data.post.is_owner) {
            if (index === 0) {
              // 编辑
              this.triggerEvent('edit', { postId: this.data.post.id });
            } else if (index === 1) {
              // 删除
              this.triggerEvent('delete', { postId: this.data.post.id });
            } else if (index === 2) {
              // 举报
              this.triggerEvent('report', { postId: this.data.post.id });
            }
          } else {
            if (index === 0) {
              // 举报
              this.triggerEvent('report', { postId: this.data.post.id });
            }
          }
        }
      });
    }
  }
}); 