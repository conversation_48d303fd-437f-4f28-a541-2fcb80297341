/* post-card 组件样式 */
.post-card {
  background: #fff;
  border-radius: 12rpx;
  margin-bottom: 20rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

/* 用户信息栏 */
.post-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx;
}

.user-info-container {
  display: flex;
  align-items: center;
  flex: 1;
}

.avatar-container {
  margin-right: 20rpx;
}

.avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
}

.user-details {
  flex: 1;
}

.username {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
}

.post-time {
  font-size: 24rpx;
  color: #999;
}

/* 关注按钮 */
.follow-container {
  margin-left: 20rpx;
}

.follow-btn {
  height: 80rpx;
  padding: 0 32rpx;
  background: #007AFF;
  color: #fff;
  border: none;
  border-radius: 40rpx;
  font-size: 28rpx;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
}

.follow-btn.following {
  background: #E5E5EA;
  color: #666;
}

.follow-btn::after {
  border: none;
}

/* 更多操作 */
.more-actions {
  padding: 10rpx;
}

.more-icon {
  font-size: 32rpx;
  color: #999;
  transform: rotate(90deg);
}

/* 内容区域 */
.post-content {
  padding: 0 24rpx 20rpx;
}

.post-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 16rpx;
  line-height: 1.4;
}

.post-text {
  font-size: 30rpx;
  color: #666;
  line-height: 1.5;
}

.post-text.collapsed {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  text-overflow: ellipsis;
}

.post-text.expanded {
  /* 显示全部内容 */
}

/* 训练统计 */
.workout-stats {
  display: flex;
  padding: 20rpx 24rpx;
  background: #F8F9FA;
  margin: 0 24rpx 20rpx;
  border-radius: 12rpx;
}

.stat-item {
  flex: 1;
  text-align: center;
}

.stat-value {
  display: block;
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
}

.stat-label {
  font-size: 24rpx;
  color: #999;
}

/* 媒体滑动区 */
.media-container {
  margin: 0 24rpx 20rpx;
}

.media-swiper {
  height: 400rpx;
  border-radius: 12rpx;
  overflow: hidden;
}

.media-item {
  width: 100%;
  height: 100%;
  position: relative;
}

.media-image {
  width: 100%;
  height: 100%;
}

/* 训练详情页面 */
.workout-detail {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 40rpx;
  display: flex;
  flex-direction: column;
}

.workout-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #fff;
  margin-bottom: 30rpx;
  text-align: center;
}

.exercise-list {
  flex: 1;
}

.exercise-item {
  display: flex;
  align-items: center;
  margin-bottom: 24rpx;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12rpx;
  padding: 20rpx;
}

.exercise-image {
  width: 60rpx;
  height: 60rpx;
  border-radius: 8rpx;
  margin-right: 20rpx;
}

.exercise-info {
  flex: 1;
}

.exercise-sets {
  font-size: 28rpx;
  font-weight: 600;
  color: #fff;
  margin-right: 16rpx;
}

.exercise-name {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.9);
}

.view-more {
  text-align: center;
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.8);
  padding: 20rpx;
  border-top: 1rpx solid rgba(255, 255, 255, 0.2);
}

/* 交互按钮栏 */
.post-actions {
  display: flex;
  align-items: center;
  padding: 20rpx 24rpx;
  border-top: 1rpx solid #F0F0F0;
}

.action-item {
  display: flex;
  align-items: center;
  margin-right: 60rpx;
  padding: 10rpx 0;
}

.action-item:last-child {
  margin-right: 0;
  margin-left: auto;
}

.action-icon {
  font-size: 40rpx;
  margin-right: 12rpx;
}

.action-text {
  font-size: 28rpx;
  color: #666;
}

.action-item.liked .action-text {
  color: #FF3B30;
}

/* 响应式调整 */
@media (max-width: 750rpx) {
  .post-header {
    padding: 20rpx;
  }
  
  .post-content {
    padding: 0 20rpx 16rpx;
  }
  
  .media-container {
    margin: 0 20rpx 16rpx;
  }
  
  .post-actions {
    padding: 16rpx 20rpx;
  }
} 