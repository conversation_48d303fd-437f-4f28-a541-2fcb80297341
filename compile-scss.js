const fs = require('fs');
const path = require('path');
const sass = require('node-sass');
const glob = require('glob');

// 将SCSS文件编译为WXSS文件
function compileSass() {
  console.log('开始编译SCSS文件为WXSS文件...');
  
  // Taro UI的样式目录
  const taroUiDir = path.resolve(__dirname, './node_modules/taro-ui/dist/style');
  
  // 处理主样式文件
  const indexScssPath = path.join(taroUiDir, 'index.scss');
  const indexWxssPath = path.join(taroUiDir, 'index.wxss');
  compileFile(indexScssPath, indexWxssPath);

  // 处理组件样式文件
  const componentGlob = path.join(taroUiDir, 'components/**/*.scss');
  
  glob.sync(componentGlob).forEach(file => {
    const wxssPath = file.replace(/\.scss$/, '.wxss');
    compileFile(file, wxssPath);
  });
  
  console.log(`编译完成，共处理了 ${glob.sync(componentGlob).length + 1} 个文件`);
}

function compileFile(srcPath, destPath) {
  try {
    const result = sass.renderSync({
      file: srcPath,
      outputStyle: 'expanded'
    });
    
    // 将编译结果写入WXSS文件
    fs.writeFileSync(destPath, result.css.toString());
    console.log(`编译成功: ${srcPath} -> ${destPath}`);
  } catch (error) {
    console.error(`编译失败: ${srcPath}`, error.message);
  }
}

// 运行编译
compileSass(); 