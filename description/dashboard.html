<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据看板 - 智能健身教练</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
            height: 100vh;
            background-color: #f6f8fa;
        }
        .status-bar {
            height: 44px;
            background-color: white;
            color: black;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 15px;
            font-size: 14px;
        }
        .tab-bar {
            height: 83px;
            background-color: white;
            border-top: 1px solid #e5e7eb;
            display: flex;
            justify-content: space-around;
            align-items: center;
            padding-bottom: 20px;
        }
        .calendar-day {
            width: 40px;
            height: 40px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
        }
        .chart-container {
            height: 200px;
            position: relative;
            overflow: hidden;
        }
        .bar {
            position: absolute;
            bottom: 0;
            width: 20px;
            border-radius: 4px 4px 0 0;
            background-color: #3b82f6;
        }
    </style>
</head>
<body class="flex flex-col">
    <!-- iOS Status Bar -->
    <div class="status-bar">
        <div>9:41</div>
        <div class="flex space-x-2">
            <i class="fas fa-signal"></i>
            <i class="fas fa-wifi"></i>
            <i class="fas fa-battery-full"></i>
        </div>
    </div>

    <!-- Header -->
    <header class="bg-white p-4 shadow-sm">
        <div class="flex justify-between items-center">
            <h1 class="text-xl font-bold">数据看板</h1>
            <button class="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center">
                <i class="fas fa-calendar-alt text-gray-600"></i>
            </button>
        </div>
    </header>

    <!-- Main Content -->
    <main class="flex-1 overflow-auto p-4">
        <!-- Weekly Calendar -->
        <div class="bg-white rounded-xl shadow-sm p-4 mb-4">
            <h2 class="text-lg font-semibold mb-3">打卡日历</h2>
            
            <div class="flex justify-between">
                <div class="calendar-day">
                    <div class="text-xs text-gray-500">一</div>
                    <div class="text-sm font-semibold">9</div>
                    <div class="w-2 h-2 bg-gray-200 rounded-full mt-1"></div>
                </div>
                <div class="calendar-day">
                    <div class="text-xs text-gray-500">二</div>
                    <div class="text-sm font-semibold">10</div>
                    <div class="w-2 h-2 bg-green-500 rounded-full mt-1"></div>
                </div>
                <div class="calendar-day">
                    <div class="text-xs text-gray-500">三</div>
                    <div class="text-sm font-semibold">11</div>
                    <div class="w-2 h-2 bg-gray-200 rounded-full mt-1"></div>
                </div>
                <div class="calendar-day">
                    <div class="text-xs text-gray-500">四</div>
                    <div class="text-sm font-semibold">12</div>
                    <div class="w-2 h-2 bg-green-500 rounded-full mt-1"></div>
                </div>
                <div class="calendar-day">
                    <div class="text-xs text-gray-500">五</div>
                    <div class="text-sm font-semibold">13</div>
                    <div class="w-2 h-2 bg-green-500 rounded-full mt-1"></div>
                </div>
                <div class="calendar-day bg-blue-50 rounded-lg">
                    <div class="text-xs text-blue-500">六</div>
                    <div class="text-sm font-semibold text-blue-500">14</div>
                    <div class="w-2 h-2 bg-blue-500 rounded-full mt-1"></div>
                </div>
                <div class="calendar-day">
                    <div class="text-xs text-gray-500">日</div>
                    <div class="text-sm font-semibold">15</div>
                    <div class="w-2 h-2 bg-gray-200 rounded-full mt-1"></div>
                </div>
            </div>
            
            <div class="mt-3 p-3 bg-blue-50 rounded-lg text-sm text-blue-700">
                <div class="font-medium">本周训练总结</div>
                <div class="text-xs mt-1">共完成4次训练，比上周提升25%，继续保持！</div>
            </div>
        </div>

        <!-- Weight Tracking -->
        <div class="bg-white rounded-xl shadow-sm p-4 mb-4">
            <div class="flex justify-between items-center mb-3">
                <h2 class="text-lg font-semibold">体重趋势</h2>
                <button class="text-sm text-blue-500">月视图</button>
            </div>
            
            <div class="chart-container">
                <div class="bar" style="height: 65%; left: 30px;"></div>
                <div class="bar" style="height: 70%; left: 70px;"></div>
                <div class="bar" style="height: 60%; left: 110px;"></div>
                <div class="bar" style="height: 62%; left: 150px;"></div>
                <div class="bar" style="height: 58%; left: 190px;"></div>
                <div class="bar" style="height: 55%; left: 230px;"></div>
                <div class="bar" style="height: 52%; left: 270px;"></div>
            </div>
            
            <div class="flex justify-between text-xs text-gray-500 mt-2">
                <div>9日</div>
                <div>10日</div>
                <div>11日</div>
                <div>12日</div>
                <div>13日</div>
                <div>14日</div>
                <div>今天</div>
            </div>
            
            <div class="mt-4 flex justify-between">
                <div>
                    <div class="text-2xl font-bold text-blue-500">68.5<span class="text-sm">kg</span></div>
                    <div class="text-xs text-gray-500">当前体重</div>
                </div>
                <div>
                    <div class="text-2xl font-bold text-green-500">-0.8<span class="text-sm">kg</span></div>
                    <div class="text-xs text-gray-500">本周变化</div>
                </div>
                <div>
                    <div class="text-2xl font-bold text-purple-500">65.0<span class="text-sm">kg</span></div>
                    <div class="text-xs text-gray-500">目标体重</div>
                </div>
            </div>
        </div>
        
        <!-- Workout Stats -->
        <div class="bg-white rounded-xl shadow-sm p-4 mb-4">
            <h2 class="text-lg font-semibold mb-3">训练数据</h2>
            
            <div class="grid grid-cols-2 gap-3">
                <div class="bg-gray-50 p-3 rounded-lg">
                    <div class="flex justify-between items-center mb-1">
                        <div class="text-sm text-gray-500">总训练时长</div>
                        <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                            <i class="fas fa-clock text-blue-500 text-sm"></i>
                        </div>
                    </div>
                    <div class="text-xl font-bold">124<span class="text-sm">分钟</span></div>
                    <div class="text-xs text-green-500">+15% 环比上周</div>
                </div>
                
                <div class="bg-gray-50 p-3 rounded-lg">
                    <div class="flex justify-between items-center mb-1">
                        <div class="text-sm text-gray-500">总消耗卡路里</div>
                        <div class="w-8 h-8 bg-red-100 rounded-full flex items-center justify-center">
                            <i class="fas fa-fire text-red-500 text-sm"></i>
                        </div>
                    </div>
                    <div class="text-xl font-bold">1,254<span class="text-sm">千卡</span></div>
                    <div class="text-xs text-green-500">+8% 环比上周</div>
                </div>
                
                <div class="bg-gray-50 p-3 rounded-lg">
                    <div class="flex justify-between items-center mb-1">
                        <div class="text-sm text-gray-500">训练次数</div>
                        <div class="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center">
                            <i class="fas fa-dumbbell text-purple-500 text-sm"></i>
                        </div>
                    </div>
                    <div class="text-xl font-bold">4<span class="text-sm">次</span></div>
                    <div class="text-xs text-green-500">+1次 环比上周</div>
                </div>
                
                <div class="bg-gray-50 p-3 rounded-lg">
                    <div class="flex justify-between items-center mb-1">
                        <div class="text-sm text-gray-500">平均运动强度</div>
                        <div class="w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center">
                            <i class="fas fa-bolt text-yellow-500 text-sm"></i>
                        </div>
                    </div>
                    <div class="text-xl font-bold">中等</div>
                    <div class="text-xs text-gray-500">与上周持平</div>
                </div>
            </div>
        </div>
        
        <!-- Body Measurements -->
        <div class="bg-white rounded-xl shadow-sm p-4 mb-4">
            <div class="flex justify-between items-center mb-3">
                <h2 class="text-lg font-semibold">身体数据</h2>
                <button class="text-sm text-blue-500">记录</button>
            </div>
            
            <div class="space-y-4">
                <div>
                    <div class="flex justify-between items-center mb-1">
                        <span class="text-sm">体脂率</span>
                        <span class="text-sm text-gray-500">22.3% <span class="text-green-500">↓0.7%</span></span>
                    </div>
                    <div class="h-2 bg-gray-100 rounded-full overflow-hidden">
                        <div class="h-full bg-green-500" style="width: 65%"></div>
                    </div>
                </div>
                
                <div>
                    <div class="flex justify-between items-center mb-1">
                        <span class="text-sm">肌肉量</span>
                        <span class="text-sm text-gray-500">26.8kg <span class="text-green-500">↑0.3kg</span></span>
                    </div>
                    <div class="h-2 bg-gray-100 rounded-full overflow-hidden">
                        <div class="h-full bg-blue-500" style="width: 70%"></div>
                    </div>
                </div>
                
                <div>
                    <div class="flex justify-between items-center mb-1">
                        <span class="text-sm">BMI指数</span>
                        <span class="text-sm text-gray-500">24.1 <span class="text-green-500">↓0.2</span></span>
                    </div>
                    <div class="h-2 bg-gray-100 rounded-full overflow-hidden">
                        <div class="h-full bg-yellow-500" style="width: 80%"></div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Tab Bar -->
    <div class="tab-bar">
        <div class="text-center text-gray-400">
            <i class="fas fa-home text-xl"></i>
            <div class="text-xs mt-1">首页</div>
        </div>
        <div class="text-center text-gray-400">
            <i class="fas fa-chart-line text-xl"></i>
            <div class="text-xs mt-1">数据</div>
        </div>
        <div class="text-center text-blue-500">
            <i class="fas fa-robot text-xl"></i>
            <div class="text-xs mt-1">AI助手</div>
        </div>
        <div class="text-center text-gray-400">
            <i class="fas fa-users text-xl"></i>
            <div class="text-xs mt-1">社区</div>
        </div>
        <div class="text-center text-gray-400">
            <i class="fas fa-user text-xl"></i>
            <div class="text-xs mt-1">我的</div>
        </div>
    </div>
</body>
</html> 