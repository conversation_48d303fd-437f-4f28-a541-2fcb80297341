# 智能健身教练小程序需求文档

## 一、项目概述

本项目旨在开发一款基于AI和大数据分析的线上智能健身教练小程序，提供个性化的运动和饮食方案，帮助用户实现健康目标。主要面向18-35岁对健康管理有需求的人群，解决"不会练"、"难坚持"、"场景受限"三大核心痛点。

## 二、技术架构

### 前端技术栈
- 框架：uni-app（支持多端发布，包括微信小程序、APP等）
- UI框架：uView
- 状态管理：Vuex
- 网络请求：uni.request / axios封装
- 本地存储：uni.storage

### 后端技术栈
- 框架：Python + FastAPI
- 数据库：PostgreSQL（用户数据和结构化数据）+ MongoDB（非结构化数据和日志）
- 缓存：Redis
- AI服务：
  - 图像识别：TensorFlow/PyTorch
  - 自然语言处理：OpenAI API / HuggingFace Transformers
  - 推荐系统：scikit-learn / LightGBM
- 部署：Docker + Kubernetes
- 服务器：阿里云/腾讯云

## 三、功能模块与需求

### 1. 用户模块

#### 1.1 前端需求
- **登录/注册界面**
  - 手机号验证码登录
  - 微信一键登录
  - 用户协议与隐私政策展示
  
- **用户信息填写界面**
  - 基础信息采集：性别、年龄、身高、体重
  - 身体类型选择
  - 健身经验等级选择
  - 健身目标设定（增肌/减脂/保持健康等）
  - 身体部位选择（重点锻炼区域）

- **个人设置界面**
  - 个人资料编辑
  - 账号安全设置
  - 消息通知设置
  - 隐私设置
  - 主题切换
  - 清除缓存
  - 帮助与反馈
  - 退出登录

#### 1.2 后端需求
- **数据库设计**
  ```
  用户表(users):
    - user_id: 主键
    - phone: 手机号
    - openid: 微信openid
    - nickname: 昵称
    - gender: 性别
    - birthday: 出生日期
    - height: 身高(cm)
    - weight: 体重(kg)
    - body_type: 身体类型
    - experience_level: 健身经验
    - fitness_goal: 健身目标
    - created_at: 创建时间
    - updated_at: 更新时间
  ```

- **API接口**
  - `/api/auth/login` - 登录接口
  - `/api/auth/register` - 注册接口
  - `/api/auth/wxlogin` - 微信一键登录
  - `/api/user/profile` - 获取用户信息
  - `/api/user/update` - 更新用户信息
  - `/api/user/settings` - 获取/更新用户设置

### 2. 健身训练模块

#### 2.1 前端需求
- **首页/今日计划界面**
  - 今日训练计划展示
  - 训练进度环形图
  - 计划完成度统计
  - 今日饮食计划概览
  - AI教练建议板块

- **训练详情界面**
  - 训练课程详情
  - 动作列表与详细说明
  - 组数/次数/间歇时间设置
  - 训练记录功能
  - 训练提示与注意事项
  - 无器械替代方案

- **训练执行界面**
  - 倒计时/计时器
  - 动作示范视频/图片
  - 语音指导
  - 实时记录功能

#### 2.2 后端需求
- **数据库设计**
  ```
  训练计划表(training_plans):
    - plan_id: 主键
    - code: 计划代码(唯一)
    - name: 计划名称
    - description: 计划描述
    - tips: 计划提示说明
    - level: 难度级别(1-初级,2-中级,3-高级)
    - scene: 训练场景(HOME/GYM/ALL)
    - weeks: 训练周期(周数)
    - training_frequency: 训练频率(每周次数)
    - status: 状态(ENABLED/DISABLED)
    - is_free: 是否免费
    - cover_img: 封面图片
    - tb_img: 缩略图
    - onboard_img: 引导图片
    - created_at: 创建时间
    - updated_at: 更新时间
    
  训练课程表(training_courses):
    - course_id: 主键
    - plan_id: 外键(计划ID)
    - code: 课程代码
    - name: 课程名称
    - description: 课程描述
    - type: 课程类型
    - scene: 训练场景
    - status: 状态
    - cover_img: 封面图片
    - tb_img: 缩略图
    - movement_count: 动作数量
    - group_count: 组数
    - estimated_duration: 预计时长(分钟)
    - created_at: 创建时间
    - updated_at: 更新时间
    
  训练动作表(exercises):
    - id: 主键
    - code: 动作代码(唯一)
    - name: 动作名称
    - en_name: 英文名称
    - level: 难度级别
    - priority: 排序优先级
    - equipment_ids: 器械ID数组
    - body_part_ids: 身体部位ID数组
    - muscle_main_ids: 主要肌肉ID数组
    - muscle_minor_ids: 次要肌肉ID数组
    - type: 动作类型(1-普通,2-热身,3-复合)
    - description: 动作描述(JSONB)
    - is_free: 是否免费
    - status: 状态
    - created_at: 创建时间
    - updated_at: 更新时间
    
  训练动作媒体表(exercise_media):
    - id: 主键
    - exercise_id: 外键(动作ID)
    - type: 媒体类型(VIDEO/GIF/IMAGE/3D)
    - label: 标签(前/侧/后)
    - url: 媒体URL
    - created_at: 创建时间
    
  训练动作关联表(training_actions):
    - action_id: 主键
    - course_id: 外键(课程ID)
    - sequence: 顺序号
    - type: 动作类型(1-单一动作,3-复合动作)
    - unit: 单位(kg/次/秒)
    - rest_time: 休息时间(秒)
    - is_superset: 是否超级组
    - created_at: 创建时间
    - updated_at: 更新时间
    
  训练动作映射表(action_exercise_map):
    - id: 主键
    - action_id: 外键(动作关联ID)
    - exercise_id: 外键(动作表ID)
    - exercise_code: 动作代码
    - sequence: 顺序(用于复合动作)
    - created_at: 创建时间
    
  训练组数表(training_sets):
    - set_id: 主键
    - action_id: 外键(动作关联ID)
    - set_number: 组号
    - created_at: 创建时间
    
  训练组数详情表(set_details):
    - id: 主键
    - set_id: 外键(组ID)
    - exercise_code: 动作代码
    - reps: 次数
    - weight: 重量
    - duration: 时长(秒)
    - created_at: 创建时间
  ```

- **API接口**
  - `/api/training/plan` - 获取/创建训练计划
  - `/api/training/today` - 获取今日训练
  - `/api/training/exercise/{id}` - 获取训练动作详情
  - `/api/training/record` - 记录训练数据
  - `/api/training/history` - 获取历史训练记录
  - `/api/training/recommendations` - 获取推荐训练

### 3. 饮食记录模块

#### 3.1 前端需求
- **饮食记录界面**
  - 今日摄入营养概览
  - 早/中/晚餐记录区域
  - 拍照识别食物功能
  - 手动添加食物功能
  - 常用食物快速添加
  - 饮食评价与建议

#### 3.2 后端需求
- **数据库设计**
  ```
  食物库表(foods):
    - id: 主键
    - name: 食物名称
    - code: 食物编码(唯一)
    - category: 食物分类
    - food_type: 食物类型
    - calories: 卡路里
    - protein: 蛋白质(g)
    - carbs: 碳水化合物(g)
    - fat: 脂肪(g)
    - thumb_image_url: 缩略图链接
    - large_image_url: 大图链接
    - is_liquid: 是否为液体
    - health_light: 健康指示灯
    - lights: 各指标评级(JSONB)
    - warnings: 警告信息(JSONB)
    - energy_fractions: 能量分配比例(JSONB)
    - updated_at: 更新时间
    
  食物记录表(diet_records):
    - record_id: 主键
    - user_id: 外键(用户ID)
    - food_id: 外键(食物ID)
    - meal_type: 餐食类型(早餐/午餐/晚餐/加餐)
    - serving_count: 份数
    - record_date: 记录日期
    - image_url: 用户上传图片
    - created_at: 创建时间
    - updated_at: 更新时间
    - custom_food_id: 自定义食物ID(可为空)
    - notes: 备注信息
    
  饮食计划表(diet_plans):
    - plan_id: 主键
    - user_id: 外键(用户ID)
    - calories_goal: 卡路里目标
    - protein_goal: 蛋白质目标
    - carbs_goal: 碳水目标
    - fat_goal: 脂肪目标
    - is_active: 是否激活
  ```

- **API接口**
  - `/api/diet/foods` - 获取食物库列表
  - `/api/diet/record` - 记录饮食数据
  - `/api/diet/daily` - 获取每日饮食记录
  - `/api/diet/recognize` - 食物图像识别
  - `/api/diet/recommendations` - 饮食推荐
  - `/api/diet/plan` - 获取/更新饮食计划

- **AI服务**
  - 食物图像识别模型
  - 营养成分分析算法
  - 卡路里计算服务

### 4. 数据分析模块

#### 4.1 前端需求
- **数据看板界面**
  - 健身打卡日历
  - 训练数据趋势图
  - 身体数据变化曲线
  - 训练频次统计
  - 饮食摄入分析
  - 健身目标完成度

#### 4.2 后端需求
- **数据库设计**
  ```
  身体数据表(body_metrics):
    - metric_id: 主键
    - user_id: 外键(用户ID)
    - weight: 体重
    - body_fat: 体脂率
    - muscle_mass: 肌肉量
    - bmi: BMI指数
    - record_date: 记录日期
    
  数据统计表(user_statistics):
    - stats_id: 主键
    - user_id: 外键(用户ID)
    - total_workouts: 总训练次数
    - total_duration: 总训练时长
    - total_calories: 总消耗卡路里
    - streak_days: 连续训练天数
    - last_update: 最后更新时间
  ```

- **API接口**
  - `/api/stats/summary` - 获取数据总览
  - `/api/stats/body` - 获取身体数据统计
  - `/api/stats/training` - 获取训练数据统计
  - `/api/stats/diet` - 获取饮食数据统计
  - `/api/stats/progress` - 获取进度数据

### 5. 社区互动模块

#### 5.1 前端需求
- **社区界面**
  - 内容分类标签
  - 用户动态列表
  - 热门话题/挑战
  - 点赞/评论功能
  - 内容分享功能
  - 发布动态入口

- **发布界面**
  - 文字输入区
  - 图片/视频上传
  - 话题/标签选择
  - 位置标记
  - 分享设置

#### 5.2 后端需求
- **数据库设计**
  ```
  动态表(posts):
    - post_id: 主键
    - user_id: 外键(用户ID)
    - content: 内容
    - images: 图片链接数组
    - video_url: 视频链接
    - topic_tags: 话题标签
    - location: 位置
    - likes_count: 点赞数
    - comments_count: 评论数
    - created_at: 创建时间
    
  评论表(comments):
    - comment_id: 主键
    - post_id: 外键(动态ID)
    - user_id: 外键(用户ID)
    - content: 内容
    - parent_id: 父评论ID(回复功能)
    - created_at: 创建时间
    
  点赞表(likes):
    - like_id: 主键
    - post_id: 外键(动态ID)
    - user_id: 外键(用户ID)
    - created_at: 创建时间
    
  话题表(topics):
    - topic_id: 主键
    - name: 话题名称
    - description: 描述
    - posts_count: 动态数
    - followers_count: 关注人数
    - is_challenge: 是否为挑战
    - challenge_rules: 挑战规则
    - start_date: 开始日期
    - end_date: 结束日期
  ```

- **API接口**
  - `/api/community/posts` - 获取动态列表
  - `/api/community/post/{id}` - 获取动态详情
  - `/api/community/publish` - 发布动态
  - `/api/community/comment` - 发表评论
  - `/api/community/like` - 点赞操作
  - `/api/community/topics` - 获取话题列表
  - `/api/community/challenges` - 获取挑战列表

### 6. AI助手模块

#### 6.1 前端需求
- **AI助手界面**
  - 多模态输入支持(文本、照片、语音)
  - 对话式交互界面
  - 内容多样化展示(文本、列表、图表)
  - 快速问题推荐
  - 历史对话记录

#### 6.2 后端需求
- **数据库设计**
  ```
  对话记录表(ai_chats):
    - chat_id: 主键
    - user_id: 外键(用户ID)
    - session_id: 会话ID
    - query: 用户提问
    - query_type: 提问类型(文本/图片/语音)
    - response: AI回答
    - created_at: 创建时间
    
  知识库表(knowledge_base):
    - entry_id: 主键
    - category: 类别(训练/饮食/健康)
    - question: 问题
    - answer: 回答
    - keywords: 关键词
    - popularity: 热门度
  ```

- **API接口**
  - `/api/ai/chat` - 文本对话接口
  - `/api/ai/analyze-image` - 图片分析接口
  - `/api/ai/speech-to-text` - 语音识别接口
  - `/api/ai/history` - 获取对话历史
  - `/api/ai/suggestions` - 获取问题建议

- **AI服务**
  - 自然语言处理模型
  - 图像识别模型(食物/器械)
  - 语音识别服务
  - 健身知识图谱
  - 个性化推荐引擎

## 四、技术实现要点

### 1. 前端实现要点（uni-app）

#### 1.1 项目结构
```
|- src
   |- pages
      |- login            // 登录相关页面
      |- user             // 用户相关页面
      |- training         // 训练相关页面
      |- diet             // 饮食相关页面
      |- stats            // 数据统计页面
      |- community        // 社区相关页面
      |- ai               // AI助手相关页面
   |- components          // 通用组件
   |- store               // Vuex状态管理
   |- utils               // 工具函数
   |- api                 // API请求封装
   |- static              // 静态资源
   |- uni_modules         // uni扩展模块
```

#### 1.2 关键功能实现
- **响应式布局**：使用rpx单位和flex布局，适配不同设备
- **离线数据处理**：本地存储训练记录，网络恢复后同步
- **图片处理**：本地压缩后再上传，减少流量消耗
- **动画效果**：使用CSS3和uni.createAnimation优化用户体验
- **表单验证**：前端数据校验，提高用户体验
- **状态管理**：使用Vuex管理全局状态，如用户信息、训练计划等
- **分包加载**：优化小程序包体积和加载速度

### 2. 后端实现要点（Python + FastAPI）

#### 2.1 项目结构
```
|- app
   |- main.py            // 应用入口
   |- db                 // 数据库模型和连接
   |- api                // API路由
      |- auth            // 认证相关API
      |- user            // 用户相关API
      |- training        // 训练相关API
      |- diet            // 饮食相关API
      |- stats           // 数据统计API
      |- community       // 社区相关API
      |- ai              // AI服务API
   |- core               // 核心配置
   |- models             // 数据模型
   |- schemas            // Pydantic模型
   |- services           // 业务逻辑
   |- utils              // 工具函数
   |- ml                 // 机器学习模型
      |- image_recognition  // 图像识别模型
      |- nlp                // 自然语言处理
      |- recommendation     // 推荐系统
```

#### 2.2 关键功能实现
- **异步处理**：使用FastAPI的异步特性处理高并发请求
- **数据验证**：使用Pydantic进行请求和响应数据验证
- **数据库ORM**：使用SQLAlchemy进行数据库操作
- **缓存优化**：使用Redis缓存热点数据，如训练计划、食物库等
- **图像识别**：使用TensorFlow/PyTorch实现食物和健身器械识别
- **对话系统**：集成OpenAI API或本地部署开源LLM实现智能问答
- **个性化推荐**：基于用户数据和行为，推荐训练计划和饮食方案
- **安全性**：JWT认证、HTTPS加密、数据脱敏等安全措施
- **日志系统**：记录用户请求和系统运行日志，便于问题排查
- **定时任务**：使用Celery处理后台任务，如数据分析、推送提醒等

## 五、非功能性需求

### 1. 性能需求
- 页面加载时间<3秒
- API响应时间<500ms
- 支持1000+并发用户访问
- 图片识别处理时间<2秒

### 2. 安全性需求
- 用户数据加密存储
- 完善的权限管理系统
- 防SQL注入、XSS攻击等安全措施
- 合规的隐私保护政策

### 3. 可用性需求
- 系统可用性>99.9%
- 友好的错误提示
- 完整的用户引导
- 支持离线模式下的基本功能

### 4. 可扩展性需求
- 模块化设计，支持功能扩展
- 可水平扩展的服务架构
- 支持第三方服务集成
- API版本管理支持

## 六、测试与验收标准

### 1. 单元测试
- 前端组件测试覆盖率>80%
- 后端API测试覆盖率>90%
- 数据模型验证测试

### 2. 集成测试
- 各模块间接口测试
- 前后端交互测试
- 第三方服务集成测试

### 3. 系统测试
- 功能测试
- 性能测试
- 安全测试
- 兼容性测试

### 4. 验收标准
- 符合需求文档的所有功能点
- 通过所有测试用例
- 用户体验评分>4.5/5
- 系统性能符合要求

## 七、迭代计划

### 第一阶段（MVP）
- 基础用户功能
- 核心训练功能
- 基础AI助手功能
- 简单数据统计

### 第二阶段
- 饮食记录与分析
- 高级训练功能
- 增强的AI助手
- 完整数据看板

### 第三阶段
- 社区功能
- 高级数据分析
- 个性化推荐系统
- 第三方服务集成

## 八、附录

### 1. API接口规范
- RESTful API设计
- JSON格式请求与响应
- HTTP状态码约定
- 错误处理规范

### 2. 数据安全规范
- 敏感数据加密
- 用户授权流程
- 数据备份策略
- 隐私保护措施

这份需求文档提供了智能健身教练小程序的全面技术需求，包括前端（uni-app）和后端（Python + FastAPI）的详细实现要点。开发团队应基于此文档进行开发，同时在实际开发过程中可以不断完善和细化具体需求。
