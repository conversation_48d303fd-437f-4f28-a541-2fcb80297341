CREATE TABLE users (
    user_id BIGINT PRIMARY KEY AUTO_INCREMENT,
    phone VARCHAR(20) UNIQUE NOT NULL,
    openid VARCHAR(50) UNIQUE NOT NULL,
    nickname VA<PERSON>HAR(50),
    gender TINYINT COMMENT '0: 未知, 1: 男, 2: 女',
    birthday DATE,
    height DECIMAL(5,2) COMMENT '身高(cm)',
    weight DECIMAL(5,2) COMMENT '体重(kg)',
    body_type VARCHAR(20) COMMENT '身体类型',
    activity_level VARCHAR(20) COMMENT '活动水平',
    fitness_goal VARCHAR(50) COMMENT '健身目标',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_phone (phone),
    INDEX idx_openid (openid)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户表'; 