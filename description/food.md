GET /api/diet/foods
- 参数：
  - page: 页码
  - size: 每页数量
  - category: 食物分类
  - keyword: 搜索关键词
  - sort: 排序方式(calories/protein/popularity)
- 返回：
  - 食物列表
  - 总数量
  - 分页信息

GET /api/diet/foods/{id}
- 参数：
  - id: 食物ID
- 返回：
  - 食物详细信息(包含营养成分完整数据)

POST /api/diet/record
- 请求体：
  - food_id: 食物ID
  - meal_type: 餐食类型(早餐/午餐/晚餐/加餐)
  - serving_count: 份数
  - record_date: 记录日期(默认今天)
  - image_url: 用户上传图片(可选)
- 返回：
  - 记录ID
  - 状态信息

PUT /api/diet/record/{record_id}
- 请求体：
  - serving_count: 份数(可选)
  - meal_type: 餐食类型(可选)
- 返回：
  - 更新状态

DELETE /api/diet/record/{record_id}
- 返回：
  - 删除状态

GET /api/diet/daily/{date}
- 参数：
  - date: 日期(YYYY-MM-DD)
  - meal_type: 餐食类型(可选)
- 返回：
  - 指定日期的所有食物记录
  - 营养摄入总结(卡路里、蛋白质、碳水、脂肪)
  - 与目标差距对比

GET /api/diet/plan
- 返回：
  - 当前激活的饮食计划详情
  - 目标营养素摄入量

POST /api/diet/plan
- 请求体：
  - calories_goal: 卡路里目标
  - protein_goal: 蛋白质目标
  - carbs_goal: 碳水目标
  - fat_goal: 脂肪目标
- 返回：
  - 计划ID
  - 创建状态

PUT /api/diet/plan/{plan_id}
- 请求体：
  - calories_goal: 卡路里目标(可选)
  - protein_goal: 蛋白质目标(可选)
  - carbs_goal: 碳水目标(可选)
  - fat_goal: 脂肪目标(可选)
  - is_active: 是否激活(可选)
- 返回：
  - 更新状态

GET /api/diet/statistics
- 参数：
  - start_date: 开始日期
  - end_date: 结束日期
- 返回：
  - 日均摄入量统计
  - 营养素摄入趋势
  - 餐食类型分布

POST /api/diet/custom-foods
- 请求体：
  - name: 食物名称
  - calories: 卡路里
  - protein: 蛋白质
  - carbs: 碳水
  - fat: 脂肪
  - serving_size: 份量
- 返回：
  - 自定义食物ID
  - 创建状态