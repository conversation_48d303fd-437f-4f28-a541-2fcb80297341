<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>欢迎 - 智能健身教练</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
            height: 100vh;
            background: linear-gradient(135deg, #4299e1, #3182ce);
        }
        .status-bar {
            height: 44px;
            background-color: transparent;
            color: white;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 15px;
            font-size: 14px;
        }
    </style>
</head>
<body class="flex flex-col">
    <!-- iOS Status Bar -->
    <div class="status-bar">
        <div>9:41</div>
        <div class="flex space-x-2">
            <i class="fas fa-signal"></i>
            <i class="fas fa-wifi"></i>
            <i class="fas fa-battery-full"></i>
        </div>
    </div>

    <div class="flex-1 flex flex-col items-center justify-center p-8">
        <div class="bg-white p-6 rounded-3xl shadow-lg w-full max-w-md">
            <div class="text-center mb-8">
                <div class="w-24 h-24 bg-blue-500 rounded-full mx-auto mb-4 flex items-center justify-center">
                    <i class="fas fa-dumbbell text-white text-4xl"></i>
                </div>
                <h1 class="text-3xl font-bold text-gray-800">SlashFit</h1>
                <p class="text-gray-600 mt-2">专属你的智能健身教练</p>
            </div>

            <div class="space-y-4">
                <button class="w-full bg-blue-600 text-white py-3 rounded-xl font-medium hover:bg-blue-700 transition">
                    手机号登录
                </button>
                <button class="w-full bg-white border border-gray-300 text-gray-700 py-3 rounded-xl font-medium hover:bg-gray-50 transition flex items-center justify-center">
                    <i class="fab fa-weixin text-green-500 mr-2"></i> 微信一键登录
                </button>
            </div>

            <div class="mt-8 text-center">
                <p class="text-sm text-gray-500">
                    还没有账号？<a href="#" class="text-blue-600 font-medium">立即注册</a>
                </p>
            </div>
        </div>

        <div class="mt-8 text-white text-sm text-center">
            <p>登录即表示您同意我们的<a href="#" class="underline">服务条款</a>和<a href="#" class="underline">隐私政策</a></p>
        </div>
    </div>
</body>
</html> 