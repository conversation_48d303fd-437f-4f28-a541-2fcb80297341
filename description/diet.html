<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>饮食记录 - 智能健身教练</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
            height: 100vh;
            background-color: #f6f8fa;
        }
        .status-bar {
            height: 44px;
            background-color: white;
            color: black;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 15px;
            font-size: 14px;
        }
        .tab-bar {
            height: 83px;
            background-color: white;
            border-top: 1px solid #e5e7eb;
            display: flex;
            justify-content: space-around;
            align-items: center;
            padding-bottom: 20px;
        }
        .nutrient-bar {
            height: 8px;
            background-color: #e5e7eb;
            border-radius: 4px;
            overflow: hidden;
            position: relative;
        }
        .nutrient-progress {
            height: 100%;
            position: absolute;
            border-radius: 4px;
        }
    </style>
</head>
<body class="flex flex-col">
    <!-- iOS Status Bar -->
    <div class="status-bar">
        <div>9:41</div>
        <div class="flex space-x-2">
            <i class="fas fa-signal"></i>
            <i class="fas fa-wifi"></i>
            <i class="fas fa-battery-full"></i>
        </div>
    </div>

    <!-- Header -->
    <header class="bg-white p-4 shadow-sm">
        <div class="flex items-center">
            <button class="mr-4">
                <i class="fas fa-arrow-left text-gray-600"></i>
            </button>
            <h1 class="text-xl font-bold">饮食记录</h1>
        </div>
    </header>

    <!-- Main Content -->
    <main class="flex-1 overflow-auto p-4">
        <!-- Daily Summary -->
        <div class="bg-white rounded-xl shadow-sm p-4 mb-4">
            <h2 class="text-lg font-semibold mb-2">今日摄入概览</h2>
            <div class="text-center mb-3">
                <div class="text-2xl font-bold">1254 / 1800</div>
                <div class="text-sm text-gray-500">已摄入卡路里 / 目标</div>
            </div>
            
            <div class="space-y-4">
                <div>
                    <div class="flex justify-between items-center mb-1">
                        <span class="text-sm font-medium">蛋白质</span>
                        <span class="text-sm text-gray-500">68g / 120g</span>
                    </div>
                    <div class="nutrient-bar">
                        <div class="nutrient-progress bg-red-500" style="width: 57%"></div>
                    </div>
                </div>
                
                <div>
                    <div class="flex justify-between items-center mb-1">
                        <span class="text-sm font-medium">碳水化合物</span>
                        <span class="text-sm text-gray-500">145g / 200g</span>
                    </div>
                    <div class="nutrient-bar">
                        <div class="nutrient-progress bg-yellow-500" style="width: 72%"></div>
                    </div>
                </div>
                
                <div>
                    <div class="flex justify-between items-center mb-1">
                        <span class="text-sm font-medium">脂肪</span>
                        <span class="text-sm text-gray-500">40g / 60g</span>
                    </div>
                    <div class="nutrient-bar">
                        <div class="nutrient-progress bg-blue-500" style="width: 67%"></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Meal Record -->
        <div class="flex justify-between items-center mb-4">
            <h2 class="text-lg font-semibold">食物记录</h2>
            <div class="text-sm text-gray-500">2023年10月15日</div>
        </div>
        
        <!-- Breakfast -->
        <div class="bg-white rounded-xl shadow-sm p-4 mb-4">
            <div class="flex justify-between items-center mb-3">
                <div class="flex items-center">
                    <div class="w-10 h-10 bg-yellow-100 rounded-full flex items-center justify-center mr-3">
                        <i class="fas fa-sun text-yellow-500"></i>
                    </div>
                    <h3 class="font-medium">早餐</h3>
                </div>
                <button class="text-blue-500">
                    <i class="fas fa-plus-circle"></i> 添加
                </button>
            </div>
            
            <div class="space-y-3">
                <div class="flex justify-between items-center py-2 border-b border-gray-100">
                    <div class="flex items-center">
                        <div class="w-12 h-12 bg-gray-100 rounded-lg overflow-hidden mr-3">
                            <img src="https://images.unsplash.com/photo-1525351484163-7529414344d8?ixlib=rb-1.2.1&auto=format&fit=crop&w=200&q=80" alt="全麦面包" class="w-full h-full object-cover">
                        </div>
                        <div>
                            <div class="font-medium">全麦面包</div>
                            <div class="text-xs text-gray-500">2片</div>
                        </div>
                    </div>
                    <div class="text-right">
                        <div class="font-medium">180</div>
                        <div class="text-xs text-gray-500">卡路里</div>
                    </div>
                </div>
                
                <div class="flex justify-between items-center py-2 border-b border-gray-100">
                    <div class="flex items-center">
                        <div class="w-12 h-12 bg-gray-100 rounded-lg overflow-hidden mr-3">
                            <img src="https://images.unsplash.com/photo-1607532941433-304659e8198a?ixlib=rb-1.2.1&auto=format&fit=crop&w=200&q=80" alt="鸡蛋" class="w-full h-full object-cover">
                        </div>
                        <div>
                            <div class="font-medium">水煮鸡蛋</div>
                            <div class="text-xs text-gray-500">2个</div>
                        </div>
                    </div>
                    <div class="text-right">
                        <div class="font-medium">150</div>
                        <div class="text-xs text-gray-500">卡路里</div>
                    </div>
                </div>
                
                <div class="flex justify-between items-center py-2">
                    <div class="flex items-center">
                        <div class="w-12 h-12 bg-gray-100 rounded-lg overflow-hidden mr-3">
                            <img src="https://images.unsplash.com/photo-1563636619-e9143da7973b?ixlib=rb-1.2.1&auto=format&fit=crop&w=200&q=80" alt="牛奶" class="w-full h-full object-cover">
                        </div>
                        <div>
                            <div class="font-medium">低脂牛奶</div>
                            <div class="text-xs text-gray-500">250ml</div>
                        </div>
                    </div>
                    <div class="text-right">
                        <div class="font-medium">120</div>
                        <div class="text-xs text-gray-500">卡路里</div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Lunch -->
        <div class="bg-white rounded-xl shadow-sm p-4 mb-4">
            <div class="flex justify-between items-center mb-3">
                <div class="flex items-center">
                    <div class="w-10 h-10 bg-orange-100 rounded-full flex items-center justify-center mr-3">
                        <i class="fas fa-cloud-sun text-orange-500"></i>
                    </div>
                    <h3 class="font-medium">午餐</h3>
                </div>
                <button class="text-blue-500">
                    <i class="fas fa-plus-circle"></i> 添加
                </button>
            </div>
            
            <div class="space-y-3">
                <div class="flex justify-between items-center py-2 border-b border-gray-100">
                    <div class="flex items-center">
                        <div class="w-12 h-12 bg-gray-100 rounded-lg overflow-hidden mr-3">
                            <img src="https://images.unsplash.com/photo-1546793665-c74683f339c1?ixlib=rb-1.2.1&auto=format&fit=crop&w=200&q=80" alt="沙拉" class="w-full h-full object-cover">
                        </div>
                        <div>
                            <div class="font-medium">鸡胸肉沙拉</div>
                            <div class="text-xs text-gray-500">1份</div>
                        </div>
                    </div>
                    <div class="text-right">
                        <div class="font-medium">320</div>
                        <div class="text-xs text-gray-500">卡路里</div>
                    </div>
                </div>
                
                <div class="flex justify-between items-center py-2">
                    <div class="flex items-center">
                        <div class="w-12 h-12 bg-gray-100 rounded-lg overflow-hidden mr-3">
                            <img src="https://images.unsplash.com/photo-1536304993881-ff6e9eefa2a6?ixlib=rb-1.2.1&auto=format&fit=crop&w=200&q=80" alt="糙米饭" class="w-full h-full object-cover">
                        </div>
                        <div>
                            <div class="font-medium">糙米饭</div>
                            <div class="text-xs text-gray-500">1小碗</div>
                        </div>
                    </div>
                    <div class="text-right">
                        <div class="font-medium">180</div>
                        <div class="text-xs text-gray-500">卡路里</div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Dinner -->
        <div class="bg-white rounded-xl shadow-sm p-4 mb-4">
            <div class="flex justify-between items-center mb-3">
                <div class="flex items-center">
                    <div class="w-10 h-10 bg-indigo-100 rounded-full flex items-center justify-center mr-3">
                        <i class="fas fa-moon text-indigo-500"></i>
                    </div>
                    <h3 class="font-medium">晚餐</h3>
                </div>
                <button class="text-blue-500">
                    <i class="fas fa-plus-circle"></i> 添加
                </button>
            </div>
            
            <div class="flex items-center justify-center py-8">
                <div class="text-center">
                    <div class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-3">
                        <i class="fas fa-utensils text-gray-400 text-xl"></i>
                    </div>
                    <p class="text-gray-500">点击添加晚餐</p>
                </div>
            </div>
        </div>
        
        <!-- Quick Add with Camera -->
        <div class="fixed bottom-24 right-4">
            <button class="w-14 h-14 bg-blue-600 rounded-full flex items-center justify-center shadow-lg">
                <i class="fas fa-camera text-white text-xl"></i>
            </button>
        </div>
    </main>

    <!-- Tab Bar -->
    <div class="tab-bar">
        <div class="text-center text-gray-400">
            <i class="fas fa-home text-xl"></i>
            <div class="text-xs mt-1">首页</div>
        </div>
        <div class="text-center text-gray-400">
            <i class="fas fa-chart-line text-xl"></i>
            <div class="text-xs mt-1">数据</div>
        </div>
        <div class="text-center text-blue-500">
            <i class="fas fa-robot text-xl"></i>
            <div class="text-xs mt-1">AI助手</div>
        </div>
        <div class="text-center text-gray-400">
            <i class="fas fa-users text-xl"></i>
            <div class="text-xs mt-1">社区</div>
        </div>
        <div class="text-center text-gray-400">
            <i class="fas fa-user text-xl"></i>
            <div class="text-xs mt-1">我的</div>
        </div>
    </div>
</body>
</html> 