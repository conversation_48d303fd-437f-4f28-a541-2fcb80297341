<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户注册 - 智能健身教练</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
            height: 100vh;
            background-color: white;
        }
        .status-bar {
            height: 44px;
            color: black;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 15px;
            font-size: 14px;
        }
        .body-type {
            width: 60px;
            height: 120px;
            background-color: #f3f4f6;
            border-radius: 10px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            cursor: pointer;
        }
        .body-type.selected {
            background-color: #dbeafe;
            border: 2px solid #3b82f6;
        }
        .body-icon {
            width: 40px;
            height: 80px;
            background-color: #e5e7eb;
            border-radius: 20px;
            margin-bottom: 8px;
        }
        .progress-bar {
            height: 4px;
            background-color: #e5e7eb;
            border-radius: 2px;
            margin: 20px 0;
        }
        .progress {
            height: 100%;
            background-color: #3b82f6;
            border-radius: 2px;
        }
    </style>
</head>
<body class="flex flex-col">
    <!-- iOS Status Bar -->
    <div class="status-bar">
        <div>9:41</div>
        <div class="flex space-x-2">
            <i class="fas fa-signal"></i>
            <i class="fas fa-wifi"></i>
            <i class="fas fa-battery-full"></i>
        </div>
    </div>

    <!-- Header -->
    <header class="p-4">
        <div class="flex justify-between items-center">
            <button>
                <i class="fas fa-arrow-left text-gray-600"></i>
            </button>
            <div>跳过</div>
        </div>
        <div class="progress-bar mt-4">
            <div class="progress" style="width: 40%"></div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="flex-1 overflow-auto p-4">
        <h1 class="text-2xl font-bold mb-1">告诉我们你的基本情况</h1>
        <p class="text-gray-500 mb-6">我们将根据您的数据定制专属训练计划</p>
        
        <!-- Basic Info Form -->
        <div class="space-y-6">
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">性别</label>
                <div class="flex space-x-4">
                    <button class="flex-1 py-3 bg-blue-500 text-white rounded-lg font-medium">男性</button>
                    <button class="flex-1 py-3 bg-white border border-gray-300 text-gray-700 rounded-lg font-medium">女性</button>
                </div>
            </div>
            
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">年龄</label>
                <div class="flex border border-gray-300 rounded-lg overflow-hidden">
                    <input type="number" placeholder="请输入" class="flex-1 p-3 outline-none" value="28">
                    <div class="px-4 flex items-center text-gray-500 bg-gray-50">岁</div>
                </div>
            </div>
            
            <div class="grid grid-cols-2 gap-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">身高</label>
                    <div class="flex border border-gray-300 rounded-lg overflow-hidden">
                        <input type="number" placeholder="请输入" class="flex-1 p-3 outline-none" value="178">
                        <div class="px-4 flex items-center text-gray-500 bg-gray-50">cm</div>
                    </div>
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">体重</label>
                    <div class="flex border border-gray-300 rounded-lg overflow-hidden">
                        <input type="number" placeholder="请输入" class="flex-1 p-3 outline-none" value="71">
                        <div class="px-4 flex items-center text-gray-500 bg-gray-50">kg</div>
                    </div>
                </div>
            </div>
            
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">身体类型（选择最接近的）</label>
                <div class="flex justify-between">
                    <div class="body-type">
                        <div class="body-icon"></div>
                        <div class="text-xs">瘦型</div>
                    </div>
                    <div class="body-type selected">
                        <div class="body-icon"></div>
                        <div class="text-xs">标准</div>
                    </div>
                    <div class="body-type">
                        <div class="body-icon"></div>
                        <div class="text-xs">肌肉型</div>
                    </div>
                    <div class="body-type">
                        <div class="body-icon"></div>
                        <div class="text-xs">偏胖</div>
                    </div>
                </div>
            </div>
            
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">健身经验</label>
                <div class="space-y-2">
                    <button class="w-full py-3 text-left px-4 bg-blue-50 border border-blue-300 text-blue-700 rounded-lg font-medium">
                        <i class="fas fa-check-circle text-blue-500 mr-2"></i> 初学者（刚开始或训练不足3个月）
                    </button>
                    <button class="w-full py-3 text-left px-4 bg-white border border-gray-300 text-gray-700 rounded-lg font-medium">
                        中级（有3-12个月的训练经验）
                    </button>
                    <button class="w-full py-3 text-left px-4 bg-white border border-gray-300 text-gray-700 rounded-lg font-medium">
                        高级（有1年以上的训练经验）
                    </button>
                </div>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="p-4">
        <button class="w-full py-3 bg-blue-600 text-white rounded-xl font-bold">
            下一步
        </button>
        <div class="text-center text-sm text-gray-500 mt-4">
            2/5 步 - 接下来设置您的健身目标
        </div>
    </footer>
</body>
</html> 