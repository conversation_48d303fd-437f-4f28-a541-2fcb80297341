<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>首页 - 智能健身教练</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
            height: 100vh;
            background-color: #f6f8fa;
        }
        .status-bar {
            height: 44px;
            background-color: white;
            color: black;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 15px;
            font-size: 14px;
        }
        .tab-bar {
            height: 83px;
            background-color: white;
            border-top: 1px solid #e5e7eb;
            display: flex;
            justify-content: space-around;
            align-items: center;
            padding-bottom: 20px;
        }
        .progress-ring {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            background: conic-gradient(#3b82f6 70%, #e5e7eb 0);
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
        }
        .progress-ring:before {
            content: "";
            position: absolute;
            width: 80px;
            height: 80px;
            border-radius: 50%;
            background: white;
        }
        .progress-text {
            position: relative;
            z-index: 1;
        }
        .calorie-ring {
            width: 200px;
            height: 200px;
            position: relative;
        }
        .calorie-ring-circle {
            width: 100%;
            height: 100%;
            border-radius: 50%;
            position: absolute;
            top: 0;
            left: 0;
        }
        .calorie-ring-background {
            background: #f3f4f6;
        }
        .calorie-ring-progress-exercise {
            background: conic-gradient(#3b82f6 0% 30%, transparent 30% 100%);
        }
        .calorie-ring-progress-diet {
            background: conic-gradient(transparent 0% 30%, #10b981 30% 70%, transparent 70% 100%);
        }
        .calorie-ring-inner {
            position: absolute;
            width: 70%;
            height: 70%;
            background: white;
            border-radius: 50%;
            top: 15%;
            left: 15%;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            box-shadow: 0 0 10px rgba(0,0,0,0.05);
        }
        .date-selector {
            position: relative;
            display: inline-block;
        }
        .date-selector input {
            padding: 8px 12px;
            border-radius: 8px;
            border: 1px solid #e5e7eb;
            background-color: white;
            font-size: 14px;
            outline: none;
            cursor: pointer;
        }
        .date-selector input::-webkit-calendar-picker-indicator {
            background: none;
            display: none;
        }
    </style>
</head>
<body class="flex flex-col">
    <!-- iOS Status Bar -->
    <div class="status-bar">
        <div>9:41</div>
        <div class="flex space-x-2">
            <i class="fas fa-signal"></i>
            <i class="fas fa-wifi"></i>
            <i class="fas fa-battery-full"></i>
        </div>
    </div>

    <!-- Header -->
    <header class="bg-white p-4 shadow-sm">
        <div class="flex justify-between items-center">
            <div>
                <h1 class="text-xl font-bold">你好，李明</h1>
                <p class="text-gray-500 text-sm">今天是健身的好日子</p>
            </div>
            <div class="date-selector">
                <input type="date" class="text-gray-600" value="2024-01-15">
                <i class="fas fa-calendar-alt absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 pointer-events-none"></i>
            </div>
            <div class="h-10 w-10 bg-gray-200 rounded-full overflow-hidden">
                <img src="https://randomuser.me/api/portraits/men/32.jpg" alt="用户头像">
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="flex-1 overflow-auto p-4">
        <!-- Summary Card -->
        <div class="bg-white rounded-xl shadow-sm p-4 mb-4">
            <div class="flex justify-between items-center mb-4">
                <h2 class="text-lg font-semibold">今日概览</h2>
                <div class="text-sm text-gray-500">目标: 2000千卡</div>
            </div>
            
            <div class="flex justify-center mb-4">
                <div class="calorie-ring">
                    <div class="calorie-ring-circle calorie-ring-background"></div>
                    <div class="calorie-ring-circle calorie-ring-progress-exercise"></div>
                    <div class="calorie-ring-circle calorie-ring-progress-diet"></div>
                    <div class="calorie-ring-inner">
                        <div class="text-3xl font-bold text-gray-800">1,320</div>
                        <div class="text-sm text-gray-500">剩余卡路里</div>
                    </div>
                </div>
            </div>
            
            <div class="grid grid-cols-2 gap-4">
                <div class="bg-blue-50 p-3 rounded-lg">
                    <div class="flex items-center justify-between mb-1">
                        <div class="text-sm text-gray-600">运动消耗</div>
                        <i class="fas fa-fire-alt text-blue-500"></i>
                    </div>
                    <div class="text-xl font-bold text-blue-600">-320</div>
                    <div class="text-xs text-blue-500">已完成30%</div>
                </div>
                
                <div class="bg-green-50 p-3 rounded-lg">
                    <div class="flex items-center justify-between mb-1">
                        <div class="text-sm text-gray-600">饮食摄入</div>
                        <i class="fas fa-utensils text-green-500"></i>
                    </div>
                    <div class="text-xl font-bold text-green-600">+680</div>
                    <div class="text-xs text-green-500">已完成40%</div>
                </div>
            </div>
        </div>

        <!-- Today's Plan -->
        <h2 class="text-lg font-semibold mb-3">今日计划</h2>
        
        <!-- Workout Plan -->
        <div class="bg-white rounded-xl shadow-sm p-4 mb-4">
            <div class="flex items-center justify-between mb-3">
                <div class="flex items-center">
                    <div class="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center mr-3">
                        <i class="fas fa-dumbbell text-blue-500"></i>
                    </div>
                    <div>
                        <h3 class="font-medium">上肢力量训练</h3>
                        <p class="text-sm text-gray-500">30分钟 · 中等强度</p>
                    </div>
                </div>
                <button class="bg-blue-500 text-white px-4 py-2 rounded-lg text-sm">
                    开始
                </button>
            </div>
            
            <div class="flex space-x-2 overflow-x-auto pb-2">
                <div class="flex-shrink-0 w-16 text-center">
                    <div class="w-16 h-16 bg-gray-100 rounded-lg mb-1 flex items-center justify-center">
                        <i class="fas fa-running text-gray-500"></i>
                    </div>
                    <span class="text-xs">热身</span>
                </div>
                <div class="flex-shrink-0 w-16 text-center">
                    <div class="w-16 h-16 bg-gray-100 rounded-lg mb-1 flex items-center justify-center">
                        <i class="fas fa-dumbbell text-gray-500"></i>
                    </div>
                    <span class="text-xs">俯卧撑</span>
                </div>
                <div class="flex-shrink-0 w-16 text-center">
                    <div class="w-16 h-16 bg-gray-100 rounded-lg mb-1 flex items-center justify-center">
                        <i class="fas fa-dumbbell text-gray-500"></i>
                    </div>
                    <span class="text-xs">引体向上</span>
                </div>
                <div class="flex-shrink-0 w-16 text-center">
                    <div class="w-16 h-16 bg-gray-100 rounded-lg mb-1 flex items-center justify-center">
                        <i class="fas fa-dumbbell text-gray-500"></i>
                    </div>
                    <span class="text-xs">哑铃卷腹</span>
                </div>
                <div class="flex-shrink-0 w-16 text-center">
                    <div class="w-16 h-16 bg-gray-100 rounded-lg mb-1 flex items-center justify-center">
                        <i class="fas fa-wind text-gray-500"></i>
                    </div>
                    <span class="text-xs">拉伸</span>
                </div>
            </div>
        </div>
        
        <!-- Diet Plan -->
        <div class="bg-white rounded-xl shadow-sm p-4 mb-4">
            <div class="flex items-center justify-between mb-3">
                <div class="flex items-center">
                    <div class="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center mr-3">
                        <i class="fas fa-utensils text-green-500"></i>
                    </div>
                    <div>
                        <h3 class="font-medium">今日膳食计划</h3>
                        <p class="text-sm text-gray-500">1800卡路里 · 均衡饮食</p>
                    </div>
                </div>
                <button class="bg-green-500 text-white px-4 py-2 rounded-lg text-sm">
                    记录
                </button>
            </div>
            
            <div class="space-y-3">
                <div class="flex justify-between items-center">
                    <div class="flex items-center">
                        <div class="w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center mr-2">
                            <i class="fas fa-sun text-yellow-500 text-xs"></i>
                        </div>
                        <span class="text-sm">早餐</span>
                    </div>
                    <div class="text-sm text-gray-500">全麦面包 + 鸡蛋 + 牛奶</div>
                </div>
                
                <div class="flex justify-between items-center">
                    <div class="flex items-center">
                        <div class="w-8 h-8 bg-orange-100 rounded-full flex items-center justify-center mr-2">
                            <i class="fas fa-cloud-sun text-orange-500 text-xs"></i>
                        </div>
                        <span class="text-sm">午餐</span>
                    </div>
                    <div class="text-sm text-gray-500">鸡胸肉沙拉 + 糙米饭</div>
                </div>
                
                <div class="flex justify-between items-center">
                    <div class="flex items-center">
                        <div class="w-8 h-8 bg-indigo-100 rounded-full flex items-center justify-center mr-2">
                            <i class="fas fa-moon text-indigo-500 text-xs"></i>
                        </div>
                        <span class="text-sm">晚餐</span>
                    </div>
                    <div class="text-sm text-gray-500">三文鱼 + 蔬菜 + 藜麦</div>
                </div>
            </div>
        </div>
        
        <!-- AI Coach -->
        <div class="bg-blue-50 rounded-xl shadow-sm p-4 border border-blue-100">
            <div class="flex">
                <div class="w-10 h-10 bg-blue-500 rounded-full flex items-center justify-center mr-3 flex-shrink-0">
                    <i class="fas fa-robot text-white"></i>
                </div>
                <div>
                    <h3 class="font-medium text-blue-700">AI教练建议</h3>
                    <p class="text-sm text-blue-600 mt-1">
                        根据您的训练记录，建议今天专注于上肢力量训练。您的胸肌和肱二头肌正在有良好的恢复，适合今天的计划。
                    </p>
                    <button class="mt-2 text-blue-700 text-sm font-medium flex items-center">
                        <span>了解更多建议</span>
                        <i class="fas fa-chevron-right ml-1 text-xs"></i>
                    </button>
                </div>
            </div>
        </div>
    </main>

    <!-- Tab Bar -->
    <div class="tab-bar">
        <div class="text-center text-gray-400">
            <i class="fas fa-home text-xl"></i>
            <div class="text-xs mt-1">首页</div>
        </div>
        <div class="text-center text-gray-400">
            <i class="fas fa-chart-line text-xl"></i>
            <div class="text-xs mt-1">数据</div>
        </div>
        <div class="text-center text-gray-400">
            <i class="fas fa-robot text-xl"></i>
            <div class="text-xs mt-1">AI助手</div>
        </div>
        <div class="text-center text-gray-400">
            <i class="fas fa-users text-xl"></i>
            <div class="text-xs mt-1">社区</div>
        </div>
        <div class="text-center text-gray-400">
            <i class="fas fa-user text-xl"></i>
            <div class="text-xs mt-1">我的</div>
        </div>
    </div>
</body>
</html>