<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>社区 - 智能健身教练</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
            height: 100vh;
            background-color: #f6f8fa;
        }
        .status-bar {
            height: 44px;
            background-color: white;
            color: black;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 15px;
            font-size: 14px;
        }
        .tab-bar {
            height: 83px;
            background-color: white;
            border-top: 1px solid #e5e7eb;
            display: flex;
            justify-content: space-around;
            align-items: center;
            padding-bottom: 20px;
        }
        .post-stats {
            display: flex;
            align-items: center;
            color: #6b7280;
            font-size: 0.875rem;
        }
        .post-stats i {
            margin-right: 4px;
        }
        .post-stats span {
            margin-right: 12px;
        }
    </style>
</head>
<body class="flex flex-col">
    <!-- iOS Status Bar -->
    <div class="status-bar">
        <div>9:41</div>
        <div class="flex space-x-2">
            <i class="fas fa-signal"></i>
            <i class="fas fa-wifi"></i>
            <i class="fas fa-battery-full"></i>
        </div>
    </div>

    <!-- Header -->
    <header class="bg-white p-4 shadow-sm">
        <div class="flex justify-between items-center">
            <h1 class="text-xl font-bold">社区</h1>
            <div class="flex space-x-4">
                <button>
                    <i class="fas fa-search text-gray-600"></i>
                </button>
                <button>
                    <i class="fas fa-bell text-gray-600"></i>
                </button>
            </div>
        </div>
    </header>

    <!-- Categories -->
    <div class="bg-white p-3 overflow-x-auto">
        <div class="flex space-x-3">
            <div class="px-4 py-1 bg-blue-500 text-white rounded-full text-sm whitespace-nowrap">
                全部
            </div>
            <div class="px-4 py-1 bg-gray-100 text-gray-600 rounded-full text-sm whitespace-nowrap">
                减脂
            </div>
            <div class="px-4 py-1 bg-gray-100 text-gray-600 rounded-full text-sm whitespace-nowrap">
                增肌
            </div>
            <div class="px-4 py-1 bg-gray-100 text-gray-600 rounded-full text-sm whitespace-nowrap">
                健康饮食
            </div>
            <div class="px-4 py-1 bg-gray-100 text-gray-600 rounded-full text-sm whitespace-nowrap">
                运动打卡
            </div>
            <div class="px-4 py-1 bg-gray-100 text-gray-600 rounded-full text-sm whitespace-nowrap">
                经验分享
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <main class="flex-1 overflow-auto">
        <!-- Post 1 -->
        <div class="bg-white p-4 mb-3">
            <div class="flex items-center mb-3">
                <div class="w-10 h-10 bg-gray-200 rounded-full overflow-hidden mr-3">
                    <img src="https://randomuser.me/api/portraits/women/44.jpg" alt="用户头像">
                </div>
                <div>
                    <div class="font-medium">张小雨</div>
                    <div class="text-xs text-gray-500">30分钟前</div>
                </div>
            </div>
            
            <p class="text-gray-800 mb-3">
                今天完成了第30天的训练挑战！感觉自己的耐力提升了很多，分享一下我的变化和一些小经验 💪
            </p>
            
            <div class="grid grid-cols-3 gap-1 mb-3">
                <div class="aspect-square bg-gray-100 rounded-md overflow-hidden">
                    <img src="https://images.unsplash.com/photo-1517838277536-f5f99be501cd?ixlib=rb-1.2.1&auto=format&fit=crop&w=300&q=80" alt="训练照片" class="w-full h-full object-cover">
                </div>
                <div class="aspect-square bg-gray-100 rounded-md overflow-hidden">
                    <img src="https://images.unsplash.com/photo-1541534741688-6078c6bfb5c5?ixlib=rb-1.2.1&auto=format&fit=crop&w=300&q=80" alt="训练照片" class="w-full h-full object-cover">
                </div>
                <div class="aspect-square bg-gray-100 rounded-md overflow-hidden">
                    <img src="https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?ixlib=rb-1.2.1&auto=format&fit=crop&w=300&q=80" alt="训练照片" class="w-full h-full object-cover">
                </div>
            </div>
            
            <div class="flex justify-between items-center">
                <div class="post-stats">
                    <i class="far fa-heart"></i>
                    <span>128</span>
                    <i class="far fa-comment"></i>
                    <span>36</span>
                </div>
                <button class="text-blue-500 text-sm">
                    查看详情
                </button>
            </div>
        </div>
        
        <!-- Post 2 -->
        <div class="bg-white p-4 mb-3">
            <div class="flex items-center mb-3">
                <div class="w-10 h-10 bg-gray-200 rounded-full overflow-hidden mr-3">
                    <img src="https://randomuser.me/api/portraits/men/32.jpg" alt="用户头像">
                </div>
                <div>
                    <div class="font-medium">李教练</div>
                    <div class="text-xs text-gray-500">2小时前 · 官方教练</div>
                </div>
            </div>
            
            <p class="text-gray-800 mb-3">
                【每日一动作】标准深蹲详解：这个动作是下肢训练的基础，正确的姿势能够有效锻炼大腿肌群和臀部肌肉，避免膝盖受伤。
            </p>
            
            <div class="w-full aspect-video bg-gray-100 rounded-md overflow-hidden mb-3">
                <img src="https://images.unsplash.com/photo-1566241440091-ec10de8db2e1?ixlib=rb-1.2.1&auto=format&fit=crop&w=600&q=80" alt="深蹲教学" class="w-full h-full object-cover">
            </div>
            
            <div class="flex justify-between items-center">
                <div class="post-stats">
                    <i class="far fa-heart"></i>
                    <span>256</span>
                    <i class="far fa-comment"></i>
                    <span>68</span>
                </div>
                <button class="text-blue-500 text-sm">
                    查看详情
                </button>
            </div>
        </div>
        
        <!-- Post 3 -->
        <div class="bg-white p-4 mb-3">
            <div class="flex items-center mb-3">
                <div class="w-10 h-10 bg-gray-200 rounded-full overflow-hidden mr-3">
                    <img src="https://randomuser.me/api/portraits/men/68.jpg" alt="用户头像">
                </div>
                <div>
                    <div class="font-medium">王建国</div>
                    <div class="text-xs text-gray-500">昨天 20:35</div>
                </div>
            </div>
            
            <p class="text-gray-800 mb-3">
                分享一个适合上班族的减脂餐食谱，低卡高蛋白，做法简单。午餐带这个，连吃一个月瘦了5公斤！
            </p>
            
            <div class="w-full rounded-md overflow-hidden mb-3">
                <img src="https://images.unsplash.com/photo-1546069901-ba9599a7e63c?ixlib=rb-1.2.1&auto=format&fit=crop&w=600&q=80" alt="健康餐食" class="w-full h-full object-cover">
            </div>
            
            <div class="flex justify-between items-center">
                <div class="post-stats">
                    <i class="far fa-heart"></i>
                    <span>412</span>
                    <i class="far fa-comment"></i>
                    <span>145</span>
                </div>
                <button class="text-blue-500 text-sm">
                    查看详情
                </button>
            </div>
        </div>
        
        <!-- Challenge Card -->
        <div class="bg-gradient-to-r from-blue-500 to-indigo-600 p-4 m-4 rounded-xl text-white">
            <div class="flex justify-between items-center mb-3">
                <h3 class="text-lg font-bold">30天腹肌挑战</h3>
                <div class="px-2 py-1 bg-white bg-opacity-20 rounded-full text-xs">
                    已有3542人参加
                </div>
            </div>
            <p class="text-sm mb-3 text-blue-100">
                每天只需15分钟，30天打造你的核心力量，适合所有水平的训练者。
            </p>
            <button class="bg-white text-blue-600 px-4 py-2 rounded-lg text-sm font-medium">
                立即参与
            </button>
        </div>
    </main>

    <!-- New Post Button -->
    <div class="fixed bottom-24 right-4">
        <button class="w-14 h-14 bg-blue-600 rounded-full flex items-center justify-center shadow-lg">
            <i class="fas fa-plus text-white text-xl"></i>
        </button>
    </div>

     <!-- Tab Bar -->
     <div class="tab-bar">
        <div class="text-center text-gray-400">
            <i class="fas fa-home text-xl"></i>
            <div class="text-xs mt-1">首页</div>
        </div>
        <div class="text-center text-gray-400">
            <i class="fas fa-chart-line text-xl"></i>
            <div class="text-xs mt-1">数据</div>
        </div>
        <div class="text-center text-blue-500">
            <i class="fas fa-robot text-xl"></i>
            <div class="text-xs mt-1">AI助手</div>
        </div>
        <div class="text-center text-gray-400">
            <i class="fas fa-users text-xl"></i>
            <div class="text-xs mt-1">社区</div>
        </div>
        <div class="text-center text-gray-400">
            <i class="fas fa-user text-xl"></i>
            <div class="text-xs mt-1">我的</div>
        </div>
    </div>
</body>
</html> 