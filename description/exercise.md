
# 训练模块API接口详细设计

## 训练计划相关接口

### 1. 训练计划管理

#### 获取训练计划列表
```
请求方法: GET
接口路径: /api/training/plans
请求参数: 
  - level: 难度级别(可选)
  - scene: 训练场景(可选)
  - is_free: 是否免费(可选)
  - page: 页码
  - page_size: 每页数量
响应格式:
  {
    "code": 200,
    "data": {
      "total": 100,
      "items": [
        {
          "plan_id": 1,
          "code": "PLAN001",
          "name": "30天燃脂计划",
          "description": "适合初学者的燃脂训练",
          "level": 1,
          "scene": "HOME",
          "weeks": 4,
          "training_frequency": 3,
          "cover_img": "https://...",
          "is_free": true
        },
        //...
      ]
    }
  }
```

#### 获取训练计划详情
```
请求方法: GET
接口路径: /api/training/plan/{plan_id}
响应格式:
  {
    "code": 200,
    "data": {
      "plan_id": 1,
      "code": "PLAN001",
      "name": "30天燃脂计划",
      "description": "适合初学者的燃脂训练",
      "tips": "记得热身...",
      "level": 1,
      "scene": "HOME",
      "weeks": 4,
      "training_frequency": 3,
      "cover_img": "https://...",
      "is_free": true,
      "courses": [
        {
          "course_id": 101,
          "name": "上肢训练",
          //...
        },
        //...
      ]
    }
  }
```

#### 创建/更新训练计划(管理员)
```
请求方法: POST/PUT
接口路径: /api/training/plan
请求体:
  {
    "code": "PLAN001",
    "name": "30天燃脂计划",
    "description": "适合初学者的燃脂训练",
    "tips": "记得热身...",
    "level": 1,
    "scene": "HOME",
    "weeks": 4,
    "training_frequency": 3,
    "is_free": true,
    "cover_img": "https://..."
  }
响应格式:
  {
    "code": 200,
    "data": {
      "plan_id": 1,
      "message": "训练计划创建/更新成功"
    }
  }
```

### 2. 训练课程相关接口

#### 获取课程列表
```
请求方法: GET
接口路径: /api/training/plan/{plan_id}/courses
响应格式:
  {
    "code": 200,
    "data": {
      "plan_id": 1,
      "plan_name": "30天燃脂计划",
      "courses": [
        {
          "course_id": 101,
          "code": "COURSE001",
          "name": "上肢训练",
          "description": "针对手臂和肩膀的训练",
          "type": "UPPER",
          "scene": "HOME",
          "movement_count": 5,
          "group_count": 3,
          "estimated_duration": 30,
          "cover_img": "https://..."
        },
        //...
      ]
    }
  }
```

#### 获取课程详情
```
请求方法: GET
接口路径: /api/training/course/{course_id}
响应格式:
  {
    "code": 200,
    "data": {
      "course_id": 101,
      "plan_id": 1,
      "code": "COURSE001",
      "name": "上肢训练",
      "description": "针对手臂和肩膀的训练",
      "type": "UPPER",
      "scene": "HOME",
      "movement_count": 5,
      "group_count": 3,
      "estimated_duration": 30,
      "cover_img": "https://...",
      "actions": [
        {
          "action_id": 201,
          "sequence": 1,
          "type": 1,
          "rest_time": 60,
          "is_superset": false,
          "exercises": [
            {
              "exercise_id": 301,
              "code": "EXE001",
              "name": "俯卧撑",
              "en_name": "Push-up",
              //...
            }
          ],
          "sets": [
            {
              "set_id": 401,
              "set_number": 1,
              "details": [
                {
                  "exercise_code": "EXE001",
                  "reps": 12,
                  "weight": 0,
                  "duration": 0
                }
              ]
            },
            //...更多组
          ]
        },
        //...更多动作
      ]
    }
  }
```

### 3. 训练动作相关接口

#### 获取动作详情
```
请求方法: GET
接口路径: /api/training/exercise/{exercise_id}
响应格式:
  {
    "code": 200,
    "data": {
      "id": 301,
      "code": "EXE001",
      "name": "俯卧撑",
      "en_name": "Push-up",
      "level": 1,
      "type": 1,
      "description": {
        "steps": ["双手撑地与肩同宽","保持身体挺直","弯曲手臂下降"],
        "tips": ["注意肘部角度","保持呼吸顺畅"]
      },
      "equipment_ids": [],
      "body_part_ids": [1, 2],
      "muscle_main_ids": [3, 4],
      "muscle_minor_ids": [5],
      "media": [
        {
          "type": "VIDEO",
          "label": "前",
          "url": "https://..."
        },
        {
          "type": "GIF",
          "label": "侧",
          "url": "https://..."
        }
      ]
    }
  }
```

### 4. 训练记录相关接口

#### 记录训练数据
```
请求方法: POST
接口路径: /api/training/record
请求体:
  {
    "user_id": 1001,
    "course_id": 101,
    "date": "2023-11-15",
    "duration": 35,
    "calories": 320,
    "comments": "今天感觉不错",
    "action_records": [
      {
        "action_id": 201,
        "set_records": [
          {
            "set_id": 401,
            "exercise_code": "EXE001",
            "reps": 10,
            "weight": 0,
            "duration": 0,
            "completed": true
          },
          //...更多组
        ]
      },
      //...更多动作
    ]
  }
响应格式:
  {
    "code": 200,
    "data": {
      "record_id": 501,
      "message": "训练记录保存成功"
    }
  }
```

#### 获取历史训练记录
```
请求方法: GET
接口路径: /api/training/history
请求参数:
  - user_id: 用户ID
  - start_date: 开始日期(可选)
  - end_date: 结束日期(可选)
  - page: 页码
  - page_size: 每页数量
响应格式:
  {
    "code": 200,
    "data": {
      "total": 50,
      "items": [
        {
          "record_id": 501,
          "date": "2023-11-15",
          "course_id": 101,
          "course_name": "上肢训练",
          "duration": 35,
          "calories": 320,
          "comments": "今天感觉不错"
        },
        //...
      ]
    }
  }
```

#### 获取训练记录详情
```
请求方法: GET
接口路径: /api/training/history/{record_id}
响应格式:
  {
    "code": 200,
    "data": {
      "record_id": 501,
      "user_id": 1001,
      "course_id": 101,
      "course_name": "上肢训练",
      "date": "2023-11-15",
      "duration": 35,
      "calories": 320,
      "comments": "今天感觉不错",
      "action_records": [
        {
          "action_id": 201,
          "exercise_name": "俯卧撑",
          "set_records": [
            {
              "set_number": 1,
              "reps": 10,
              "weight": 0,
              "completed": true
            },
            //...
          ]
        },
        //...
      ]
    }
  }
```

### 5. 推荐训练相关接口

#### 获取今日训练
```
请求方法: GET
接口路径: /api/training/today
请求参数:
  - user_id: 用户ID
响应格式:
  {
    "code": 200,
    "data": {
      "has_plan": true,
      "progress": 30, // 百分比
      "course": {
        "course_id": 101,
        "name": "上肢训练",
        "description": "针对手臂和肩膀的训练",
        //...课程详情
      }
    }
  }
```

#### 获取训练推荐
```
请求方法: GET
接口路径: /api/training/recommendations
请求参数:
  - user_id: 用户ID
  - goal: 训练目标(可选)
  - experience: 经验水平(可选)
响应格式:
  {
    "code": 200,
    "data": {
      "plans": [
        {
          "plan_id": 1,
          "name": "30天燃脂计划",
          "description": "适合初学者的燃脂训练",
          "level": 1,
          "match_score": 95, // 匹配度
          //...
        },
        //...
      ],
      "courses": [
        {
          "course_id": 101,
          "name": "上肢训练",
          "description": "针对手臂和肩膀的训练",
          "match_score": 90,
          //...
        },
        //...
      ]
    }
  }
```

## 补充接口

### 用户训练计划订阅
```
请求方法: POST
接口路径: /api/training/subscribe
请求体:
  {
    "user_id": 1001,
    "plan_id": 1,
    "start_date": "2023-11-15"
  }
响应格式:
  {
    "code": 200,
    "data": {
      "subscription_id": 601,
      "message": "训练计划订阅成功",
      "next_training": {
        "date": "2023-11-15",
        "course_id": 101,
        "course_name": "上肢训练"
      }
    }
  }
```

### 获取训练数据统计
```
请求方法: GET
接口路径: /api/training/stats
请求参数:
  - user_id: 用户ID
  - period: 统计周期(week/month/year)
响应格式:
  {
    "code": 200,
    "data": {
      "total_workouts": 45,
      "total_duration": 1350, // 分钟
      "total_calories": 12500,
      "most_trained_part": "胸部",
      "stats_by_period": [
        {
          "period": "2023-11-第1周",
          "workouts": 3,
          "duration": 90,
          "calories": 850
        },
        //...
      ]
    }
  }
```

以上API设计涵盖了训练模块的核心功能，包括计划管理、课程详情、动作信息、训练记录和推荐等，可根据实际需求进一步调整和扩展。
