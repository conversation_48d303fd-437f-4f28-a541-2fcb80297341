
    <!DOCTYPE html>
    <html lang="zh-CN">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>设置 - 智能健身教练</title>
        <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
        <style>
            body {
                font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
                height: 100vh;
                background-color: #f6f8fa;
            }
            .status-bar {
                height: 44px;
                background-color: white;
                color: black;
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 0 15px;
                font-size: 14px;
            }
            .tab-bar {
                height: 83px;
                background-color: white;
                border-top: 1px solid #e5e7eb;
                display: flex;
                justify-content: space-around;
                align-items: center;
                padding-bottom: 20px;
            }
            .setting-item {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 16px;
                background-color: white;
                border-bottom: 1px solid #f3f4f6;
            }
            .setting-item-left {
                display: flex;
                align-items: center;
            }
            .setting-icon {
                width: 36px;
                height: 36px;
                border-radius: 8px;
                display: flex;
                align-items: center;
                justify-content: center;
                margin-right: 12px;
            }
            .sub-page {
                display: none;
                position: fixed;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background: white;
                z-index: 100;
            }
            .sub-page.active {
                display: block;
            }
            .sub-page-header {
                height: 44px;
                display: flex;
                align-items: center;
                padding: 0 16px;
                border-bottom: 1px solid #e5e7eb;
            }
            .sub-page-content {
                padding: 16px;
                overflow-y: auto;
                height: calc(100vh - 44px);
            }
        </style>
    </head>
    <body class="flex flex-col">
        <!-- iOS Status Bar -->
        <div class="status-bar">
            <div>9:41</div>
            <div class="flex space-x-2">
                <i class="fas fa-signal"></i>
                <i class="fas fa-wifi"></i>
                <i class="fas fa-battery-full"></i>
            </div>
        </div>
    
        <!-- Header -->
        <header class="bg-white p-4 shadow-sm">
            <div class="flex items-center">
                <button class="mr-4">
                    <i class="fas fa-arrow-left text-gray-600"></i>
                </button>
                <h1 class="text-xl font-bold">设置</h1>
            </div>
        </header>
    
        <!-- Main Content -->
        <main class="flex-1 overflow-auto">
            <!-- User Profile -->
            <div class="bg-white p-4 mb-4">
                <div class="flex items-center">
                    <div class="w-16 h-16 bg-gray-200 rounded-full overflow-hidden mr-4">
                        <img src="https://randomuser.me/api/portraits/men/32.jpg" alt="用户头像">
                    </div>
                    <div>
                        <div class="text-lg font-bold">李明</div>
                        <div class="text-sm text-gray-500">ID: ********</div>
                    </div>
                    <button class="ml-auto px-3 py-1 border border-gray-300 rounded-lg text-sm">
                        编辑资料
                    </button>
                </div>
            </div>
            
            <!-- Account Settings -->
            <div class="mb-4">
                <div class="px-4 py-2 text-sm text-gray-500 bg-gray-50">
                    账号设置
                </div>
                
                <div class="setting-item" onclick="showSubPage('personal-info')">
                    <div class="setting-item-left">
                        <div class="setting-icon bg-blue-100">
                            <i class="fas fa-user text-blue-500"></i>
                        </div>
                        <span>个人信息</span>
                    </div>
                    <i class="fas fa-chevron-right text-gray-400"></i>
                </div>
                
                <div class="setting-item" onclick="showSubPage('account-security')">
                    <div class="setting-item-left">
                        <div class="setting-icon bg-green-100">
                            <i class="fas fa-shield-alt text-green-500"></i>
                        </div>
                        <span>账号安全</span>
                    </div>
                    <i class="fas fa-chevron-right text-gray-400"></i>
                </div>
                
                <div class="setting-item" onclick="showSubPage('notification')">
                    <div class="setting-item-left">
                        <div class="setting-icon bg-purple-100">
                            <i class="fas fa-bell text-purple-500"></i>
                        </div>
                        <span>通知设置</span>
                    </div>
                    <i class="fas fa-chevron-right text-gray-400"></i>
                </div>
                
                <div class="setting-item" onclick="showSubPage('privacy')">
                    <div class="setting-item-left">
                        <div class="setting-icon bg-red-100">
                            <i class="fas fa-lock text-red-500"></i>
                        </div>
                        <span>隐私设置</span>
                    </div>
                    <i class="fas fa-chevron-right text-gray-400"></i>
                </div>
            </div>
            
            <!-- Training Settings -->
            <div class="mb-4">
                <div class="px-4 py-2 text-sm text-gray-500 bg-gray-50">
                    训练设置
                </div>
                
                <div class="setting-item" onclick="showSubPage('training-goals')">
                    <div class="setting-item-left">
                        <div class="setting-icon bg-yellow-100">
                            <i class="fas fa-dumbbell text-yellow-500"></i>
                        </div>
                        <span>训练目标</span>
                    </div>
                    <div class="flex items-center">
                        <span class="text-sm text-gray-500 mr-2">增肌</span>
                        <i class="fas fa-chevron-right text-gray-400"></i>
                    </div>
                </div>
                
                <div class="setting-item" onclick="showSubPage('training-frequency')">
                    <div class="setting-item-left">
                        <div class="setting-icon bg-indigo-100">
                            <i class="fas fa-calendar-alt text-indigo-500"></i>
                        </div>
                        <span>训练频率</span>
                    </div>
                    <div class="flex items-center">
                        <span class="text-sm text-gray-500 mr-2">每周4次</span>
                        <i class="fas fa-chevron-right text-gray-400"></i>
                    </div>
                </div>
                
                <div class="setting-item" onclick="showSubPage('training-duration')">
                    <div class="setting-item-left">
                        <div class="setting-icon bg-blue-100">
                            <i class="fas fa-clock text-blue-500"></i>
                        </div>
                        <span>训练时长</span>
                    </div>
                    <div class="flex items-center">
                        <span class="text-sm text-gray-500 mr-2">30-45分钟</span>
                        <i class="fas fa-chevron-right text-gray-400"></i>
                    </div>
                </div>
                
                <div class="setting-item" onclick="showSubPage('nutrition-goals')">
                    <div class="setting-item-left">
                        <div class="setting-icon bg-green-100">
                            <i class="fas fa-utensils text-green-500"></i>
                        </div>
                        <span>营养目标</span>
                    </div>
                    <i class="fas fa-chevron-right text-gray-400"></i>
                </div>
            </div>
            
            <!-- About -->
            <div class="mb-4">
                <div class="px-4 py-2 text-sm text-gray-500 bg-gray-50">
                    关于
                </div>
                
                <div class="setting-item" onclick="showSubPage('about')">
                    <div class="setting-item-left">
                        <div class="setting-icon bg-gray-100">
                            <i class="fas fa-info-circle text-gray-500"></i>
                        </div>
                        <span>关于我们</span>
                    </div>
                    <i class="fas fa-chevron-right text-gray-400"></i>
                </div>
                
                <div class="setting-item" onclick="showSubPage('help')">
                    <div class="setting-item-left">
                        <div class="setting-icon bg-gray-100">
                            <i class="fas fa-question-circle text-gray-500"></i>
                        </div>
                        <span>帮助中心</span>
                    </div>
                    <i class="fas fa-chevron-right text-gray-400"></i>
                </div>
                
                <div class="setting-item" onclick="showSubPage('terms')">
                    <div class="setting-item-left">
                        <div class="setting-icon bg-gray-100">
                            <i class="fas fa-file-alt text-gray-500"></i>
                        </div>
                        <span>用户协议</span>
                    </div>
                    <i class="fas fa-chevron-right text-gray-400"></i>
                </div>
                
                <div class="setting-item" onclick="showSubPage('privacy-policy')">
                    <div class="setting-item-left">
                        <div class="setting-icon bg-gray-100">
                            <i class="fas fa-shield-alt text-gray-500"></i>
                        </div>
                        <span>隐私政策</span>
                    </div>
                    <i class="fas fa-chevron-right text-gray-400"></i>
                </div>
            </div>
            
            <!-- Logout -->
            <div class="p-4">
                <button class="w-full py-3 bg-red-50 text-red-600 rounded-lg font-medium">
                    退出登录
                </button>
            </div>
        </main>
    
        <!-- Sub Pages -->
        <!-- Personal Info Page -->
        <div class="sub-page" id="personal-info">
            <div class="sub-page-header">
                <button onclick="hideSubPage('personal-info')" class="mr-4">
                    <i class="fas fa-arrow-left text-gray-600"></i>
                </button>
                <h2 class="text-lg font-semibold">个人信息</h2>
            </div>
            <div class="sub-page-content">
                <div class="space-y-4">
                    <div class="flex items-center justify-center py-4">
                        <div class="w-24 h-24 bg-gray-200 rounded-full overflow-hidden">
                            <img src="https://randomuser.me/api/portraits/men/32.jpg" alt="用户头像">
                        </div>
                        <button class="ml-4 px-4 py-2 bg-blue-500 text-white rounded-lg">
                            更换头像
                        </button>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">用户名</label>
                        <input type="text" class="w-full px-3 py-2 border rounded-lg" value="李明">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">性别</label>
                        <select class="w-full px-3 py-2 border rounded-lg">
                            <option>男</option>
                            <option>女</option>
                        </select>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">生日</label>
                        <input type="date" class="w-full px-3 py-2 border rounded-lg">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">身高(cm)</label>
                        <input type="number" class="w-full px-3 py-2 border rounded-lg" value="175">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">体重(kg)</label>
                        <input type="number" class="w-full px-3 py-2 border rounded-lg" value="70">
                    </div>
                    <button class="w-full py-3 bg-blue-500 text-white rounded-lg font-medium mt-4">
                        保存修改
                    </button>
                </div>
            </div>
        </div>
    
        <!-- Account Security Page -->
        <div class="sub-page" id="account-security">
            <div class="sub-page-header">
                <button onclick="hideSubPage('account-security')" class="mr-4">
                    <i class="fas fa-arrow-left text-gray-600"></i>
                </button>
                <h2 class="text-lg font-semibold">账号安全</h2>
            </div>
            <div class="sub-page-content">
                <div class="space-y-4">
                    <div class="setting-item">
                        <div class="setting-item-left">
                            <div class="setting-icon bg-blue-100">
                                <i class="fas fa-key text-blue-500"></i>
                            </div>
                            <span>修改密码</span>
                        </div>
                        <i class="fas fa-chevron-right text-gray-400"></i>
                    </div>
                    <div class="setting-item">
                        <div class="setting-item-left">
                            <div class="setting-icon bg-green-100">
                                <i class="fas fa-mobile-alt text-green-500"></i>
                            </div>
                            <span>手机号绑定</span>
                        </div>
                        <div class="flex items-center">
                            <span class="text-sm text-gray-500 mr-2">138****8888</span>
                            <i class="fas fa-chevron-right text-gray-400"></i>
                        </div>
                    </div>
                    <div class="setting-item">
                        <div class="setting-item-left">
                            <div class="setting-icon bg-purple-100">
                                <i class="fas fa-envelope text-purple-500"></i>
                            </div>
                            <span>邮箱绑定</span>
                        </div>
                        <div class="flex items-center">
                            <span class="text-sm text-gray-500 mr-2">li****@example.com</span>
                            <i class="fas fa-chevron-right text-gray-400"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    
    
        <!-- Training Goals Page -->
        <div class="sub-page" id="training-goals">
            <div class="sub-page-header">
                <button onclick="hideSubPage('training-goals')" class="mr-4">
                    <i class="fas fa-arrow-left text-gray-600"></i>
                </button>
                <h2 class="text-lg font-semibold">训练目标</h2>
            </div>
            <div class="sub-page-content">
                <div class="space-y-4">
                    <div class="p-4 bg-white rounded-lg border">
                        <label class="block text-sm font-medium text-gray-700 mb-3">选择您的主要训练目标</label>
                        <div class="space-y-2">
                            <div class="flex items-center">
                                <input type="radio" name="goal" id="goal1" class="mr-3" checked>
                                <label for="goal1">增肌</label>
                            </div>
                            <div class="flex items-center">
                                <input type="radio" name="goal" id="goal2" class="mr-3">
                                <label for="goal2">减脂</label>
                            </div>
                            <div class="flex items-center">
                                <input type="radio" name="goal" id="goal3" class="mr-3">
                                <label for="goal3">保持健康</label>
                            </div>
                            <div class="flex items-center">
                                <input type="radio" name="goal" id="goal4" class="mr-3">
                                <label for="goal4">提高力量</label>
                            </div>
                        </div>
                    </div>
                    <div class="p-4 bg-white rounded-lg border">
                        <label class="block text-sm font-medium text-gray-700 mb-3">每周训练目标</label>
                        <input type="range" min="1" max="7" value="4" class="w-full">
                        <div class="flex justify-between text-sm text-gray-500 mt-2">
                            <span>1天</span>
                            <span>4天</span>
                            <span>7天</span>
                        </div>
                    </div>
                    <button class="w-full py-3 bg-blue-500 text-white rounded-lg font-medium mt-4">
                        保存设置
                    </button>
                </div>
            </div>
        </div>
    
        <!-- Nutrition Goals Page -->
        <div class="sub-page" id="nutrition-goals">
            <div class="sub-page-header">
                <button onclick="hideSubPage('nutrition-goals')" class="mr-4">
                    <i class="fas fa-arrow-left text-gray-600"></i>
                </button>
                <h2 class="text-lg font-semibold">营养目标</h2>
            </div>
            <div class="sub-page-content">
                <div class="space-y-4">
                    <div class="p-4 bg-white rounded-lg border">
                        <label class="block text-sm font-medium text-gray-700 mb-3">每日卡路里目标</label>
                        <input type="number" class="w-full px-3 py-2 border rounded-lg" value="2000">
                        <p class="text-sm text-gray-500 mt-2">根据您的身高体重，建议摄入2000卡路里/天</p>
                    </div>
                    <div class="p-4 bg-white rounded-lg border">
                        <label class="block text-sm font-medium text-gray-700 mb-3">营养素比例</label>
                        <div class="space-y-4">
                            <div>
                                <div class="flex justify-between mb-1">
                                    <span class="text-sm">蛋白质</span>
                                    <span class="text-sm text-gray-500">30%</span>
                                </div>
                                <input type="range" min="0" max="100" value="30" class="w-full">
                            </div>
                            <div>
                                <div class="flex justify-between mb-1">
                                    <span class="text-sm">碳水化合物</span>
                                    <span class="text-sm text-gray-500">50%</span>
                                </div>
                                <input type="range" min="0" max="100" value="50" class="w-full">
                            </div>
                            <div>
                                <div class="flex justify-between mb-1">
                                    <span class="text-sm">脂肪</span>
                                    <span class="text-sm text-gray-500">20%</span>
                                </div>
                                <input type="range" min="0" max="100" value="20" class="w-full">
                            </div>
                        </div>
                    </div>
                    <button class="w-full py-3 bg-blue-500 text-white rounded-lg font-medium mt-4">
                        保存设置
                    </button>
                </div>
            </div>
        </div>
    
        <!-- Help Center Page -->
        <div class="sub-page" id="help">
            <div class="sub-page-header">
                <button onclick="hideSubPage('help')" class="mr-4">
                    <i class="fas fa-arrow-left text-gray-600"></i>
                </button>
                <h2 class="text-lg font-semibold">帮助中心</h2>
            </div>
            <div class="sub-page-content">
                <div class="space-y-4">
                    <div class="setting-item">
                        <div class="setting-item-left">
                            <div class="setting-icon bg-blue-100">
                                <i class="fas fa-book text-blue-500"></i>
                            </div>
                            <span>使用指南</span>
                        </div>
                        <i class="fas fa-chevron-right text-gray-400"></i>
                    </div>
                    <div class="setting-item">
                        <div class="setting-item-left">
                            <div class="setting-icon bg-green-100">
                                <i class="fas fa-question-circle text-green-500"></i>
                            </div>
                            <span>常见问题</span>
                        </div>
                        <i class="fas fa-chevron-right text-gray-400"></i>
                    </div>
                    <div class="setting-item">
                        <div class="setting-item-left">
                            <div class="setting-icon bg-purple-100">
                                <i class="fas fa-headset text-purple-500"></i>
                            </div>
                            <span>联系客服</span>
                        </div>
                        <i class="fas fa-chevron-right text-gray-400"></i>
                    </div>
                    <div class="setting-item">
                        <div class="setting-item-left">
                            <div class="setting-icon bg-yellow-100">
                                <i class="fas fa-comment-alt text-yellow-500"></i>
                            </div>
                            <span>意见反馈</span>
                        </div>
                        <i class="fas fa-chevron-right text-gray-400"></i>
                    </div>
                </div>
            </div>
        </div>
    
        <!-- About Page -->
        <div class="sub-page" id="about">
            <div class="sub-page-header">
                <button onclick="hideSubPage('about')" class="mr-4">
                    <i class="fas fa-arrow-left text-gray-600"></i>
                </button>
                <h2 class="text-lg font-semibold">关于我们</h2>
            </div>
            <div class="sub-page-content">
                <div class="space-y-6 p-4">
                    <div class="text-center">
                        <img src="logo.png" alt="Logo" class="w-24 h-24 mx-auto mb-4">
                        <h3 class="text-xl font-bold">智能健身教练</h3>
                        <p class="text-gray-500 text-sm mt-2">版本 1.0.0</p>
                    </div>
                    <div class="text-sm text-gray-600 leading-relaxed">
                        <p>智能健身教练是一款基于AI和大数据的智能健身应用，致力于为用户提供个性化的运动和饮食计划，帮助用户实现健康目标。</p>
                    </div>
                    <div class="space-y-2">
                        <div class="flex justify-between items-center py-2">
                            <span class="text-sm">官方网站</span>
                            <a href="#" class="text-blue-500 text-sm">www.smartfitness.com</a>
                        </div>
                        <div class="flex justify-between items-center py-2">
                            <span class="text-sm">微信公众号</span>
                            <span class="text-gray-500 text-sm">智能健身教练</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    
        <!-- Tab Bar -->
        <div class="tab-bar">
            <div class="text-center text-gray-400">
                <i class="fas fa-home text-xl"></i>
                <div class="text-xs mt-1">首页</div>
            </div>
            <div class="text-center text-gray-400">
                <i class="fas fa-chart-line text-xl"></i>
                <div class="text-xs mt-1">数据</div>
            </div>
            <div class="text-center text-gray-400">
                <i class="fas fa-robot text-xl"></i>
                <div class="text-xs mt-1">AI助手</div>
            </div>
            <div class="text-center text-gray-400">
                <i class="fas fa-users text-xl"></i>
                <div class="text-xs mt-1">社区</div>
            </div>
            <div class="text-center text-blue-500">
                <i class="fas fa-user text-xl"></i>
                <div class="text-xs mt-1">我的</div>
            </div>
        </div>
    
        <script>
            function showSubPage(id) {
                document.getElementById(id).classList.add('active');
            }
            
            function hideSubPage(id) {
                document.getElementById(id).classList.remove('active');
            }
        </script>
    </body>
    </html>
