<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI助手 - 智能健身教练</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
            height: 100vh;
            background-color: #f6f8fa;
        }
        .status-bar {
            height: 44px;
            background-color: white;
            color: black;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 15px;
            font-size: 14px;
        }
        .tab-bar {
            height: 83px;
            background-color: white;
            border-top: 1px solid #e5e7eb;
            display: flex;
            justify-content: space-around;
            align-items: center;
            padding-bottom: 20px;
        }
        .chat-container {
            display: flex;
            flex-direction: column;
            height: 100%;
        }
        .message-container {
            flex: 1;
            overflow-y: auto;
            padding: 16px;
        }
        .message {
            max-width: 80%;
            margin-bottom: 12px;
            padding: 12px;
            border-radius: 18px;
        }
        .message-user {
            align-self: flex-end;
            background-color: #3b82f6;
            color: white;
            margin-left: auto;
        }
        .message-ai {
            align-self: flex-start;
            background-color: #e5e7eb;
            color: #1f2937;
        }
        .input-area {
            padding: 12px;
            background-color: white;
            border-top: 1px solid #e5e7eb;
        }
        .input-container {
            display: flex;
            align-items: center;
            background-color: #f3f4f6;
            border-radius: 24px;
            padding: 8px 16px;
        }
        .ai-bubble {
            display: flex;
            align-items: center;
        }
        .ai-avatar {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background-color: #3b82f6;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 8px;
        }
        .quick-actions {
            display: flex;
            overflow-x: auto;
            padding: 8px 0;
            margin-bottom: 8px;
        }
        .quick-action {
            flex-shrink: 0;
            padding: 8px 16px;
            background-color: white;
            border: 1px solid #e5e7eb;
            border-radius: 16px;
            margin-right: 8px;
            font-size: 13px;
            white-space: nowrap;
        }
    </style>
</head>
<body class="flex flex-col">
    <!-- iOS Status Bar -->
    <div class="status-bar">
        <div>9:41</div>
        <div class="flex space-x-2">
            <i class="fas fa-signal"></i>
            <i class="fas fa-wifi"></i>
            <i class="fas fa-battery-full"></i>
        </div>
    </div>

    <!-- Header -->
    <header class="bg-white p-4 shadow-sm">
        <div class="flex justify-between items-center">
            <h1 class="text-xl font-bold">AI健身助手</h1>
            <button class="w-8 h-8 flex items-center justify-center rounded-full bg-gray-100">
                <i class="fas fa-ellipsis-h text-gray-600"></i>
            </button>
        </div>
    </header>

    <!-- Main Content -->
    <main class="flex-1 overflow-hidden flex flex-col">
        <div class="quick-actions bg-gray-50 px-4">
            <button class="quick-action">
                <i class="fas fa-utensils text-orange-500 mr-1"></i> 分析食物营养
            </button>
            <button class="quick-action">
                <i class="fas fa-dumbbell text-blue-500 mr-1"></i> 识别健身器械
            </button>
            <button class="quick-action">
                <i class="fas fa-heartbeat text-red-500 mr-1"></i> 健康饮食建议
            </button>
            <button class="quick-action">
                <i class="fas fa-running text-green-500 mr-1"></i> 运动计划定制
            </button>
            <button class="quick-action">
                <i class="fas fa-weight text-purple-500 mr-1"></i> 体重管理咨询
            </button>
        </div>
        
        <div class="message-container">
            <!-- AI Welcome Message -->
            <div class="flex mb-4">
                <div class="ai-avatar">
                    <i class="fas fa-robot text-white"></i>
                </div>
                <div class="message message-ai">
                    <p>你好，我是你的AI健身助手！你可以向我提问关于健身、饮食和健康的任何问题，或者上传照片让我识别食物成分和健身器械。</p>
                </div>
            </div>
            
            <!-- User Message -->
            <div class="flex justify-end mb-4">
                <div class="message message-user">
                    <p>我想了解一下HIIT训练的好处</p>
                </div>
            </div>
            
            <!-- AI Response -->
            <div class="flex mb-4">
                <div class="ai-avatar">
                    <i class="fas fa-robot text-white"></i>
                </div>
                <div class="message message-ai">
                    <p>HIIT（高强度间歇训练）的主要好处包括：</p>
                    <ul class="list-disc pl-5 mt-2">
                        <li>提高有氧和无氧能力</li>
                        <li>提升新陈代谢，训练后继续燃烧卡路里</li>
                        <li>相比传统有氧训练，节省时间</li>
                        <li>不需要特殊设备，在家即可完成</li>
                        <li>增强心肺功能和胰岛素敏感性</li>
                    </ul>
                    <p class="mt-2">你需要我为你制定一个初学者友好的HIIT计划吗？</p>
                </div>
            </div>
            
            <!-- User Photo Message -->
            <div class="flex justify-end mb-4">
                <div class="message message-user">
                    <div class="mb-2">这个食物的营养成分如何？适合健身后食用吗？</div>
                    <div class="w-48 h-48 bg-blue-200 rounded-md overflow-hidden">
                        <img src="https://images.unsplash.com/photo-1546069901-ba9599a7e63c?ixlib=rb-1.2.1&auto=format&fit=crop&w=300&q=80" alt="健康沙拉" class="w-full h-full object-cover">
                    </div>
                </div>
            </div>
            
            <!-- AI Response to Photo -->
            <div class="flex mb-4">
                <div class="ai-avatar">
                    <i class="fas fa-robot text-white"></i>
                </div>
                <div class="message message-ai">
                    <p>我分析出这是一份健康沙拉，包含以下营养成分：</p>
                    <ul class="mt-2">
                        <li><span class="font-medium">热量：</span>约250-300卡路里</li>
                        <li><span class="font-medium">蛋白质：</span>约15克</li>
                        <li><span class="font-medium">碳水化合物：</span>约25克</li>
                        <li><span class="font-medium">脂肪：</span>约15克（主要是健康脂肪）</li>
                        <li><span class="font-medium">纤维素：</span>约8克</li>
                    </ul>
                    <p class="mt-2">这是一个非常适合健身后食用的餐食，因为它：</p>
                    <ul class="list-disc pl-5 mt-1">
                        <li>含有优质蛋白质帮助肌肉恢复</li>
                        <li>提供适量碳水补充训练消耗的能量</li>
                        <li>含有丰富维生素和矿物质</li>
                    </ul>
                    <p class="mt-2">建议在训练后30-60分钟内食用以促进恢复。</p>
                </div>
            </div>
        </div>
        
        <!-- Input Area -->
        <div class="input-area">
            <div class="flex mb-2">
                <button class="w-10 h-10 rounded-full bg-gray-100 flex items-center justify-center mr-2">
                    <i class="fas fa-camera text-gray-600"></i>
                </button>
                <button class="w-10 h-10 rounded-full bg-gray-100 flex items-center justify-center mr-2">
                    <i class="fas fa-microphone text-gray-600"></i>
                </button>
                <button class="w-10 h-10 rounded-full bg-gray-100 flex items-center justify-center">
                    <i class="fas fa-image text-gray-600"></i>
                </button>
            </div>
            <div class="input-container">
                <input type="text" placeholder="问我任何健身和饮食问题..." class="bg-transparent flex-1 outline-none">
                <button class="ml-2 w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                    <i class="fas fa-paper-plane text-white text-sm"></i>
                </button>
            </div>
        </div>
    </main>

    <!-- Tab Bar -->
    <div class="tab-bar">
        <div class="text-center text-gray-400">
            <i class="fas fa-home text-xl"></i>
            <div class="text-xs mt-1">首页</div>
        </div>
        <div class="text-center text-gray-400">
            <i class="fas fa-chart-line text-xl"></i>
            <div class="text-xs mt-1">数据</div>
        </div>
        <div class="text-center text-blue-500">
            <i class="fas fa-robot text-xl"></i>
            <div class="text-xs mt-1">AI助手</div>
        </div>
        <div class="text-center text-gray-400">
            <i class="fas fa-users text-xl"></i>
            <div class="text-xs mt-1">社区</div>
        </div>
        <div class="text-center text-gray-400">
            <i class="fas fa-user text-xl"></i>
            <div class="text-xs mt-1">我的</div>
        </div>
    </div>
</body>
</html> 