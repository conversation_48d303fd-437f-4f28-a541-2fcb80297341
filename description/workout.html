<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>训练详情 - 智能健身教练</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
            height: 100vh;
            background-color: #f6f8fa;
        }
        .status-bar {
            height: 44px;
            background-color: white;
            color: black;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 15px;
            font-size: 14px;
        }
        .tab-bar {
            height: 83px;
            background-color: white;
            border-top: 1px solid #e5e7eb;
            display: flex;
            justify-content: space-around;
            align-items: center;
            padding-bottom: 20px;
        }
        .exercise-tag {
            display: inline-block;
            border-radius: 12px;
            font-size: 12px;
            padding: 2px 8px;
        }
    </style>
</head>
<body class="flex flex-col">
    <!-- iOS Status Bar -->
    <div class="status-bar">
        <div>9:41</div>
        <div class="flex space-x-2">
            <i class="fas fa-signal"></i>
            <i class="fas fa-wifi"></i>
            <i class="fas fa-battery-full"></i>
        </div>
    </div>

    <!-- Header -->
    <header class="bg-white p-4 shadow-sm">
        <div class="flex items-center">
            <button class="mr-4">
                <i class="fas fa-arrow-left text-gray-600"></i>
            </button>
            <h1 class="text-xl font-bold">上肢力量训练</h1>
        </div>
    </header>

    <!-- Main Content -->
    <main class="flex-1 overflow-auto p-4">
        <!-- Summary Card -->
        <div class="bg-white rounded-xl shadow-sm p-4 mb-4">
            <div class="flex justify-between items-center mb-3">
                <div>
                    <h2 class="text-lg font-semibold">训练概览</h2>
                    <p class="text-sm text-gray-500">30分钟 · 5个动作 · 中等强度</p>
                </div>
                <div class="bg-blue-100 text-blue-700 rounded-full px-3 py-1 text-sm font-medium">
                    上肢
                </div>
            </div>
            
            <div class="flex space-x-4">
                <div class="flex-1 bg-gray-100 rounded-lg p-3 text-center">
                    <div class="text-blue-500 text-xl font-bold">320</div>
                    <div class="text-xs text-gray-500">预计消耗卡路里</div>
                </div>
                <div class="flex-1 bg-gray-100 rounded-lg p-3 text-center">
                    <div class="text-green-500 text-xl font-bold">4.6</div>
                    <div class="text-xs text-gray-500">用户平均评分</div>
                </div>
                <div class="flex-1 bg-gray-100 rounded-lg p-3 text-center">
                    <div class="text-purple-500 text-xl font-bold">92%</div>
                    <div class="text-xs text-gray-500">适合新手</div>
                </div>
            </div>
        </div>

        <!-- Exercise List -->
        <h2 class="text-lg font-semibold mb-3">训练动作</h2>
        
        <!-- Exercise 1 -->
        <div class="bg-white rounded-xl shadow-sm p-4 mb-4">
            <div class="flex mb-3">
                <div class="w-20 h-20 bg-gray-100 rounded-lg overflow-hidden mr-3 flex-shrink-0">
                    <img src="https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?ixlib=rb-1.2.1&auto=format&fit=crop&w=300&q=80" alt="热身" class="w-full h-full object-cover">
                </div>
                <div>
                    <div class="flex items-center justify-between">
                        <h3 class="font-medium">1. 动态热身</h3>
                        <span class="text-gray-500 text-sm">5分钟</span>
                    </div>
                    <p class="text-sm text-gray-600 mt-1">
                        包含手臂摆动、肩部旋转、体侧拉伸等动作
                    </p>
                    <div class="mt-2">
                        <span class="exercise-tag bg-yellow-100 text-yellow-700">热身</span>
                        <span class="exercise-tag bg-blue-100 text-blue-700 ml-2">关节活动度</span>
                    </div>
                </div>
            </div>
            <button class="w-full py-2 bg-blue-500 text-white rounded-lg font-medium">
                查看详细指导
            </button>
        </div>
        
        <!-- Exercise 2 -->
        <div class="bg-white rounded-xl shadow-sm p-4 mb-4">
            <div class="flex mb-3">
                <div class="w-20 h-20 bg-gray-100 rounded-lg overflow-hidden mr-3 flex-shrink-0">
                    <img src="https://images.unsplash.com/photo-1598971639058-fab055101edc?ixlib=rb-1.2.1&auto=format&fit=crop&w=300&q=80" alt="俯卧撑" class="w-full h-full object-cover">
                </div>
                <div>
                    <div class="flex items-center justify-between">
                        <h3 class="font-medium">2. 标准俯卧撑</h3>
                        <span class="text-gray-500 text-sm">3组x10次</span>
                    </div>
                    <p class="text-sm text-gray-600 mt-1">
                        锻炼胸大肌、肱三头肌和三角肌前束
                    </p>
                    <div class="mt-2">
                        <span class="exercise-tag bg-red-100 text-red-700">胸部</span>
                        <span class="exercise-tag bg-green-100 text-green-700 ml-2">肱三头肌</span>
                    </div>
                </div>
            </div>
            <button class="w-full py-2 bg-blue-500 text-white rounded-lg font-medium">
                查看详细指导
            </button>
        </div>
        
        <!-- Exercise 3 -->
        <div class="bg-white rounded-xl shadow-sm p-4 mb-4">
            <div class="flex mb-3">
                <div class="w-20 h-20 bg-gray-100 rounded-lg overflow-hidden mr-3 flex-shrink-0">
                    <img src="https://images.unsplash.com/photo-1599058917765-a780eda07a3e?ixlib=rb-1.2.1&auto=format&fit=crop&w=300&q=80" alt="哑铃弯举" class="w-full h-full object-cover">
                </div>
                <div>
                    <div class="flex items-center justify-between">
                        <h3 class="font-medium">3. 哑铃弯举</h3>
                        <span class="text-gray-500 text-sm">3组x12次</span>
                    </div>
                    <p class="text-sm text-gray-600 mt-1">
                        锻炼肱二头肌，提升手臂线条
                    </p>
                    <div class="mt-2">
                        <span class="exercise-tag bg-indigo-100 text-indigo-700">肱二头肌</span>
                        <span class="exercise-tag bg-purple-100 text-purple-700 ml-2">前臂</span>
                    </div>
                </div>
            </div>
            <button class="w-full py-2 bg-blue-500 text-white rounded-lg font-medium">
                查看详细指导
            </button>
        </div>
        
        <!-- Alternative Exercises -->
        <div class="border border-dashed border-blue-400 rounded-xl p-4 mb-4 bg-blue-50">
            <div class="flex items-start">
                <div class="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center mr-3 flex-shrink-0">
                    <i class="fas fa-lightbulb text-blue-500"></i>
                </div>
                <div>
                    <h3 class="font-medium text-blue-700">无器械替代方案</h3>
                    <p class="text-sm text-blue-600 mt-1">
                        如果没有哑铃，您可以使用装满水的瓶子或者家中的重物替代。或者可以执行徒手动作如三头肌撑墙和俯卧撑变式。
                    </p>
                    <button class="mt-2 text-blue-700 text-sm font-medium flex items-center">
                        <span>查看替代动作</span>
                        <i class="fas fa-chevron-right ml-1 text-xs"></i>
                    </button>
                </div>
            </div>
        </div>
        
        <!-- AI Coach Tips -->
        <div class="bg-white rounded-xl shadow-sm p-4 mb-4">
            <div class="flex items-center mb-3">
                <div class="w-10 h-10 bg-purple-100 rounded-full flex items-center justify-center mr-3">
                    <i class="fas fa-robot text-purple-500"></i>
                </div>
                <h3 class="font-medium">AI教练提示</h3>
            </div>
            
            <div class="space-y-3">
                <div class="flex">
                    <div class="w-6 h-6 bg-green-100 rounded-full flex items-center justify-center mr-2 flex-shrink-0">
                        <i class="fas fa-check text-green-500 text-xs"></i>
                    </div>
                    <p class="text-sm text-gray-600">
                        保持正确的呼吸节奏，俯卧撑下降时吸气，上升时呼气
                    </p>
                </div>
                
                <div class="flex">
                    <div class="w-6 h-6 bg-green-100 rounded-full flex items-center justify-center mr-2 flex-shrink-0">
                        <i class="fas fa-check text-green-500 text-xs"></i>
                    </div>
                    <p class="text-sm text-gray-600">
                        哑铃弯举时避免耸肩，保持肘部固定位置
                    </p>
                </div>
                
                <div class="flex">
                    <div class="w-6 h-6 bg-green-100 rounded-full flex items-center justify-center mr-2 flex-shrink-0">
                        <i class="fas fa-check text-green-500 text-xs"></i>
                    </div>
                    <p class="text-sm text-gray-600">
                        组间休息60-90秒，不要过长导致身体冷却
                    </p>
                </div>
            </div>
        </div>
        
        <!-- Start Button -->
        <button class="w-full py-3 bg-blue-600 text-white rounded-xl font-bold shadow-lg mb-6">
            开始训练
        </button>
    </main>

    <!-- Tab Bar -->
    <div class="tab-bar">
        <div class="text-center text-blue-500">
            <i class="fas fa-home text-xl"></i>
            <div class="text-xs mt-1">首页</div>
        </div>
        <div class="text-center text-gray-400">
            <i class="fas fa-chart-line text-xl"></i>
            <div class="text-xs mt-1">数据</div>
        </div>
        <div class="text-center text-gray-400">
            <i class="fas fa-users text-xl"></i>
            <div class="text-xs mt-1">社区</div>
        </div>
        <div class="text-center text-gray-400">
            <i class="fas fa-user text-xl"></i>
            <div class="text-xs mt-1">我的</div>
        </div>
    </div>
</body>
</html>