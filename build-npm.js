const path = require('path')
const ci = require('miniprogram-ci')
const { spawnSync } = require('child_process')
const fs = require('fs')

/**
 * 构建npm步骤
 */
async function buildNpm() {
  console.log('开始构建npm包...')
  
  // 1. 创建临时目录
  const tempDir = path.resolve(__dirname, './.temp')
  if (!fs.existsSync(tempDir)) {
    fs.mkdirSync(tempDir)
  }
  
  try {
    // 2. 初始化项目配置
    const projectConfig = {
      appid: 'wxidhere', // 替换为您的小程序AppID
      type: 'miniProgram',
      projectPath: path.resolve(__dirname),
      privateKeyPath: path.resolve(__dirname, './private.key'), // 这个在CI环境需要，本地可能不需要
      ignores: ['node_modules/**/*'],
    }

    console.log('项目配置:', projectConfig)
    
    // 3. 创建项目实例
    try {
      const project = new ci.Project(projectConfig)
      
      // 4. 调用构建npm
      console.log('开始调用buildNpm...')
      const result = await ci.packNpm(project, {
        reporter: (infos) => {
          console.log(infos)
        }
      })
      
      console.log('构建npm成功:', result)
    } catch (e) {
      console.error('创建项目或构建过程失败:', e)
      
      // 5. 如果CI构建失败，可以尝试使用命令行方式
      console.log('尝试使用命令行方式构建...')
      const wxCliPath = '/Applications/wechatwebdevtools.app/Contents/MacOS/cli' // macOS路径
      
      // 检查是否存在微信开发者工具CLI
      if (fs.existsSync(wxCliPath)) {
        console.log('使用微信开发者工具CLI构建...')
        const result = spawnSync(wxCliPath, ['build-npm', '--project', __dirname])
        
        if (result.error) {
          console.error('CLI构建失败:', result.error)
        } else {
          console.log('CLI构建输出:', result.stdout.toString())
          console.log('CLI构建错误:', result.stderr.toString())
          console.log('CLI构建完成，返回码:', result.status)
        }
      } else {
        console.error('未找到微信开发者工具CLI，请手动在IDE中构建npm')
      }
    }
  } catch (error) {
    console.error('构建过程中出错:', error)
  }
}

// 执行构建
buildNpm() 